{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run",
            "type": "debugpy",
            "request": "launch",
            "program": "/fs-computility/llmeval/zhudongsheng/program/gorilla/berkeley-function-call-leaderboard/openfunctions_evaluation.py",
            "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/BFCL/bin/python",
            "args": [
                "--model", "gemini-2.5-pro-preview-05-06-FC",
                "--base-url", "https://generativelanguage.googleapis.com/v1beta/openai/",
                "--num-threads", "1",
                "--skip-server-setup"
            ],
            "console": "integratedTerminal",
            "env": {
                "HTTPS_PROXY": "http://closeai-proxy.pjlab.org.cn:23128", 
                "HTTP_PROXY": "http://closeai-proxy.pjlab.org.cn:23128", 
                "https_proxy": "http://closeai-proxy.pjlab.org.cn:23128",
                "http_proxy": "http://closeai-proxy.pjlab.org.cn:23128",
            }
        }
    ]
}