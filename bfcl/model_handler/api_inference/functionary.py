from bfcl.model_handler.api_inference.openai import OpenA<PERSON><PERSON><PERSON>ler
from bfcl.model_handler.model_style import ModelStyle
from openai import OpenAI

# For setup instructions, please refer to https://github.com/MeetKai/functionary for setup details.
class FunctionaryHandler(OpenAIHandler):
    def __init__(self, model_name, temperature, top_k=-1, top_p=1.0, base_url=None) -> None:
        super().__init__(model_name, temperature, top_k, top_p, base_url)
        self.model_style = ModelStyle.OpenAI

        # Use custom base_url if provided, otherwise use default Functionary endpoint
        functionary_base_url = base_url if base_url else "http://localhost:8000/v1"
        self.client = OpenAI(base_url=functionary_base_url, api_key="functionary")
