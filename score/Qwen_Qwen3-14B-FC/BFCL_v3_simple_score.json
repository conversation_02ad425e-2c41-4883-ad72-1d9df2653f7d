{"accuracy": 0.95, "correct_count": 380, "total_count": 400}
{"id": "simple_13", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": [{"calculate_area_under_curve": "{\"function\": \"lambda x: x**2\", \"interval\": [1, 3], \"method\": \"trapezoidal\"}"}], "model_result_decoded": [{"calculate_area_under_curve": {"function": "lambda x: x**2", "interval": [1, 3], "method": "trapezoidal"}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_51", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'isothermal': False. Expected one of ['', True]."], "error_type": "value_error:others", "prompt": {"id": "simple_51", "question": [[{"role": "user", "content": "Calculate the entropy change for a certain process given an initial temperature of 300K, a final temperature of 400K, and a heat capacity of 5J/K."}]], "function": [{"name": "calculate_entropy_change", "description": "Calculate the entropy change for an isothermal and reversible process.", "parameters": {"type": "dict", "properties": {"initial_temp": {"type": "integer", "description": "The initial temperature in Kelvin."}, "final_temp": {"type": "integer", "description": "The final temperature in Kelvin."}, "heat_capacity": {"type": "integer", "description": "The heat capacity in J/K."}, "isothermal": {"type": "boolean", "description": "Whether the process is isothermal. Default is True."}}, "required": ["initial_temp", "final_temp", "heat_capacity"]}}]}, "model_result_raw": [{"calculate_entropy_change": "{\"initial_temp\": 300, \"final_temp\": 400, \"heat_capacity\": 5, \"isothermal\": false}"}], "model_result_decoded": [{"calculate_entropy_change": {"initial_temp": 300, "final_temp": 400, "heat_capacity": 5, "isothermal": false}}], "possible_answer": [{"calculate_entropy_change": {"initial_temp": [300], "final_temp": [400], "heat_capacity": [5], "isothermal": ["", true]}}]}
{"id": "simple_55", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'cell_type': 'human cell'. Expected one of ['human']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_55", "question": [[{"role": "user", "content": "Find me detailed information about the structure of human cell"}]], "function": [{"name": "biology.get_cell_info", "description": "Retrieve information about the structure and functioning of a specified type of cell", "parameters": {"type": "dict", "properties": {"cell_type": {"type": "string", "description": "Type of cell you want information about"}, "detailed": {"type": "boolean", "description": "Indicate if you want a detailed description of the cell", "default": "false"}}, "required": ["cell_type"]}}]}, "model_result_raw": [{"biology_get_cell_info": "{\"cell_type\": \"human cell\", \"detailed\": true}"}], "model_result_decoded": [{"biology_get_cell_info": {"cell_type": "human cell", "detailed": true}}], "possible_answer": [{"biology.get_cell_info": {"cell_type": ["human"], "detailed": [true]}}]}
{"id": "simple_82", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers'. Expected outer type array with inner type <class 'float'>. Parameter value: [12, 15, 18, 20, 21, 26, 30]."], "error_type": "type_error:nested", "prompt": {"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}, "model_result_raw": [{"calculate_average": "{\"numbers\": [12, 15, 18, 20, 21, 26, 30]}"}], "model_result_decoded": [{"calculate_average": {"numbers": [12, 15, 18, 20, 21, 26, 30]}}], "possible_answer": [{"calculate_average": {"numbers": [[12.0, 15.0, 18.0, 20.0, 21.0, 26.0, 30.0]]}}]}
{"id": "simple_87", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'list'. Expected outer type array with inner type <class 'float'>. Parameter value: [5, 3, 4, 1, 2]."], "error_type": "type_error:nested", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": [{"array_sort": "{\"list\": [5, 3, 4, 1, 2], \"order\": \"ascending\"}"}], "model_result_decoded": [{"array_sort": {"list": [5, 3, 4, 1, 2], "order": "ascending"}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_103", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'polynomial'. Expected outer type array with inner type <class 'float'>. Parameter value: [3, 2, -4]."], "error_type": "type_error:nested", "prompt": {"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}, "model_result_raw": [{"mathematics_calculate_area_under_curve": "{\"polynomial\": [3, 2, -4], \"limits\": [-1, 2]}"}], "model_result_decoded": [{"mathematics_calculate_area_under_curve": {"polynomial": [3, 2, -4], "limits": [-1, 2]}}], "possible_answer": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3.0, 2.0, -4.0]], "limits": [[-1.0, 2.0]]}}]}
{"id": "simple_130", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'years': [0, 1, 2, 3, 4, 5]. Expected one of ['', []]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_130", "question": [[{"role": "user", "content": "What's the NPV (Net Present Value) of a series of cash flows: [-50000, 10000, 15000, 20000, 25000, 30000] discounted at 8% annually?"}]], "function": [{"name": "finance_calculator.npv", "description": "Calculate the Net Present Value (NPV) for a series of cash flows discounted at a certain interest rate.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "integer"}, "description": "A list of cash flows."}, "discount_rate": {"type": "float", "description": "The annual interest rate used to discount the cash flows."}, "years": {"type": "array", "items": {"type": "integer"}, "description": "A list of years when the cash flow occurs. Default is empty array."}}, "required": ["cash_flows", "discount_rate"]}}]}, "model_result_raw": [{"finance_calculator_npv": "{\"cash_flows\": [-50000, 10000, 15000, 20000, 25000, 30000], \"discount_rate\": 0.08, \"years\": [0, 1, 2, 3, 4, 5]}"}], "model_result_decoded": [{"finance_calculator_npv": {"cash_flows": [-50000, 10000, 15000, 20000, 25000, 30000], "discount_rate": 0.08, "years": [0, 1, 2, 3, 4, 5]}}], "possible_answer": [{"finance_calculator.npv": {"cash_flows": [[-50000, 10000, 15000, 20000, 25000, 30000]], "discount_rate": [0.08], "years": ["", []]}}]}
{"id": "simple_139", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'yearly_yield': 0.05. Expected one of [5.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_139", "question": [[{"role": "user", "content": "What is the estimated return on a mutual fund, given that it has a yearly yield of 5%, an investment amount of $2000 and a time period of 3 years?"}]], "function": [{"name": "estimate_mutual_fund_return", "description": "Calculate the estimated return on a mutual fund given the yearly yield, the investment amount and the time period.", "parameters": {"type": "dict", "properties": {"yearly_yield": {"type": "float", "description": "The yearly yield of the mutual fund as a percentage."}, "investment_amount": {"type": "integer", "description": "The initial investment amount in the mutual fund."}, "years": {"type": "integer", "description": "The time period for which the investment is made in years."}}, "required": ["yearly_yield", "investment_amount", "years"]}}]}, "model_result_raw": [{"estimate_mutual_fund_return": "{\"yearly_yield\": 0.05, \"investment_amount\": 2000, \"years\": 3}"}], "model_result_decoded": [{"estimate_mutual_fund_return": {"yearly_yield": 0.05, "investment_amount": 2000, "years": 3}}], "possible_answer": [{"estimate_mutual_fund_return": {"yearly_yield": [5.0], "investment_amount": [2000], "years": [3]}}]}
{"id": "simple_202", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_202", "question": [[{"role": "user", "content": "How many greenhouse gas emissions would I save if I switched to renewable energy sources for 3 months in California?"}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'Texas'."}}, "required": ["energy_type", "usage_duration"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"calculate_emission_savings": {"energy_type": ["renewable"], "usage_duration": [3], "region": ["California", "CA"]}}]}
{"id": "simple_203", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'detail': False. Expected one of [True]."], "error_type": "value_error:others", "prompt": {"id": "simple_203", "question": [[{"role": "user", "content": "Can you find me the latest information about air quality index and pollution data for Chicago?"}]], "function": [{"name": "get_air_quality", "description": "Retrieve real-time air quality and pollution data for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality data for."}, "detail": {"type": "boolean", "description": "If true, additional data like PM2.5, PM10, ozone levels, and pollution sources will be retrieved. Default is false."}}, "required": ["location"]}}]}, "model_result_raw": [{"get_air_quality": "{\"location\": \"Chicago\", \"detail\": false}"}], "model_result_decoded": [{"get_air_quality": {"location": "Chicago", "detail": false}}], "possible_answer": [{"get_air_quality": {"location": ["Chicago"], "detail": [true]}}]}
{"id": "simple_224", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'sub_topics': ['behavior', 'group dynamics']. Expected one of [['behaviour', 'group dynamics']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_224", "question": [[{"role": "user", "content": "Find the most followed person on twitter who tweets about psychology related to behaviour and group dynamics."}]], "function": [{"name": "social_media_analytics.most_followed", "description": "Find the most followed Twitter user related to certain topics.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The main topic of interest."}, "sub_topics": {"type": "array", "items": {"type": "string"}, "description": "Sub-topics related to main topic. Default is empty."}, "region": {"type": "string", "description": "Region of interest for twitter search. Default is 'all'."}}, "required": ["topic"]}}]}, "model_result_raw": [{"social_media_analytics_most_followed": "{\"topic\": \"psychology\", \"sub_topics\": [\"behavior\", \"group dynamics\"], \"region\": \"all\"}"}], "model_result_decoded": [{"social_media_analytics_most_followed": {"topic": "psychology", "sub_topics": ["behavior", "group dynamics"], "region": "all"}}], "possible_answer": [{"social_media_analytics.most_followed": {"topic": ["psychology"], "sub_topics": [["behaviour", "group dynamics"]], "region": ["", "all"]}}]}
{"id": "simple_252", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'century': 15. Expected one of [16]."], "error_type": "value_error:others", "prompt": {"id": "simple_252", "question": [[{"role": "user", "content": "Find ten major historical events related to Christianity in the 16th century sort by importance."}]], "function": [{"name": "get_religion_history", "description": "Retrieves significant religious events, including the details of the event, its historical context, and its impacts.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "Name of the religion to be queried."}, "century": {"type": "integer", "description": "The century in which the event(s) took place."}, "sort_by": {"type": "string", "enum": ["importance", "chronological"], "default": "chronological", "description": "Order of sorting the events. Default is chronological."}, "count": {"type": "integer", "default": 5, "description": "Number of events to return. Default is 5."}}, "required": ["religion", "century"]}}]}, "model_result_raw": [{"get_religion_history": "{\"religion\": \"Christianity\", \"century\": 15, \"sort_by\": \"importance\", \"count\": 10}"}], "model_result_decoded": [{"get_religion_history": {"religion": "Christianity", "century": 15, "sort_by": "importance", "count": 10}}], "possible_answer": [{"get_religion_history": {"religion": ["Christianity"], "century": [16], "sort_by": ["importance"], "count": [10]}}]}
{"id": "simple_267", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'month': 'upcoming'. Expected one of ['']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_267", "question": [[{"role": "user", "content": "Find the top rated modern sculpture exhibition happening in New York in the upcoming month."}]], "function": [{"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the exhibition is held, e.g., New York City, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events if not specified."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'low'"}}, "required": ["location", "art_form"]}}]}, "model_result_raw": [{"find_exhibition": "{\"location\": \"New York City, NY\", \"art_form\": \"sculpture\", \"user_ratings\": \"high\", \"month\": \"upcoming\"}"}], "model_result_decoded": [{"find_exhibition": {"location": "New York City, NY", "art_form": "sculpture", "user_ratings": "high", "month": "upcoming"}}], "possible_answer": [{"find_exhibition": {"location": ["New York City, NY"], "art_form": ["sculpture", "modern sculpture"], "month": [""], "user_ratings": ["high"]}}]}
{"id": "simple_284", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'date': '2023-10'. Expected one of ['next month', '2023-06-01', '06/01/2023', 'Jun.1,2023', 'June 2023']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_284", "question": [[{"role": "user", "content": "Get information about the pop concerts in New York for next month."}]], "function": [{"name": "concert_info.get", "description": "Retrieve information about concerts based on specific genre, location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the concert will take place."}, "date": {"type": "string", "description": "Time frame to get the concert for."}, "genre": {"type": "string", "description": "Genre of the concert.", "enum": ["Pop", "Rock", "Country", "Classical", "Electronic", "Hip-Hop"]}}, "required": ["location", "date", "genre"]}}]}, "model_result_raw": [{"concert_info_get": "{\"location\": \"New York\", \"date\": \"2023-10\", \"genre\": \"Pop\"}"}], "model_result_decoded": [{"concert_info_get": {"location": "New York", "date": "2023-10", "genre": "Pop"}}], "possible_answer": [{"concert_info.get": {"location": ["New York City, NY", "New York"], "date": ["next month", "2023-06-01", "06/01/2023", "Jun.1,2023", "June 2023"], "genre": ["Pop"]}}]}
{"id": "simple_307", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'venue': 'home'. Expected one of ['', True]."], "error_type": "value_error:others", "prompt": {"id": "simple_307", "question": [[{"role": "user", "content": "Who won the basketball game between Lakers and Clippers on Jan 28, 2021?"}]], "function": [{"name": "game_result.get_winner", "description": "Get the winner of a specific basketball game.", "parameters": {"type": "dict", "properties": {"teams": {"type": "array", "items": {"type": "string"}, "description": "List of two teams who played the game."}, "date": {"type": "string", "description": "The date of the game, formatted as YYYY-MM-DD."}, "venue": {"type": "string", "optional": true, "description": "Optional: The venue of the game. Default is 'home'."}}, "required": ["teams", "date"]}}]}, "model_result_raw": [{"game_result_get_winner": "{\"teams\": [\"Lakers\", \"Clippers\"], \"date\": \"2021-01-28\", \"venue\": \"home\"}"}], "model_result_decoded": [{"game_result_get_winner": {"teams": ["Lakers", "Clippers"], "date": "2021-01-28", "venue": "home"}}], "possible_answer": [{"game_result.get_winner": {"teams": [["Lakers", "Clippers"], ["Clippers", "Lakers"]], "date": ["2021-01-28", "01/28/2021", "Jan.28,2021"], "venue": ["", true]}}]}
{"id": "simple_354", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'cooking_time': 29. Expected one of [30]."], "error_type": "value_error:others", "prompt": {"id": "simple_354", "question": [[{"role": "user", "content": "Find a vegan soup recipe that takes under 30 minutes to make."}]], "function": [{"name": "get_vegan_recipe", "description": "Retrieve a vegan soup recipe based on the provided cooking time.", "parameters": {"type": "dict", "properties": {"dish_type": {"type": "string", "description": "The type of dish, e.g. soup, dessert, etc.", "enum": ["soup", "main dish", "dessert", "salad"]}, "cooking_time": {"type": "integer", "description": "The maximum cooking time for the recipe in minutes."}, "ingredient_preference": {"type": "array", "items": {"type": "string"}, "description": "Preferred ingredients to be included in the recipe, if any. Default to not use it if not provided."}}, "required": ["dish_type", "cooking_time"]}}]}, "model_result_raw": [{"get_vegan_recipe": "{\"dish_type\": \"soup\", \"cooking_time\": 29}"}], "model_result_decoded": [{"get_vegan_recipe": {"dish_type": "soup", "cooking_time": 29}}], "possible_answer": [{"get_vegan_recipe": {"dish_type": ["soup"], "cooking_time": [30], "ingredient_preference": [""]}}]}
{"id": "simple_362", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'review_rate': 0.8. Expected one of [4.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_362", "question": [[{"role": "user", "content": "What are the top five sushi restaurants with high reviews i.e. above 4/5 in Tokyo?"}]], "function": [{"name": "get_best_sushi_places", "description": "Returns the best sushi places given the city, review_rate and top number.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city in which to look for the sushi places."}, "top": {"type": "integer", "description": "The number of top sushi places to be returned."}, "review_rate": {"type": "float", "description": "The review rating to filter the sushi places. Places with review ratings above this value will be returned. Default 0.00."}}, "required": ["city", "top"]}}]}, "model_result_raw": [{"get_best_sushi_places": "{\"city\": \"Tokyo\", \"top\": 5, \"review_rate\": 0.8}"}], "model_result_decoded": [{"get_best_sushi_places": {"city": "Tokyo", "top": 5, "review_rate": 0.8}}], "possible_answer": [{"get_best_sushi_places": {"city": ["Tokyo"], "top": [5], "review_rate": [4.0]}}]}
{"id": "simple_370", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'quantity': [3, 5]. Expected one of [[3, 1]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": [{"safeway_order": "{\"location\": \"Palo Alto, CA\", \"items\": [\"olive oil\", \"rice\"], \"quantity\": [3, 5]}"}], "model_result_decoded": [{"safeway_order": {"location": "Palo Alto, CA", "items": ["olive oil", "rice"], "quantity": [3, 5]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_385", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'room_type': 'king size bed'. Expected one of ['King Size', 'king size']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_385", "question": [[{"role": "user", "content": "Book a hotel room with king size bed in Los Angeles for 2 nights starting from 15th October,2023."}]], "function": [{"name": "hotel_bookings.book_room", "description": "Book a hotel room based on specific criteria like location, room type, and check-in and check-out dates.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where you want to book the hotel, e.g. Los Angeles, CA"}, "room_type": {"type": "string", "description": "Preferred type of room in the hotel, e.g. king size, queen size, deluxe, suite etc."}, "check_in_date": {"type": "string", "description": "Check-in date for the hotel. Format - DD-MM-YYYY."}, "no_of_nights": {"type": "integer", "description": "Number of nights for the stay."}, "no_of_rooms": {"type": "integer", "description": "Number of rooms to book. Default is 1.", "default": 1}}, "required": ["location", "room_type", "check_in_date", "no_of_nights"]}}]}, "model_result_raw": [{"hotel_bookings_book_room": "{\"location\": \"Los Angeles, CA\", \"room_type\": \"king size bed\", \"check_in_date\": \"15-10-2023\", \"no_of_nights\": 2}"}], "model_result_decoded": [{"hotel_bookings_book_room": {"location": "Los Angeles, CA", "room_type": "king size bed", "check_in_date": "15-10-2023", "no_of_nights": 2}}], "possible_answer": [{"hotel_bookings.book_room": {"location": ["Los Angeles", "Los Angeles, CA", "LA"], "room_type": ["King Size", "king size"], "check_in_date": ["15-10-2023", "15th October", "2023-10-15", "10/15/2023", "Oct.15,2023"], "no_of_nights": [2], "no_of_rooms": ["", 1]}}]}
{"id": "simple_399", "model_name": "Qwen_Qwen3-14B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'rating': 5. Expected one of [4]."], "error_type": "value_error:others", "prompt": {"id": "simple_399", "question": [[{"role": "user", "content": "Find me the best Italian restaurants in New York City with average customer ratings of more than 4 and accepts credit cards."}]], "function": [{"name": "restaurant_search", "description": "Locates top rated restaurants based on specific criteria such as type of cuisine, ratings, and facilities.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York City, NY"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine e.g., Italian, Indian, American, etc."}, "rating": {"type": "integer", "description": "Minimum average customer rating out of 5"}, "accepts_credit_cards": {"type": "boolean", "description": "If the restaurant should accept credit cards."}}, "required": ["location", "cuisine", "rating", "accepts_credit_cards"]}}]}, "model_result_raw": [{"restaurant_search": "{\"location\": \"New York City, NY\", \"cuisine\": \"Italian\", \"rating\": 5, \"accepts_credit_cards\": true}"}], "model_result_decoded": [{"restaurant_search": {"location": "New York City, NY", "cuisine": "Italian", "rating": 5, "accepts_credit_cards": true}}], "possible_answer": [{"restaurant_search": {"location": ["New York City", "New York City, NY", "NYC"], "cuisine": ["Italian"], "rating": [4], "accepts_credit_cards": [true]}}]}