{"accuracy": 0.8575, "correct_count": 343, "total_count": 400}
{"id": "simple_13", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": [{"calculate_area_under_curve": "{\"function\": \"y=x^2\", \"interval\": [1, 3], \"method\": \"trapezoidal\"}"}], "model_result_decoded": [{"calculate_area_under_curve": {"function": "y=x^2", "interval": [1, 3], "method": "trapezoidal"}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_41", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'medium_permittivity': 8.854. Expected one of ['', 8.854e-12]."], "error_type": "value_error:others", "prompt": {"id": "simple_41", "question": [[{"role": "user", "content": "Calculate the electromagnetic force between two charges of 5C and 7C placed 3 meters apart."}]], "function": [{"name": "electromagnetic_force", "description": "Calculate the electromagnetic force between two charges placed at a certain distance.", "parameters": {"type": "dict", "properties": {"charge1": {"type": "integer", "description": "The magnitude of the first charge in coulombs."}, "charge2": {"type": "integer", "description": "The magnitude of the second charge in coulombs."}, "distance": {"type": "integer", "description": "The distance between the two charges in meters."}, "medium_permittivity": {"type": "float", "description": "The relative permittivity of the medium in which the charges are present. Default is 8.854e-12 (Vacuum Permittivity)."}}, "required": ["charge1", "charge2", "distance"]}}]}, "model_result_raw": [{"electromagnetic_force": "{\"charge1\": 5, \"charge2\": 7, \"distance\": 3, \"medium_permittivity\": 8.854}"}], "model_result_decoded": [{"electromagnetic_force": {"charge1": 5, "charge2": 7, "distance": 3, "medium_permittivity": 8.854}}], "possible_answer": [{"electromagnetic_force": {"charge1": [5], "charge2": [7], "distance": [3], "medium_permittivity": ["", 8.854e-12]}}]}
{"id": "simple_42", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'capacitance': 0.1. Expected one of [0.0001]."], "error_type": "value_error:others", "prompt": {"id": "simple_42", "question": [[{"role": "user", "content": "Calculate the resonant frequency of an LC circuit given capacitance of 100\u00b5F and inductance of 50mH."}]], "function": [{"name": "calculate_resonant_frequency", "description": "Calculate the resonant frequency of an LC (inductor-capacitor) circuit.", "parameters": {"type": "dict", "properties": {"inductance": {"type": "float", "description": "The inductance (L) in henries (H)."}, "capacitance": {"type": "float", "description": "The capacitance (C) in farads (F)."}, "round_off": {"type": "integer", "description": "Rounding off the result to a certain decimal places, default is 2."}}, "required": ["inductance", "capacitance"]}}]}, "model_result_raw": [{"calculate_resonant_frequency": "{\"inductance\": 0.05, \"capacitance\": 0.1, \"round_off\": 2}"}], "model_result_decoded": [{"calculate_resonant_frequency": {"inductance": 0.05, "capacitance": 0.1, "round_off": 2}}], "possible_answer": [{"calculate_resonant_frequency": {"inductance": [0.05], "capacitance": [0.0001], "round_off": ["", 2]}}]}
{"id": "simple_61", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'height': 65. Expected one of [70]."], "error_type": "value_error:others", "prompt": {"id": "simple_61", "question": [[{"role": "user", "content": "Predict whether a person with weight 150lbs and height 5ft 10in who is lightly active will get type 2 diabetes."}]], "function": [{"name": "diabetes_prediction", "description": "Predict the likelihood of diabetes type 2 based on a person's weight and height.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "Weight of the person in lbs."}, "height": {"type": "integer", "description": "Height of the person in inches."}, "activity_level": {"type": "string", "enum": ["sedentary", "lightly active", "moderately active", "very active", "extra active"], "description": "Physical activity level of the person."}}, "required": ["weight", "height", "activity_level"]}}]}, "model_result_raw": [{"diabetes_prediction": "{\"weight\": 150, \"height\": 65, \"activity_level\": \"lightly active\"}"}], "model_result_decoded": [{"diabetes_prediction": {"weight": 150, "height": 65, "activity_level": "lightly active"}}], "possible_answer": [{"diabetes_prediction": {"weight": [150], "height": [70], "activity_level": ["lightly active"]}}]}
{"id": "simple_69", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'species' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_69", "question": [[{"role": "user", "content": "Find out the population and species of turtles in Mississippi river in 2020."}]], "function": [{"name": "ecology.get_turtle_population", "description": "Get the population and species of turtles in a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location."}, "year": {"type": "integer", "description": "The year of the data requested. Default is 2001."}, "species": {"type": "boolean", "description": "Whether to include species information. Default is false."}}, "required": ["location"]}}]}, "model_result_raw": [{"ecology_get_turtle_population": "{\"location\": \"Mississippi River\", \"year\": 2020}"}], "model_result_decoded": [{"ecology_get_turtle_population": {"location": "Mississippi River", "year": 2020}}], "possible_answer": [{"ecology.get_turtle_population": {"location": ["Mississippi river"], "year": [2020], "species": [true]}}]}
{"id": "simple_82", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers'. Expected outer type array with inner type <class 'float'>. Parameter value: [12, 15, 18, 20, 21, 26, 30]."], "error_type": "type_error:nested", "prompt": {"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}, "model_result_raw": [{"calculate_average": "{\"numbers\": [12, 15, 18, 20, 21, 26, 30]}"}], "model_result_decoded": [{"calculate_average": {"numbers": [12, 15, 18, 20, 21, 26, 30]}}], "possible_answer": [{"calculate_average": {"numbers": [[12.0, 15.0, 18.0, 20.0, 21.0, 26.0, 30.0]]}}]}
{"id": "simple_83", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'coord1': [33.4484, 112.074]. Expected one of [[33.4484, -112.074]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_83", "question": [[{"role": "user", "content": "Calculate the distance between two GPS coordinates (33.4484 N, 112.0740 W) and (34.0522 N, 118.2437 W) in miles."}]], "function": [{"name": "calculate_distance", "description": "Calculate the distance between two GPS coordinates.", "parameters": {"type": "dict", "properties": {"coord1": {"type": "tuple", "description": "The first coordinate as (latitude, longitude).", "items": {"type": "float"}}, "coord2": {"type": "tuple", "description": "The second coordinate as (latitude, longitude).", "items": {"type": "float"}}, "unit": {"type": "string", "description": "The unit of distance. Options: 'miles', 'kilometers'."}}, "required": ["coord1", "coord2", "unit"]}}]}, "model_result_raw": [{"calculate_distance": "{\"coord1\": [33.4484, 112.074], \"coord2\": [34.0522, 118.2437], \"unit\": \"miles\"}"}], "model_result_decoded": [{"calculate_distance": {"coord1": [33.4484, 112.074], "coord2": [34.0522, 118.2437], "unit": "miles"}}], "possible_answer": [{"calculate_distance": {"coord1": [[33.4484, -112.074]], "coord2": [[34.0522, -118.2437]], "unit": ["miles"]}}]}
{"id": "simple_87", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'list'. Expected outer type array with inner type <class 'float'>. Parameter value: [5, 3, 4, 1, 2]."], "error_type": "type_error:nested", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": [{"array_sort": "{\"list\": [5, 3, 4, 1, 2], \"order\": \"ascending\"}"}], "model_result_decoded": [{"array_sort": {"list": [5, 3, 4, 1, 2], "order": "ascending"}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_89", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'table_name': 'student'. Expected one of ['students']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_89", "question": [[{"role": "user", "content": "Fetch all records for students studying Science in 'Bluebird High School' from the StudentDB."}]], "function": [{"name": "db_fetch_records", "description": "Fetch records from a specified database table based on certain conditions.", "parameters": {"type": "dict", "properties": {"database_name": {"type": "string", "description": "The name of the database."}, "table_name": {"type": "string", "description": "The name of the table from which records need to be fetched."}, "conditions": {"type": "dict", "properties": {"department": {"type": "string", "description": "The name of the department of students."}, "school": {"type": "string", "description": "The name of the school students are enrolled in."}}, "description": "The conditions based on which records are to be fetched."}, "fetch_limit": {"type": "integer", "description": "Limits the number of records to be fetched. Default is 0, which means no limit."}}, "required": ["database_name", "table_name", "conditions"]}}]}, "model_result_raw": [{"db_fetch_records": "{\"database_name\": \"StudentDB\", \"table_name\": \"student\", \"conditions\": {\"department\": \"Science\", \"school\": \"Bluebird High School\"}}"}], "model_result_decoded": [{"db_fetch_records": {"database_name": "StudentDB", "table_name": "student", "conditions": {"department": "Science", "school": "Bluebird High School"}}}], "possible_answer": [{"db_fetch_records": {"database_name": ["StudentDB"], "table_name": ["students"], "conditions": [{"department": ["Science"], "school": ["Bluebird High School", "Bluebird HS"]}], "fetch_limit": ["", 0]}}]}
{"id": "simple_91", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'condition': 'Sunday'. Expected one of ['open on Sundays', 'opens on Sundays']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_91", "question": [[{"role": "user", "content": "Get the highest rated sushi restaurant in Boston, that opens on Sundays."}]], "function": [{"name": "get_restaurant", "description": "Retrieve highest rated restaurant given cuisine, location, and a condition.", "parameters": {"type": "dict", "properties": {"cuisine": {"type": "string", "description": "Cuisine of the restaurant."}, "location": {"type": "string", "description": "City where restaurant is located."}, "condition": {"type": "string", "description": "Condition to be met by the restaurant (e.g., operating days, amenities, etc.)"}}, "required": ["cuisine", "location", "condition"]}}]}, "model_result_raw": [{"get_restaurant": "{\"cuisine\": \"sushi\", \"location\": \"Boston\", \"condition\": \"Sunday\"}"}], "model_result_decoded": [{"get_restaurant": {"cuisine": "sushi", "location": "Boston", "condition": "Sunday"}}], "possible_answer": [{"get_restaurant": {"cuisine": ["sushi"], "location": ["Boston"], "condition": ["open on Sundays", "opens on Sundays"]}}]}
{"id": "simple_103", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'polynomial'. Expected outer type array with inner type <class 'float'>. Parameter value: [3, 2, -4]."], "error_type": "type_error:nested", "prompt": {"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}, "model_result_raw": [{"mathematics_calculate_area_under_curve": "{\"polynomial\": [3, 2, -4], \"limits\": [-1, 2]}"}], "model_result_decoded": [{"mathematics_calculate_area_under_curve": {"polynomial": [3, 2, -4], "limits": [-1, 2]}}], "possible_answer": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3.0, 2.0, -4.0]], "limits": [[-1.0, 2.0]]}}]}
{"id": "simple_127", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'discount_rate': 10.0. Expected one of [0.1]."], "error_type": "value_error:others", "prompt": {"id": "simple_127", "question": [[{"role": "user", "content": "Find the Net Present Value (NPV) of an investment, given cash_flows=[200,300,400,500], a discount rate of 10%, and an initial investment of $2000."}]], "function": [{"name": "calculate_NPV", "description": "Calculate the NPV (Net Present Value) of an investment, considering a series of future cash flows, discount rate, and an initial investment.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "integer"}, "description": "Series of future cash flows."}, "discount_rate": {"type": "float", "description": "The discount rate to use."}, "initial_investment": {"type": "integer", "description": "The initial investment. Default is 0 if not specified."}}, "required": ["cash_flows", "discount_rate"]}}]}, "model_result_raw": [{"calculate_NPV": "{\"cash_flows\": [200, 300, 400, 500], \"discount_rate\": 10.0, \"initial_investment\": 2000}"}], "model_result_decoded": [{"calculate_NPV": {"cash_flows": [200, 300, 400, 500], "discount_rate": 10.0, "initial_investment": 2000}}], "possible_answer": [{"calculate_NPV": {"cash_flows": [[200, 300, 400, 500]], "discount_rate": [0.1], "initial_investment": [2000]}}]}
{"id": "simple_129", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'discount_rate': 4.0. Expected one of [0.04]."], "error_type": "value_error:others", "prompt": {"id": "simple_129", "question": [[{"role": "user", "content": "Calculate the discounted cash flow of a bond that is giving a coupon payment of $100 annually for next 5 years with discount rate 4%."}]], "function": [{"name": "calculate_discounted_cash_flow", "description": "Calculate the discounted cash flow of a bond for a given annual coupon payment, time frame and discount rate.", "parameters": {"type": "dict", "properties": {"coupon_payment": {"type": "integer", "description": "The annual coupon payment."}, "period": {"type": "integer", "description": "The time frame in years for which coupon payment is made."}, "discount_rate": {"type": "float", "description": "The discount rate."}, "face_value": {"type": "integer", "description": "The face value of the bond, default is 1000."}}, "required": ["coupon_payment", "period", "discount_rate"]}}]}, "model_result_raw": [{"calculate_discounted_cash_flow": "{\"coupon_payment\": 100, \"period\": 5, \"discount_rate\": 4}"}], "model_result_decoded": [{"calculate_discounted_cash_flow": {"coupon_payment": 100, "period": 5, "discount_rate": 4}}], "possible_answer": [{"calculate_discounted_cash_flow": {"coupon_payment": [100], "period": [5], "discount_rate": [0.04], "face_value": ["", 1000]}}]}
{"id": "simple_132", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'dividends_paid' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_132", "question": [[{"role": "user", "content": "Calculate the company's return on equity given its net income of $2,000,000, shareholder's equity of $10,000,000, and dividends paid of $200,000."}]], "function": [{"name": "calculate_return_on_equity", "description": "Calculate a company's return on equity based on its net income, shareholder's equity, and dividends paid.", "parameters": {"type": "dict", "properties": {"net_income": {"type": "integer", "description": "The company's net income."}, "shareholder_equity": {"type": "integer", "description": "The company's total shareholder's equity."}, "dividends_paid": {"type": "integer", "description": "The total dividends paid by the company. Optional. If not given, default to 0."}}, "required": ["net_income", "shareholder_equity"]}}]}, "model_result_raw": [{"calculate_return_on_equity": "{\"net_income\": 2000000, \"shareholder_equity\": 10000000}"}], "model_result_decoded": [{"calculate_return_on_equity": {"net_income": 2000000, "shareholder_equity": 10000000}}], "possible_answer": [{"calculate_return_on_equity": {"net_income": [2000000], "shareholder_equity": [10000000], "dividends_paid": [200000]}}]}
{"id": "simple_136", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'annual_rate': 0.05. Expected one of [5.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_136", "question": [[{"role": "user", "content": "Find the compound interest for an investment of $10000 with an annual interest rate of 5% compounded monthly for 5 years."}]], "function": [{"name": "compound_interest", "description": "Calculate compound interest for a certain time period.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The initial amount of money that was invested or loaned out."}, "annual_rate": {"type": "float", "description": "The interest rate for a year as a percentage."}, "compounding_freq": {"type": "string", "enum": ["monthly", "quarterly", "annually"], "description": "The number of times that interest is compounded per unit period."}, "time_in_years": {"type": "integer", "description": "The time the money is invested for in years."}}, "required": ["principal", "annual_rate", "compounding_freq", "time_in_years"]}}]}, "model_result_raw": [{"compound_interest": "{\"principal\": 10000, \"annual_rate\": 0.05, \"compounding_freq\": \"monthly\", \"time_in_years\": 5}"}], "model_result_decoded": [{"compound_interest": {"principal": 10000, "annual_rate": 0.05, "compounding_freq": "monthly", "time_in_years": 5}}], "possible_answer": [{"compound_interest": {"principal": [10000], "annual_rate": [5.0], "compounding_freq": ["monthly"], "time_in_years": [5]}}]}
{"id": "simple_139", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'yearly_yield': 0.05. Expected one of [5.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_139", "question": [[{"role": "user", "content": "What is the estimated return on a mutual fund, given that it has a yearly yield of 5%, an investment amount of $2000 and a time period of 3 years?"}]], "function": [{"name": "estimate_mutual_fund_return", "description": "Calculate the estimated return on a mutual fund given the yearly yield, the investment amount and the time period.", "parameters": {"type": "dict", "properties": {"yearly_yield": {"type": "float", "description": "The yearly yield of the mutual fund as a percentage."}, "investment_amount": {"type": "integer", "description": "The initial investment amount in the mutual fund."}, "years": {"type": "integer", "description": "The time period for which the investment is made in years."}}, "required": ["yearly_yield", "investment_amount", "years"]}}]}, "model_result_raw": [{"estimate_mutual_fund_return": "{\"yearly_yield\": 0.05, \"investment_amount\": 2000, \"years\": 3}"}], "model_result_decoded": [{"estimate_mutual_fund_return": {"yearly_yield": 0.05, "investment_amount": 2000, "years": 3}}], "possible_answer": [{"estimate_mutual_fund_return": {"yearly_yield": [5.0], "investment_amount": [2000], "years": [3]}}]}
{"id": "simple_151", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'top_n' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_151", "question": [[{"role": "user", "content": "Find the highest grossing bank in the U.S for year 2020."}]], "function": [{"name": "highest_grossing_banks", "description": "Retrieve the highest grossing banks in a specified country and year.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country to get the data from."}, "year": {"type": "integer", "description": "The year to get the data from."}, "top_n": {"type": "integer", "description": "Top n banks in terms of grossing. Default is 5"}}, "required": ["country", "year"]}}]}, "model_result_raw": [{"highest_grossing_banks": "{\"country\": \"United States\", \"year\": 2020}"}], "model_result_decoded": [{"highest_grossing_banks": {"country": "United States", "year": 2020}}], "possible_answer": [{"highest_grossing_banks": {"country": ["U.S", "United States", "USA", "U.S."], "year": [2020], "top_n": [1]}}]}
{"id": "simple_156", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'details' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_156", "question": [[{"role": "user", "content": "Look up details of a felony crime record for case number CA123456 in San Diego County"}]], "function": [{"name": "crime_record.get_record", "description": "Retrieve detailed felony crime records using a specific case number and location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The case number related to the crime."}, "county": {"type": "string", "description": "The county in which the crime occurred."}, "details": {"type": "boolean", "description": "To get a detailed report, set as true. Defaults to false."}}, "required": ["case_number", "county"]}}]}, "model_result_raw": [{"crime_record_get_record": "{\"case_number\": \"CA123456\", \"county\": \"San Diego County\"}"}], "model_result_decoded": [{"crime_record_get_record": {"case_number": "CA123456", "county": "San Diego County"}}], "possible_answer": [{"crime_record.get_record": {"case_number": ["CA123456"], "county": ["San Diego", "San Diego County"], "details": [true]}}]}
{"id": "simple_159", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'act_name': 'Criminal Law Amendment Act of 2013'. Expected one of ['Criminal Law Amendment Act', 'Criminal Law Amendment']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_159", "question": [[{"role": "user", "content": "Give me the details of Criminal Law Amendment Act of 2013."}]], "function": [{"name": "get_act_details", "description": "Retrieve the details of a particular legal act based on its name and year of amendment if any.", "parameters": {"type": "dict", "properties": {"act_name": {"type": "string", "description": "The name of the act."}, "amendment_year": {"type": "integer", "description": "Year of amendment if any. If not provided, the latest amendment year will be considered."}}, "required": ["act_name", "amendment_year"]}}]}, "model_result_raw": [{"get_act_details": "{\"act_name\": \"Criminal Law Amendment Act of 2013\", \"amendment_year\": 2013}"}], "model_result_decoded": [{"get_act_details": {"act_name": "Criminal Law Amendment Act of 2013", "amendment_year": 2013}}], "possible_answer": [{"get_act_details": {"act_name": ["Criminal Law Amendment Act", "Criminal Law Amendment"], "amendment_year": [2013]}}]}
{"id": "simple_162", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'contract_type': 'rental'. Expected one of ['Rental Agreement', 'rental agreement']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_162", "question": [[{"role": "user", "content": "Generate a customized law contract between John and Alice for rental agreement in California."}]], "function": [{"name": "generate_law_contract", "description": "Generates a customized law contract given involved parties, contract type and location.", "parameters": {"type": "dict", "properties": {"parties": {"type": "array", "items": {"type": "string"}, "description": "Parties involved in the contract."}, "contract_type": {"type": "string", "description": "Type of the contract."}, "location": {"type": "string", "description": "Location where the contract will be in effect."}}, "required": ["parties", "contract_type", "location"]}}]}, "model_result_raw": [{"generate_law_contract": "{\"parties\": [\"John\", \"Alice\"], \"contract_type\": \"rental\", \"location\": \"California\"}"}], "model_result_decoded": [{"generate_law_contract": {"parties": ["John", "Alice"], "contract_type": "rental", "location": "California"}}], "possible_answer": [{"generate_law_contract": {"parties": [["John", "Alice"], ["John", "Alice"]], "contract_type": ["Rental Agreement", "rental agreement"], "location": ["California", "CA"]}}]}
{"id": "simple_166", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'city': 'Chicago'. Expected one of ['Chicago, IL.', 'Chicago, IL']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_166", "question": [[{"role": "user", "content": "Find a lawyer specializing in divorce cases and charge fee less than 400 dollars per hour in Chicago."}]], "function": [{"name": "lawyer.find_nearby", "description": "Locate nearby lawyers based on specific criteria like specialty, fee per hour and city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city and state, e.g. Chicago, IL."}, "specialty": {"type": "array", "items": {"type": "string", "enum": ["Civil", "Divorce", "Immigration", "Business", "Criminal"]}, "description": "Specialization of the lawyer."}, "fee": {"type": "integer", "description": "Hourly fee charged by lawyer"}}, "required": ["city", "specialty", "fee"]}}]}, "model_result_raw": [{"lawyer_find_nearby": "{\"city\": \"Chicago\", \"specialty\": [\"Divorce\"], \"fee\": 399}"}], "model_result_decoded": [{"lawyer_find_nearby": {"city": "Chicago", "specialty": ["Divorce"], "fee": 399}}], "possible_answer": [{"lawyer.find_nearby": {"city": ["Chicago, IL.", "Chicago, IL"], "specialty": [["Divorce"]], "fee": [400]}}]}
{"id": "simple_172", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_172", "question": [[{"role": "user", "content": "How to obtain the detailed case information of the 'R vs Adams' legal case?"}]], "function": [{"name": "legal_case.fetch", "description": "Fetch detailed legal case information from database.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "string", "description": "The ID of the legal case."}, "details": {"type": "boolean", "description": "True if need the detail info. "}}, "required": ["case_id", "details"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"legal_case.fetch": {"case_id": ["R vs Adams"], "details": [true]}}]}
{"id": "simple_173", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'judicial_system' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_173", "question": [[{"role": "user", "content": "Find state law cases related to land disputes in the past 5 years from 2015 to 2021 in New York."}]], "function": [{"name": "law_case_search", "description": "Search and retrieve law cases based on the topic, timeline, and location.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The subject matter of the case."}, "year_range": {"type": "array", "items": {"type": "integer"}, "description": "The start and end year for searching cases."}, "location": {"type": "string", "description": "The location where the case is being heard."}, "judicial_system": {"type": "string", "description": "The specific judicial system in which to search (e.g. 'federal', 'state').", "default": "all"}}, "required": ["topic", "year_range", "location"]}}]}, "model_result_raw": [{"law_case_search": "{\"topic\": \"land disputes\", \"year_range\": [2015, 2021], \"location\": \"New York\"}"}], "model_result_decoded": [{"law_case_search": {"topic": "land disputes", "year_range": [2015, 2021], "location": "New York"}}], "possible_answer": [{"law_case_search": {"topic": ["land disputes"], "year_range": [[2015, 2021]], "location": ["New York"], "judicial_system": ["state"]}}]}
{"id": "simple_176", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'case_type' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_176", "question": [[{"role": "user", "content": "Find details of patent lawsuits involving the company 'Apple Inc.' from the year 2010."}]], "function": [{"name": "lawsuit_details.find", "description": "Find details of lawsuits involving a specific company from a given year.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "Name of the company."}, "year": {"type": "integer", "description": "Year of the lawsuit."}, "case_type": {"type": "string", "description": "Type of the lawsuit, e.g., 'IPR', 'Patent', 'Commercial', etc. Default is 'all'."}}, "required": ["company_name", "year"]}}]}, "model_result_raw": [{"lawsuit_details_find": "{\"company_name\": \"Apple Inc.\", \"year\": 2010}"}], "model_result_decoded": [{"lawsuit_details_find": {"company_name": "Apple Inc.", "year": 2010}}], "possible_answer": [{"lawsuit_details.find": {"company_name": ["Apple Inc."], "year": [2010], "case_type": ["Patent", "IPR"]}}]}
{"id": "simple_181", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'with_verdict' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_181", "question": [[{"role": "user", "content": "Get details of a lawsuit with case number '123456-ABC' filed in Los Angeles court with verdict"}]], "function": [{"name": "get_lawsuit_details", "description": "Retrieve details of a lawsuit based on its case number and court location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "Case number of the lawsuit."}, "court_location": {"type": "string", "description": "The location of the court where the lawsuit was filed."}, "with_verdict": {"type": "boolean", "description": "Flag to include verdict details if available. Default is False"}}, "required": ["case_number", "court_location"]}}]}, "model_result_raw": [{"get_lawsuit_details": "{\"case_number\": \"123456-ABC\", \"court_location\": \"Los Angeles\"}"}], "model_result_decoded": [{"get_lawsuit_details": {"case_number": "123456-ABC", "court_location": "Los Angeles"}}], "possible_answer": [{"get_lawsuit_details": {"case_number": ["123456-ABC"], "court_location": ["Los Angeles"], "with_verdict": [true]}}]}
{"id": "simple_196", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'date': '2022/08/16'. Expected one of ['08-16-2022']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_196", "question": [[{"role": "user", "content": "What is the air quality index in London 2022/08/16?"}]], "function": [{"name": "air_quality", "description": "Retrieve the air quality index for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality index for."}, "date": {"type": "string", "description": "The date (month-day-year) you want to get the air quality index for."}}, "required": ["location", "date"]}}]}, "model_result_raw": [{"air_quality": "{\"location\": \"London\", \"date\": \"2022/08/16\"}"}], "model_result_decoded": [{"air_quality": {"location": "London", "date": "2022/08/16"}}], "possible_answer": [{"air_quality": {"location": ["London"], "date": ["08-16-2022"]}}]}
{"id": "simple_202", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_202", "question": [[{"role": "user", "content": "How many greenhouse gas emissions would I save if I switched to renewable energy sources for 3 months in California?"}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'Texas'."}}, "required": ["energy_type", "usage_duration"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"calculate_emission_savings": {"energy_type": ["renewable"], "usage_duration": [3], "region": ["California", "CA"]}}]}
{"id": "simple_203", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'detail' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_203", "question": [[{"role": "user", "content": "Can you find me the latest information about air quality index and pollution data for Chicago?"}]], "function": [{"name": "get_air_quality", "description": "Retrieve real-time air quality and pollution data for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality data for."}, "detail": {"type": "boolean", "description": "If true, additional data like PM2.5, PM10, ozone levels, and pollution sources will be retrieved. Default is false."}}, "required": ["location"]}}]}, "model_result_raw": [{"get_air_quality": "{\"location\": \"Chicago\"}"}], "model_result_decoded": [{"get_air_quality": {"location": "Chicago"}}], "possible_answer": [{"get_air_quality": {"location": ["Chicago"], "detail": [true]}}]}
{"id": "simple_214", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'days_ahead' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_214", "question": [[{"role": "user", "content": "Search for upcoming month rock concerts in New York."}]], "function": [{"name": "event_finder.find_upcoming", "description": "Find upcoming events of a specific genre in a given location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the search will take place, e.g. New York, NY."}, "genre": {"type": "string", "description": "The genre of events."}, "days_ahead": {"type": "integer", "description": "The number of days from now to include in the search.", "default": 7}}, "required": ["location", "genre"]}}]}, "model_result_raw": [{"event_finder_find_upcoming": "{\"location\": \"New York\", \"genre\": \"rock\"}"}], "model_result_decoded": [{"event_finder_find_upcoming": {"location": "New York", "genre": "rock"}}], "possible_answer": [{"event_finder.find_upcoming": {"location": ["New York", "New York, NY", "NYC"], "genre": ["Rock", "rock"], "days_ahead": [30]}}]}
{"id": "simple_224", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'sub_topics': ['behavior', 'group dynamics']. Expected one of [['behaviour', 'group dynamics']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_224", "question": [[{"role": "user", "content": "Find the most followed person on twitter who tweets about psychology related to behaviour and group dynamics."}]], "function": [{"name": "social_media_analytics.most_followed", "description": "Find the most followed Twitter user related to certain topics.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The main topic of interest."}, "sub_topics": {"type": "array", "items": {"type": "string"}, "description": "Sub-topics related to main topic. Default is empty."}, "region": {"type": "string", "description": "Region of interest for twitter search. Default is 'all'."}}, "required": ["topic"]}}]}, "model_result_raw": [{"social_media_analytics_most_followed": "{\"topic\": \"psychology\", \"sub_topics\": [\"behavior\", \"group dynamics\"], \"region\": \"all\"}"}], "model_result_decoded": [{"social_media_analytics_most_followed": {"topic": "psychology", "sub_topics": ["behavior", "group dynamics"], "region": "all"}}], "possible_answer": [{"social_media_analytics.most_followed": {"topic": ["psychology"], "sub_topics": [["behaviour", "group dynamics"]], "region": ["", "all"]}}]}
{"id": "simple_228", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'trait_count': 5. Expected one of [3]."], "error_type": "value_error:others", "prompt": {"id": "simple_228", "question": [[{"role": "user", "content": "Find three personality traits of people who like jogging."}]], "function": [{"name": "get_personality_traits", "description": "Retrieve the common personality traits of people based on their hobbies or activities.", "parameters": {"type": "dict", "properties": {"hobby": {"type": "string", "description": "The hobby or activity of interest."}, "trait_count": {"type": "integer", "description": "The number of top traits to return, default is 5"}}, "required": ["hobby"]}}]}, "model_result_raw": [{"get_personality_traits": "{\"hobby\": \"jogging\", \"trait_count\": 5}"}], "model_result_decoded": [{"get_personality_traits": {"hobby": "jogging", "trait_count": 5}}], "possible_answer": [{"get_personality_traits": {"hobby": ["jogging"], "trait_count": [3]}}]}
{"id": "simple_233", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'format' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_233", "question": [[{"role": "user", "content": "When did the Treaty of Tordesillas take place? Put it in the format of YYYY."}]], "function": [{"name": "european_history.get_event_date", "description": "Retrieve the date of a specific event in European history.", "parameters": {"type": "dict", "properties": {"event_name": {"type": "string", "description": "The name of the event."}, "format": {"type": "string", "description": "Optional format of the returned date. Default is 'MM-DD-YYYY'."}}, "required": ["event_name"]}}]}, "model_result_raw": [{"european_history_get_event_date": "{\"event_name\": \"Treaty of Tordesillas\"}"}], "model_result_decoded": [{"european_history_get_event_date": {"event_name": "Treaty of Tordesillas"}}], "possible_answer": [{"european_history.get_event_date": {"event_name": ["Treaty of Tordesillas"], "format": ["YYYY"]}}]}
{"id": "simple_238", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_238", "question": [[{"role": "user", "content": "Who was the president of the United States during the American Civil War?"}]], "function": [{"name": "us_history.get_president", "description": "Retrieve the U.S. president during a specific event in American history.", "parameters": {"type": "dict", "properties": {"event": {"type": "string", "description": "The event in U.S. history."}, "year": {"type": "integer", "description": "The specific year of the event."}}, "required": ["event", "year"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"us_history.get_president": {"event": ["American Civil War"], "year": [1861, 1862, 1863, 1864, 1865]}}]}
{"id": "simple_242", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_242", "question": [[{"role": "user", "content": "Who is the scientist that first proposed the theory of evolution?"}]], "function": [{"name": "get_scientist_for_discovery", "description": "Retrieve the scientist's name who is credited for a specific scientific discovery or theory.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The scientific discovery or theory."}}, "required": ["discovery"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"get_scientist_for_discovery": {"discovery": ["Theory of Evolution", "theory of evolution"]}}]}
{"id": "simple_251", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'name': 'Jesus'. Expected one of ['Jesus Christ']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_251", "question": [[{"role": "user", "content": "What is the earliest reference of Jesus Christ in history from historical record?"}]], "function": [{"name": "get_earliest_reference", "description": "Retrieve the earliest historical reference of a person.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "source": {"type": "string", "enum": ["scriptures", "historical records"], "description": "Source to fetch the reference. Default is 'scriptures'"}}, "required": ["name"]}}]}, "model_result_raw": [{"get_earliest_reference": "{\"name\": \"Jesus\", \"source\": \"historical records\"}"}], "model_result_decoded": [{"get_earliest_reference": {"name": "Jesus", "source": "historical records"}}], "possible_answer": [{"get_earliest_reference": {"name": ["Jesus Christ"], "source": ["historical records"]}}]}
{"id": "simple_267", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York City, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_267", "question": [[{"role": "user", "content": "Find the top rated modern sculpture exhibition happening in New York in the upcoming month."}]], "function": [{"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the exhibition is held, e.g., New York City, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events if not specified."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'low'"}}, "required": ["location", "art_form"]}}]}, "model_result_raw": [{"find_exhibition": "{\"location\": \"New York\", \"art_form\": \"modern sculpture\", \"month\": \"upcoming\"}"}], "model_result_decoded": [{"find_exhibition": {"location": "New York", "art_form": "modern sculpture", "month": "upcoming"}}], "possible_answer": [{"find_exhibition": {"location": ["New York City, NY"], "art_form": ["sculpture", "modern sculpture"], "month": [""], "user_ratings": ["high"]}}]}
{"id": "simple_279", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'model': 'American Professional II'. Expected one of ['American Professional II Stratocaster']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_279", "question": [[{"role": "user", "content": "What's the retail price of a Fender American Professional II Stratocaster in Rosewood Finish?"}]], "function": [{"name": "instrument_price.get", "description": "Retrieve the current retail price of a specific musical instrument.", "parameters": {"type": "dict", "properties": {"brand": {"type": "string", "description": "The brand of the instrument."}, "model": {"type": "string", "description": "The specific model of the instrument."}, "finish": {"type": "string", "description": "The color or type of finish on the instrument."}}, "required": ["brand", "model", "finish"]}}]}, "model_result_raw": [{"instrument_price_get": "{\"brand\": \"Fender\", \"model\": \"American Professional II\", \"finish\": \"Rosewood\"}"}], "model_result_decoded": [{"instrument_price_get": {"brand": "Fender", "model": "American Professional II", "finish": "Rosewood"}}], "possible_answer": [{"instrument_price.get": {"brand": ["Fender"], "model": ["American Professional II Stratocaster"], "finish": ["Rosewood"]}}]}
{"id": "simple_285", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'Chicago'. Expected one of ['Chicago, Illinois', 'Chicago, IL']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_285", "question": [[{"role": "user", "content": "Find me a Rock concert in Chicago with ticket availability under $100."}]], "function": [{"name": "find_concert", "description": "Locate a concert in a specified location within a certain budget.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you are looking for a concert. In the format City, State."}, "price": {"type": "integer", "description": "Maximum ticket price."}, "genre": {"type": "string", "description": "Music genre of the concert. Default to 'Jazz'. ", "enum": ["Rock", "Pop", "Country", "Jazz", "Classical"]}}, "required": ["location", "price"]}}]}, "model_result_raw": [{"find_concert": "{\"location\": \"Chicago\", \"price\": 100, \"genre\": \"Rock\"}"}], "model_result_decoded": [{"find_concert": {"location": "Chicago", "price": 100, "genre": "Rock"}}], "possible_answer": [{"find_concert": {"location": ["Chicago, Illinois", "Chicago, IL"], "price": [100], "genre": ["Rock"]}}]}
{"id": "simple_286", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'artist': 'Beyonc\u00e9'. Expected one of ['Beyonce']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_286", "question": [[{"role": "user", "content": "Get concert details for the artist Beyonce performing in San Diego next month (April 2022)."}]], "function": [{"name": "concert.get_details", "description": "Fetch the details for a particular concert based on the artist and location.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the artist/band who's performing."}, "location": {"type": "string", "description": "City where the concert is taking place."}, "date": {"type": "string", "description": "Date of the concert in 'mm-yyyy' format. Default is the current month if not specified."}}, "required": ["artist", "location"]}}]}, "model_result_raw": [{"concert_get_details": "{\"artist\": \"Beyonc\u00e9\", \"location\": \"San Diego\", \"date\": \"04-2022\"}"}], "model_result_decoded": [{"concert_get_details": {"artist": "Beyonc\u00e9", "location": "San Diego", "date": "04-2022"}}], "possible_answer": [{"concert.get_details": {"artist": ["Beyonce"], "location": ["San Diego", "San Diego, California", "CA"], "date": ["04-2022", "April 2022"]}}]}
{"id": "simple_304", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'team': \"LeBron James's team\". Expected one of ['Los Angeles Lakers', 'LAL', 'Lakers']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_304", "question": [[{"role": "user", "content": "Get point and rebound stats for player 'LeBron James' from last basketball game"}]], "function": [{"name": "player_stats.getLastGame", "description": "Get last game statistics for a specific player in basketball", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the basketball player."}, "team": {"type": "string", "description": "The team that player currently plays for."}, "metrics": {"type": "array", "items": {"type": "string", "enum": ["Points", "Rebounds", "Assists", "Blocks"]}, "description": "Specific metrics to retrieve. If no value is specified, all available metrics will be returned by default."}}, "required": ["player_name", "team"]}}]}, "model_result_raw": [{"player_stats_getLastGame": "{\"player_name\": \"LeBron James\", \"team\": \"LeBron James's team\", \"metrics\": [\"Points\", \"Rebounds\"]}"}], "model_result_decoded": [{"player_stats_getLastGame": {"player_name": "LeBron James", "team": "LeBron James's team", "metrics": ["Points", "Rebounds"]}}], "possible_answer": [{"player_stats.getLastGame": {"player_name": ["LeBron James"], "team": ["Los Angeles Lakers", "LAL", "Lakers"], "metrics": [["Points", "Rebounds"]]}}]}
{"id": "simple_305", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'tournament': 'La Liga 2020-2021'. Expected one of ['La Liga']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_305", "question": [[{"role": "user", "content": "Calculate the overall goal and assist of soccer player Messi in La Liga 2020-2021 season"}]], "function": [{"name": "sports_stats.get_performance", "description": "Compute the performance score of a soccer player given his game stats for a specific tournament in a season.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "Name of the player."}, "tournament": {"type": "string", "description": "Name of the soccer tournament."}, "season": {"type": "string", "description": "Specific season in format 'YYYY-YYYY'."}, "performance_indicator": {"type": "array", "items": {"type": "string", "enum": ["Goals Scored", "Assists Made", "Saves Made", "Cards Received"]}, "description": "Array of performance indicators. Use as much as possible. Default to use all if not specified."}}, "required": ["player_name", "tournament", "season"]}}]}, "model_result_raw": [{"sports_stats_get_performance": "{\"player_name\": \"Messi\", \"tournament\": \"La Liga 2020-2021\", \"season\": \"2020-2021\", \"performance_indicator\": [\"Goals Scored\", \"Assists Made\"]}"}], "model_result_decoded": [{"sports_stats_get_performance": {"player_name": "Messi", "tournament": "La Liga 2020-2021", "season": "2020-2021", "performance_indicator": ["Goals Scored", "Assists Made"]}}], "possible_answer": [{"sports_stats.get_performance": {"player_name": ["Messi", "Lionel Messi"], "tournament": ["La Liga"], "season": ["2020-2021"], "performance_indicator": [["Goals Scored", "Assists Made"]]}}]}
{"id": "simple_323", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'sport': 'women tennis'. Expected one of ['tennis']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_323", "question": [[{"role": "user", "content": "Who is ranked as the top player in woman tennis?"}]], "function": [{"name": "sports_ranking.get_top_player", "description": "Get the top player in a specific sport.", "parameters": {"type": "dict", "properties": {"sport": {"type": "string", "description": "The type of sport."}, "gender": {"type": "string", "description": "The gender of the sport category. Optional.", "default": "men"}}, "required": ["sport"]}}]}, "model_result_raw": [{"sports_ranking_get_top_player": "{\"sport\": \"women tennis\", \"gender\": \"women\"}"}], "model_result_decoded": [{"sports_ranking_get_top_player": {"sport": "women tennis", "gender": "women"}}], "possible_answer": [{"sports_ranking.get_top_player": {"sport": ["tennis"], "gender": ["women"]}}]}
{"id": "simple_351", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'rating': 4.6. Expected one of [4.5]."], "error_type": "value_error:others", "prompt": {"id": "simple_351", "question": [[{"role": "user", "content": "Find me a multiplayer game with rating above 4.5 and compatible with Windows 10."}]], "function": [{"name": "multiplayer_game_finder", "description": "Locate multiplayer games that match specific criteria such as rating, platform compatibility, genre, etc.", "parameters": {"type": "dict", "properties": {"platform": {"type": "string", "description": "The platform you want the game to be compatible with, e.g. Windows 10, PS5."}, "rating": {"type": "float", "description": "Desired minimum game rating on a 5.0 scale."}, "genre": {"type": "string", "description": "Desired game genre, e.g. Action, Adventure, Racing. Default is 'Action'.", "enum": ["Action", "Adventure", "Racing", "Strategy", "Simulation"]}}, "required": ["platform", "rating"]}}]}, "model_result_raw": [{"multiplayer_game_finder": "{\"platform\": \"Windows 10\", \"rating\": 4.6}"}], "model_result_decoded": [{"multiplayer_game_finder": {"platform": "Windows 10", "rating": 4.6}}], "possible_answer": [{"multiplayer_game_finder": {"platform": ["Windows 10"], "rating": [4.5], "genre": ["", "Action"]}}]}
{"id": "simple_354", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'cooking_time': 29. Expected one of [30]."], "error_type": "value_error:others", "prompt": {"id": "simple_354", "question": [[{"role": "user", "content": "Find a vegan soup recipe that takes under 30 minutes to make."}]], "function": [{"name": "get_vegan_recipe", "description": "Retrieve a vegan soup recipe based on the provided cooking time.", "parameters": {"type": "dict", "properties": {"dish_type": {"type": "string", "description": "The type of dish, e.g. soup, dessert, etc.", "enum": ["soup", "main dish", "dessert", "salad"]}, "cooking_time": {"type": "integer", "description": "The maximum cooking time for the recipe in minutes."}, "ingredient_preference": {"type": "array", "items": {"type": "string"}, "description": "Preferred ingredients to be included in the recipe, if any. Default to not use it if not provided."}}, "required": ["dish_type", "cooking_time"]}}]}, "model_result_raw": [{"get_vegan_recipe": "{\"dish_type\": \"soup\", \"cooking_time\": 29}"}], "model_result_decoded": [{"get_vegan_recipe": {"dish_type": "soup", "cooking_time": 29}}], "possible_answer": [{"get_vegan_recipe": {"dish_type": ["soup"], "cooking_time": [30], "ingredient_preference": [""]}}]}
{"id": "simple_358", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'dish'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_358", "question": [[{"role": "user", "content": "Find a gluten-free cookie recipe that takes less than 30 minutes to prepare."}]], "function": [{"name": "recipe_search", "description": "Search for a cooking recipe based on specific dietary needs and time constraint.", "parameters": {"type": "dict", "properties": {"diet": {"type": "array", "items": {"type": "string", "enum": ["Gluten Free", "Dairy Free", "Vegan", "Vegetarian"]}, "description": "Specific dietary need."}, "time_limit": {"type": "integer", "description": "The maximum time to prepare the recipe in minutes. Default is 60 minutes."}, "dish": {"type": "string", "description": "The name of the dish to search for. Default is not use if not specified."}}, "required": ["dish", "diet"]}}]}, "model_result_raw": [{"recipe_search": "{\"diet\": [\"Gluten Free\"], \"time_limit\": 29}"}], "model_result_decoded": [{"recipe_search": {"diet": ["Gluten Free"], "time_limit": 29}}], "possible_answer": [{"recipe_search": {"diet": [["Gluten Free"], ["GF"], ["gluten free"]], "time_limit": [30], "dish": ["cookie"]}}]}
{"id": "simple_360", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'maxCalories': 499. Expected one of [500]."], "error_type": "value_error:others", "prompt": {"id": "simple_360", "question": [[{"role": "user", "content": "Find a recipe for pasta carbonara which contains only less than 500 calories."}]], "function": [{"name": "find_recipe", "description": "Locate a recipe based on name and its calorie content", "parameters": {"type": "dict", "properties": {"recipeName": {"type": "string", "description": "The recipe's name."}, "maxCalories": {"type": "integer", "description": "The maximum calorie content of the recipe.", "default": 1000}}, "required": ["recipeName"]}}]}, "model_result_raw": [{"find_recipe": "{\"recipeName\": \"pasta carbonara\", \"maxCalories\": 499}"}], "model_result_decoded": [{"find_recipe": {"recipeName": "pasta carbonara", "maxCalories": 499}}], "possible_answer": [{"find_recipe": {"recipeName": ["pasta carbonara"], "maxCalories": [500]}}]}
{"id": "simple_362", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'review_rate': 0.8. Expected one of [4.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_362", "question": [[{"role": "user", "content": "What are the top five sushi restaurants with high reviews i.e. above 4/5 in Tokyo?"}]], "function": [{"name": "get_best_sushi_places", "description": "Returns the best sushi places given the city, review_rate and top number.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city in which to look for the sushi places."}, "top": {"type": "integer", "description": "The number of top sushi places to be returned."}, "review_rate": {"type": "float", "description": "The review rating to filter the sushi places. Places with review ratings above this value will be returned. Default 0.00."}}, "required": ["city", "top"]}}]}, "model_result_raw": [{"get_best_sushi_places": "{\"city\": \"Tokyo\", \"top\": 5, \"review_rate\": 0.8}"}], "model_result_decoded": [{"get_best_sushi_places": {"city": "Tokyo", "top": 5, "review_rate": 0.8}}], "possible_answer": [{"get_best_sushi_places": {"city": ["Tokyo"], "top": [5], "review_rate": [4.0]}}]}
{"id": "simple_369", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'categories': ['Organic', 'Vegetables']. Expected one of [['Organic', 'Vegetables', 'Fruits'], ['Organic', 'Fruits', 'Vegetables'], ['Vegetables', 'Fruits', 'Organic'], ['Fruits', 'Vegetables', 'Organic'], ['Fruits', 'Organic', 'Vegetables'], ['Vegetables', 'Organic', 'Fruits']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_369", "question": [[{"role": "user", "content": "Find a grocery store near me with organic fruits and vegetables in Houston."}]], "function": [{"name": "grocery_store.find_nearby", "description": "Locate nearby grocery stores based on specific criteria like organic fruits and vegetables.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Houston, TX"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["Organic", "Vegetables", "Fruits", "Dairy", "Seafood", "Bakery"]}, "description": "Categories of items to be found in the grocery store. Default is all if not specified."}}, "required": ["location"]}}]}, "model_result_raw": [{"grocery_store_find_nearby": "{\"location\": \"Houston, TX\", \"categories\": [\"Organic\", \"Vegetables\"]}"}], "model_result_decoded": [{"grocery_store_find_nearby": {"location": "Houston, TX", "categories": ["Organic", "Vegetables"]}}], "possible_answer": [{"grocery_store.find_nearby": {"location": ["Houston", "Houston, TX"], "categories": [["Organic", "Vegetables", "Fruits"], ["Organic", "Fruits", "Vegetables"], ["Vegetables", "Fruits", "Organic"], ["Fruits", "Vegetables", "Organic"], ["Fruits", "Organic", "Vegetables"], ["Vegetables", "Organic", "Fruits"]]}}]}
{"id": "simple_370", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'quantity': [3, 5]. Expected one of [[3, 1]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": [{"safeway_order": "{\"location\": \"Palo Alto\", \"items\": [\"olive oil\", \"rice\"], \"quantity\": [3, 5]}"}], "model_result_decoded": [{"safeway_order": {"location": "Palo Alto", "items": ["olive oil", "rice"], "quantity": [3, 5]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_373", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'pack_size': [12]. Expected one of [[1, 1, 12]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_373", "question": [[{"role": "user", "content": "I want to buy apples, rice, and 12 pack of bottled water from a Walmart near San Jose. Show me the product information and stock availability."}]], "function": [{"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is not use it if not specified."}}, "required": ["loc", "product_list"]}}]}, "model_result_raw": [{"walmart_purchase": "{\"loc\": \"San Jose\", \"product_list\": [\"apples\", \"rice\", \"bottled water\"], \"pack_size\": [12]}"}], "model_result_decoded": [{"walmart_purchase": {"loc": "San Jose", "product_list": ["apples", "rice", "bottled water"], "pack_size": [12]}}], "possible_answer": [{"walmart.purchase": {"loc": ["San Jose", "San Jose, CA"], "product_list": [["apples", "rice", "bottled water"], ["apples", "rice", "water"]], "pack_size": [[1, 1, 12]]}}]}
{"id": "simple_375", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'items': ['pumpkin', 'pumpkin', 'pumpkin']. Expected one of [['pumpkins', 'eggs'], ['pumpkin', 'dozen eggs']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_375", "question": [[{"role": "user", "content": "Check the total price for three pumpkins and two dozen eggs at Walmart."}]], "function": [{"name": "walmart.check_price", "description": "Calculate total price for given items and their quantities at Walmart.", "parameters": {"type": "dict", "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "List of items to be priced."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item corresponding to the items list."}, "store_location": {"type": "string", "description": "The store location for specific pricing (optional). Default to all if not specified."}}, "required": ["items", "quantities"]}}]}, "model_result_raw": [{"walmart_check_price": "{\"items\": [\"pumpkin\", \"pumpkin\", \"pumpkin\"], \"quantities\": [3, 24]}"}], "model_result_decoded": [{"walmart_check_price": {"items": ["pumpkin", "pumpkin", "pumpkin"], "quantities": [3, 24]}}], "possible_answer": [{"walmart.check_price": {"items": [["pumpkins", "eggs"], ["pumpkin", "dozen eggs"]], "quantities": [[3, 24], [3, 2]], "store_location": [""]}}]}
{"id": "simple_376", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'display_format' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_376", "question": [[{"role": "user", "content": "What time is it currently in London, UK in 24 hour format?"}]], "function": [{"name": "time_zone_converter", "description": "Retrieve the current time of a specific city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city you want to know the current time for."}, "country": {"type": "string", "description": "The country where the city is located."}, "display_format": {"type": "string", "description": "The time display format: '12h' or '24h'. Default is '24h'."}}, "required": ["city", "country"]}}]}, "model_result_raw": [{"time_zone_converter": "{\"city\": \"London\", \"country\": \"UK\"}"}], "model_result_decoded": [{"time_zone_converter": {"city": "London", "country": "UK"}}], "possible_answer": [{"time_zone_converter": {"city": ["London"], "country": ["UK", "United Kingdom"], "display_format": ["24h", "24 hour"]}}]}
{"id": "simple_382", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'start_date': '10th December 2022'. Expected one of ['2022-12-10', '10/12/2022', 'Dec 10, 2022', 'December 10, 2022']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_382", "question": [[{"role": "user", "content": "Book a single room for two nights at the Hilton Hotel in Chicago, starting from 10th December 2022."}]], "function": [{"name": "book_hotel", "description": "Book a room of specified type for a particular number of nights at a specific hotel, starting from a specified date.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city in which the hotel is located."}, "room_type": {"type": "string", "description": "The type of room to be booked."}, "start_date": {"type": "string", "description": "The start date for the booking."}, "nights": {"type": "integer", "description": "The number of nights for which the booking is to be made."}}, "required": ["hotel_name", "location", "room_type", "start_date", "nights"]}}]}, "model_result_raw": [{"book_hotel": "{\"hotel_name\": \"Hilton Hotel\", \"location\": \"Chicago\", \"room_type\": \"single\", \"start_date\": \"10th December 2022\", \"nights\": 2}"}], "model_result_decoded": [{"book_hotel": {"hotel_name": "Hilton Hotel", "location": "Chicago", "room_type": "single", "start_date": "10th December 2022", "nights": 2}}], "possible_answer": [{"book_hotel": {"hotel_name": ["Hilton Hotel", "Hilton"], "location": ["Chicago"], "room_type": ["single"], "start_date": ["2022-12-10", "10/12/2022", "Dec 10, 2022", "December 10, 2022"], "nights": [2]}}]}
{"id": "simple_386", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'view' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_386", "question": [[{"role": "user", "content": "Book a luxury room in Hotel Paradise, Las Vegas, with a city view for 3 days starting from May 12, 2022."}]], "function": [{"name": "book_hotel", "description": "Book a room in a specific hotel with particular preferences", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The location of the hotel."}, "room_type": {"type": "string", "description": "The type of room preferred."}, "start_date": {"type": "string", "description": "The starting date of the stay in format MM-DD-YYYY."}, "stay_duration": {"type": "integer", "description": "The duration of the stay in days."}, "view": {"type": "string", "description": "The preferred view from the room, can be ignored if no preference. If none provided, assumes no preference.", "default": "No preference"}}, "required": ["hotel_name", "location", "room_type", "start_date", "stay_duration"]}}]}, "model_result_raw": [{"book_hotel": "{\"hotel_name\": \"Hotel Paradise\", \"location\": \"Las Vegas\", \"room_type\": \"luxury\", \"start_date\": \"05-12-2022\", \"stay_duration\": 3}"}], "model_result_decoded": [{"book_hotel": {"hotel_name": "Hotel Paradise", "location": "Las Vegas", "room_type": "luxury", "start_date": "05-12-2022", "stay_duration": 3}}], "possible_answer": [{"book_hotel": {"hotel_name": ["Hotel Paradise"], "location": ["Las Vegas", "LV"], "room_type": ["luxury", "Luxury"], "start_date": ["05-12-2022", "2022-05-12", "12/05/2022", "May.12,2022", "May 12, 2022"], "stay_duration": [3], "view": ["city view", "city"]}}]}
{"id": "simple_387", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'end_date': '2022-06-03'. Expected one of ['2022-06-04', '06/04/2022', 'Jun.4,2022']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_387", "question": [[{"role": "user", "content": "Book a hotel room at the Plaza Hotel in New York for 3 nights starting from 1st June 2022"}]], "function": [{"name": "hotel_booking", "description": "Books a hotel room for a specific date range.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY."}, "start_date": {"type": "string", "description": "The start date of the reservation. Use format 'YYYY-MM-DD'."}, "end_date": {"type": "string", "description": "The end date of the reservation. Use format 'YYYY-MM-DD'."}, "rooms": {"type": "integer", "default": 1, "description": "The number of rooms to reserve."}}, "required": ["hotel_name", "location", "start_date", "end_date"]}}]}, "model_result_raw": [{"hotel_booking": "{\"hotel_name\": \"Plaza Hotel\", \"location\": \"New York, NY\", \"start_date\": \"2022-06-01\", \"end_date\": \"2022-06-03\"}"}], "model_result_decoded": [{"hotel_booking": {"hotel_name": "Plaza Hotel", "location": "New York, NY", "start_date": "2022-06-01", "end_date": "2022-06-03"}}], "possible_answer": [{"hotel_booking": {"hotel_name": ["Plaza Hotel"], "location": ["New York City, NY", "New York, NY"], "start_date": ["2022-06-01", "06/01/2022", "Jun.1,2022"], "end_date": ["2022-06-04", "06/04/2022", "Jun.4,2022"], "rooms": [1, ""]}}]}
{"id": "simple_388", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'base_currency': 'US'. Expected one of ['USD']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_388", "question": [[{"role": "user", "content": "How many Canadian dollars can I get for 500 US dollars?"}]], "function": [{"name": "currency_exchange.convert", "description": "Convert an amount from a base currency to a target currency based on the current exchange rate.", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The currency to convert from."}, "target_currency": {"type": "string", "description": "The currency to convert to."}, "amount": {"type": "integer", "description": "The amount in base currency to convert"}}, "required": ["base_currency", "target_currency", "amount"]}}]}, "model_result_raw": [{"currency_exchange_convert": "{\"base_currency\": \"US\", \"target_currency\": \"CAD\", \"amount\": 500}"}], "model_result_decoded": [{"currency_exchange_convert": {"base_currency": "US", "target_currency": "CAD", "amount": 500}}], "possible_answer": [{"currency_exchange.convert": {"base_currency": ["USD"], "target_currency": ["CAD"], "amount": [500]}}]}
{"id": "simple_399", "model_name": "Qwen_Qwen3-0.6B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'rating': 5. Expected one of [4]."], "error_type": "value_error:others", "prompt": {"id": "simple_399", "question": [[{"role": "user", "content": "Find me the best Italian restaurants in New York City with average customer ratings of more than 4 and accepts credit cards."}]], "function": [{"name": "restaurant_search", "description": "Locates top rated restaurants based on specific criteria such as type of cuisine, ratings, and facilities.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York City, NY"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine e.g., Italian, Indian, American, etc."}, "rating": {"type": "integer", "description": "Minimum average customer rating out of 5"}, "accepts_credit_cards": {"type": "boolean", "description": "If the restaurant should accept credit cards."}}, "required": ["location", "cuisine", "rating", "accepts_credit_cards"]}}]}, "model_result_raw": [{"restaurant_search": "{\"location\": \"New York City, NY\", \"cuisine\": \"Italian\", \"rating\": 5, \"accepts_credit_cards\": true}"}], "model_result_decoded": [{"restaurant_search": {"location": "New York City, NY", "cuisine": "Italian", "rating": 5, "accepts_credit_cards": true}}], "possible_answer": [{"restaurant_search": {"location": ["New York City", "New York City, NY", "NYC"], "cuisine": ["Italian"], "rating": [4], "accepts_credit_cards": [true]}}]}