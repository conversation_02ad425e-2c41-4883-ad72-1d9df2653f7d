{"accuracy": 0.5625, "correct_count": 225, "total_count": 400}
{"id": "simple_1", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'math_factorial' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_1", "question": [[{"role": "user", "content": "Calculate the factorial of 5 using math functions."}]], "function": [{"name": "math.factorial", "description": "Calculate the factorial of a given number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number for which factorial needs to be calculated."}}, "required": ["number"]}}]}, "model_result_raw": "\n\n[math.factorial(number=5)]", "model_result_decoded": [{"math.factorial": {"number": 5}}], "possible_answer": [{"math.factorial": {"number": [5]}}]}
{"id": "simple_2", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'math_hypot' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_2", "question": [[{"role": "user", "content": "Calculate the hypotenuse of a right triangle given the lengths of the other two sides as 4 and 5."}]], "function": [{"name": "math.hypot", "description": "Calculate the Euclidean norm, sqrt(sum(squares)), the length of the vector from the origin to point (x, y) which is the hypotenuse of the right triangle.", "parameters": {"type": "dict", "properties": {"x": {"type": "integer", "description": "The x-coordinate value."}, "y": {"type": "integer", "description": "The y-coordinate value."}, "z": {"type": "integer", "description": "Optional. The z-coordinate value. Default is 0."}}, "required": ["x", "y"]}}]}, "model_result_raw": "\n\n[math.hypot(x=4, y=5)]", "model_result_decoded": [{"math.hypot": {"x": 4, "y": 5}}], "possible_answer": [{"math.hypot": {"x": [4], "y": [5], "z": ["", 0]}}]}
{"id": "simple_3", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'algebra_quadratic_roots' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_3", "question": [[{"role": "user", "content": "Find the roots of a quadratic equation with coefficients a=1, b=-3, c=2."}]], "function": [{"name": "algebra.quadratic_roots", "description": "Find the roots of a quadratic equation ax^2 + bx + c = 0.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of x^2."}, "b": {"type": "integer", "description": "Coefficient of x."}, "c": {"type": "integer", "description": "Constant term."}}, "required": ["a", "b", "c"]}}]}, "model_result_raw": "\n\n[algebra.quadratic_roots(a=1, b=-3, c=2)]", "model_result_decoded": [{"algebra.quadratic_roots": {"a": 1, "b": -3, "c": 2}}], "possible_answer": [{"algebra.quadratic_roots": {"a": [1], "b": [-3], "c": [2]}}]}
{"id": "simple_8", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'geometry_area_circle' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_8", "question": [[{"role": "user", "content": "What's the area of a circle with a radius of 10?"}]], "function": [{"name": "geometry.area_circle", "description": "Calculate the area of a circle given the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}, "units": {"type": "string", "description": "The units in which the radius is measured (defaults to 'meters')."}}, "required": ["radius"]}}]}, "model_result_raw": "\n\n[geometry.area_circle(radius=10)]", "model_result_decoded": [{"geometry.area_circle": {"radius": 10}}], "possible_answer": [{"geometry.area_circle": {"radius": [10], "units": ["meters", ""]}}]}
{"id": "simple_9", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'geometry_calculate_area_circle' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_9", "question": [[{"role": "user", "content": "Calculate the area of a circle with a radius of 5 units."}]], "function": [{"name": "geometry.calculate_area_circle", "description": "Calculate the area of a circle given its radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}, "unit": {"type": "string", "description": "The measurement unit of the radius (optional parameter, default is 'units')."}}, "required": ["radius"]}}]}, "model_result_raw": "\n\n[geometry.calculate_area_circle(radius=5, unit='units')]", "model_result_decoded": [{"geometry.calculate_area_circle": {"radius": 5, "unit": "units"}}], "possible_answer": [{"geometry.calculate_area_circle": {"radius": [5], "unit": ["units", ""]}}]}
{"id": "simple_12", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'geometry_circumference' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_12", "question": [[{"role": "user", "content": "Calculate the circumference of a circle with radius 3"}]], "function": [{"name": "geometry.circumference", "description": "Calculate the circumference of a circle given the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}, "units": {"type": "string", "description": "Units for the output circumference measurement. Default is 'cm'."}}, "required": ["radius"]}}]}, "model_result_raw": "\n\n[geometry.circumference(radius=3)]", "model_result_decoded": [{"geometry.circumference": {"radius": 3}}], "possible_answer": [{"geometry.circumference": {"radius": [3], "units": ["cm", ""]}}]}
{"id": "simple_13", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": "\n\n[calculate_area_under_curve(function=\"x**2\", interval=[1, 3])]", "model_result_decoded": [{"calculate_area_under_curve": {"function": "x**2", "interval": [1, 3]}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_16", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'calculus_derivative' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_16", "question": [[{"role": "user", "content": "Calculate the derivative of the function 2x^2 at x = 1."}]], "function": [{"name": "calculus.derivative", "description": "Compute the derivative of a function at a specific value.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The function to calculate the derivative of."}, "value": {"type": "integer", "description": "The value where the derivative needs to be calculated at."}, "function_variable": {"type": "string", "description": "The variable present in the function, for instance x or y, etc. Default is 'x'."}}, "required": ["function", "value"]}}]}, "model_result_raw": "\n\n[calculus.derivative(function=\"2*x**2\", value=1, function_variable=\"x\")]", "model_result_decoded": [{"calculus.derivative": {"function": "2*x**2", "value": 1, "function_variable": "x"}}], "possible_answer": [{"calculus.derivative": {"function": ["2x**2", "lambda x: 2x**2"], "value": [1], "function_variable": ["x", ""]}}]}
{"id": "simple_18", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'number_analysis_prime_factors' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_18", "question": [[{"role": "user", "content": "Find the prime factors of the number 123456."}]], "function": [{"name": "number_analysis.prime_factors", "description": "Compute the prime factors of a number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number to be factored."}}, "required": ["number"]}}]}, "model_result_raw": "\n\n[number_analysis.prime_factors(number=123456)]", "model_result_decoded": [{"number_analysis.prime_factors": {"number": 123456}}], "possible_answer": [{"number_analysis.prime_factors": {"number": [123456]}}]}
{"id": "simple_19", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'math_gcd' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_19", "question": [[{"role": "user", "content": "Calculate the greatest common divisor of two numbers: 40 and 50"}]], "function": [{"name": "math.gcd", "description": "Compute the greatest common divisor of two numbers", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "The first number."}, "num2": {"type": "integer", "description": "The second number."}}, "required": ["num1", "num2"]}}]}, "model_result_raw": "\n\n[math.gcd(num1=40, num2=50)]", "model_result_decoded": [{"math.gcd": {"num1": 40, "num2": 50}}], "possible_answer": [{"math.gcd": {"num1": [40], "num2": [50]}}]}
{"id": "simple_20", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'math_hcf' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_20", "question": [[{"role": "user", "content": "Find the highest common factor of 36 and 24."}]], "function": [{"name": "math.hcf", "description": "Calculate the highest common factor of two numbers.", "parameters": {"type": "dict", "properties": {"number1": {"type": "integer", "description": "First number."}, "number2": {"type": "integer", "description": "Second number."}}, "required": ["number1", "number2"]}}]}, "model_result_raw": "\n\n[math.hcf(number1=36, number2=24)]", "model_result_decoded": [{"math.hcf": {"number1": 36, "number2": 24}}], "possible_answer": [{"math.hcf": {"number1": [36], "number2": [24]}}]}
{"id": "simple_21", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'number_theory_gcd' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_21", "question": [[{"role": "user", "content": "Find the Greatest Common Divisor (GCD) of two numbers, say 36 and 48."}]], "function": [{"name": "number_theory.gcd", "description": "Compute the greatest common divisor of two given integers.", "parameters": {"type": "dict", "properties": {"number1": {"type": "integer", "description": "The first integer."}, "number2": {"type": "integer", "description": "The second integer."}}, "required": ["number1", "number2"]}}]}, "model_result_raw": "\n\n[number_theory.gcd(number1=36, number2=48)]", "model_result_decoded": [{"number_theory.gcd": {"number1": 36, "number2": 48}}], "possible_answer": [{"number_theory.gcd": {"number1": [36], "number2": [48]}}]}
{"id": "simple_22", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'math_gcd' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_22", "question": [[{"role": "user", "content": "Calculate the greatest common divisor of two given numbers, for example 12 and 15."}]], "function": [{"name": "math.gcd", "description": "Calculate the greatest common divisor (gcd) of the two integers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "The first number."}, "num2": {"type": "integer", "description": "The second number."}}, "required": ["num1", "num2"]}}]}, "model_result_raw": "\n\n[math.gcd(num1=12, num2=15)]", "model_result_decoded": [{"math.gcd": {"num1": 12, "num2": 15}}], "possible_answer": [{"math.gcd": {"num1": [12], "num2": [15]}}]}
{"id": "simple_24", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'math_gcd' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_24", "question": [[{"role": "user", "content": "Find the greatest common divisor (GCD) of 12 and 18"}]], "function": [{"name": "math.gcd", "description": "Calculate the greatest common divisor of two integers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "First number."}, "num2": {"type": "integer", "description": "Second number."}}, "required": ["num1", "num2"]}}]}, "model_result_raw": "\n\n[math.gcd(num1=12, num2=18)]", "model_result_decoded": [{"math.gcd": {"num1": 12, "num2": 18}}], "possible_answer": [{"math.gcd": {"num1": [12], "num2": [18]}}]}
{"id": "simple_30", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'kinematics_final_velocity_from_distance' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_30", "question": [[{"role": "user", "content": "What is the final velocity of a vehicle that started from rest and accelerated at 4 m/s^2 for a distance of 300 meters?"}]], "function": [{"name": "kinematics.final_velocity_from_distance", "description": "Calculate the final velocity of an object given the acceleration and distance travelled, assuming initial velocity is 0.", "parameters": {"type": "dict", "properties": {"acceleration": {"type": "integer", "description": "Acceleration of the object, m/s^2."}, "distance": {"type": "integer", "description": "Distance traveled by the object, m."}, "initial_velocity": {"type": "float", "description": "Initial velocity of the object. Default is 0, m/s"}}, "required": ["acceleration", "distance"]}}]}, "model_result_raw": "\n\n[kinematics.final_velocity_from_distance(acceleration=4, distance=300)]", "model_result_decoded": [{"kinematics.final_velocity_from_distance": {"acceleration": 4, "distance": 300}}], "possible_answer": [{"kinematics.final_velocity_from_distance": {"acceleration": [4], "distance": [300], "initial_velocity": ["", 0.0]}}]}
{"id": "simple_35", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'vegan_restaurant_find_nearby' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_35", "question": [[{"role": "user", "content": "Find an all vegan restaurant in New York that opens until at least 11 PM."}]], "function": [{"name": "vegan_restaurant.find_nearby", "description": "Locate nearby vegan restaurants based on specific criteria like operating hours.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York, NY, you should format it as City, State."}, "operating_hours": {"type": "integer", "description": "Preferred latest closing time of the restaurant. E.g. if 11 is given, then restaurants that close at or after 11 PM will be considered. This is in 24 hour format. Default is 24."}}, "required": ["location"]}}]}, "model_result_raw": "\n\n[vegan_restaurant.find_nearby(location=\"New York, NY\", operating_hours=11)]", "model_result_decoded": [{"vegan_restaurant.find_nearby": {"location": "New York, NY", "operating_hours": 11}}], "possible_answer": [{"vegan_restaurant.find_nearby": {"location": ["New York, NY"], "operating_hours": [23]}}]}
{"id": "simple_37", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'route_estimate_time' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_37", "question": [[{"role": "user", "content": "Find the estimated travel time by car from San Francisco to Los Angeles with stops at Santa Barbara and Monterey."}]], "function": [{"name": "route.estimate_time", "description": "Estimate the travel time for a specific route with optional stops.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting point for the journey. It should be format as city name such as Boston."}, "end_location": {"type": "string", "description": "The destination for the journey. It should be format as city name such as Boston."}, "stops": {"type": "array", "items": {"type": "string"}, "description": "Additional cities or points of interest to stop at during the journey. Default is an empty list."}}, "required": ["start_location", "end_location"]}}]}, "model_result_raw": "\n\n[route.estimate_time(start_location=\"San Francisco\", end_location=\"Los Angeles\", stops=[\"Santa Barbara\", \"Monterey\"])]", "model_result_decoded": [{"route.estimate_time": {"start_location": "San Francisco", "end_location": "Los Angeles", "stops": ["Santa Barbara", "Monterey"]}}], "possible_answer": [{"route.estimate_time": {"start_location": ["San Francisco"], "end_location": ["Los Angeles"], "stops": [["Santa Barbara", "Monterey"], ["Monterey", "Santa Barbara"]]}}]}
{"id": "simple_45", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'thermo_calculate_energy' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_45", "question": [[{"role": "user", "content": "Calculate the energy (in Joules) absorbed or released during the phase change of 100g of water from liquid to steam at its boiling point."}]], "function": [{"name": "thermo.calculate_energy", "description": "Calculate the energy required or released during a phase change using mass, the phase transition temperature and the specific latent heat.", "parameters": {"type": "dict", "properties": {"mass": {"type": "integer", "description": "Mass of the substance in grams."}, "phase_transition": {"type": "string", "description": "Phase transition. Can be 'melting', 'freezing', 'vaporization', 'condensation'."}, "substance": {"type": "string", "description": "The substance which is undergoing phase change, default is 'water'"}}, "required": ["mass", "phase_transition"]}}]}, "model_result_raw": "\n\n[thermo.calculate_energy(mass=100, phase_transition='vaporization', substance='water')]", "model_result_decoded": [{"thermo.calculate_energy": {"mass": 100, "phase_transition": "vaporization", "substance": "water"}}], "possible_answer": [{"thermo.calculate_energy": {"mass": [100], "phase_transition": ["vaporization"], "substance": ["water", ""]}}]}
{"id": "simple_50", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'entropy_change_calculate' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_50", "question": [[{"role": "user", "content": "What is the change in entropy in Joules per Kelvin of a 1kg ice block at 0\u00b0C if it is heated to 100\u00b0C under 1 atmosphere of pressure?"}]], "function": [{"name": "entropy_change.calculate", "description": "Calculate the change in entropy for a mass of a specific substance under set initial and final conditions.", "parameters": {"type": "dict", "properties": {"substance": {"type": "string", "description": "The substance for which the change in entropy is calculated."}, "mass": {"type": "integer", "description": "The mass of the substance in kg."}, "initial_temperature": {"type": "integer", "description": "The initial temperature of the substance in degree Celsius."}, "final_temperature": {"type": "integer", "description": "The final temperature of the substance in degree Celsius."}, "pressure": {"type": "integer", "default": 1, "description": "The pressure the substance is under in atmospheres."}}, "required": ["substance", "mass", "initial_temperature", "final_temperature"]}}]}, "model_result_raw": "\n\n[entropy_change.calculate(substance='ice', mass=1, initial_temperature=0, final_temperature=100)]", "model_result_decoded": [{"entropy_change.calculate": {"substance": "ice", "mass": 1, "initial_temperature": 0, "final_temperature": 100}}], "possible_answer": [{"entropy_change.calculate": {"substance": ["ice"], "mass": [1], "initial_temperature": [0], "final_temperature": [100], "pressure": ["", 1]}}]}
{"id": "simple_55", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'biology_get_cell_info' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_55", "question": [[{"role": "user", "content": "Find me detailed information about the structure of human cell"}]], "function": [{"name": "biology.get_cell_info", "description": "Retrieve information about the structure and functioning of a specified type of cell", "parameters": {"type": "dict", "properties": {"cell_type": {"type": "string", "description": "Type of cell you want information about"}, "detailed": {"type": "boolean", "description": "Indicate if you want a detailed description of the cell", "default": "false"}}, "required": ["cell_type"]}}]}, "model_result_raw": "\n\n[biology.get_cell_info(cell_type=\"human cell\", detailed=True)]", "model_result_decoded": [{"biology.get_cell_info": {"cell_type": "human cell", "detailed": true}}], "possible_answer": [{"biology.get_cell_info": {"cell_type": ["human"], "detailed": [true]}}]}
{"id": "simple_56", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'cellbio_get_proteins' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_56", "question": [[{"role": "user", "content": "What are the names of proteins found in the plasma membrane?"}]], "function": [{"name": "cellbio.get_proteins", "description": "Get the list of proteins in a specific cell compartment.", "parameters": {"type": "dict", "properties": {"cell_compartment": {"type": "string", "description": "The specific cell compartment."}, "include_description": {"type": "boolean", "description": "Set true if you want a brief description of each protein.", "default": "false"}}, "required": ["cell_compartment"]}}]}, "model_result_raw": "\n\n[cellbio.get_proteins(cell_compartment=\"plasma membrane\", include_description=False)]", "model_result_decoded": [{"cellbio.get_proteins": {"cell_compartment": "plasma membrane", "include_description": false}}], "possible_answer": [{"cellbio.get_proteins": {"cell_compartment": ["plasma membrane"], "include_description": ["", true, false]}}]}
{"id": "simple_58", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'cell_biology_function_lookup' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_58", "question": [[{"role": "user", "content": "What is the function of ATP synthase in mitochondria?"}]], "function": [{"name": "cell_biology.function_lookup", "description": "Look up the function of a given molecule in a specified organelle.", "parameters": {"type": "dict", "properties": {"molecule": {"type": "string", "description": "The molecule of interest."}, "organelle": {"type": "string", "description": "The organelle of interest."}, "specific_function": {"type": "boolean", "description": "If set to true, a specific function of the molecule within the organelle will be provided, if such information exists."}}, "required": ["molecule", "organelle", "specific_function"]}}]}, "model_result_raw": "\n\n[cell_biology.function_lookup(molecule=\"ATP synthase\", organelle=\"mitochondria\", specific_function=False)]", "model_result_decoded": [{"cell_biology.function_lookup": {"molecule": "ATP synthase", "organelle": "mitochondria", "specific_function": false}}], "possible_answer": [{"cell_biology.function_lookup": {"molecule": ["ATP synthase"], "organelle": ["mitochondria"], "specific_function": [true]}}]}
{"id": "simple_60", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'mutation_type_find' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_60", "question": [[{"role": "user", "content": "Find the type of gene mutation based on SNP (Single Nucleotide Polymorphism) ID rs6034464."}]], "function": [{"name": "mutation_type.find", "description": "Finds the type of a genetic mutation based on its SNP (Single Nucleotide Polymorphism) ID.", "parameters": {"type": "dict", "properties": {"snp_id": {"type": "string", "description": "The ID of the Single Nucleotide Polymorphism (SNP) mutation."}, "species": {"type": "string", "description": "Species in which the SNP occurs, default is 'Homo sapiens' (Humans)."}}, "required": ["snp_id"]}}]}, "model_result_raw": "\n\n[mutation_type.find(snp_id=\"rs6034464\")]", "model_result_decoded": [{"mutation_type.find": {"snp_id": "rs6034464"}}], "possible_answer": [{"mutation_type.find": {"snp_id": ["rs6034464"], "species": ["Homo sapiens", ""]}}]}
{"id": "simple_63", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'genetics_calculate_similarity' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_63", "question": [[{"role": "user", "content": "Find out how genetically similar a human and a chimp are in percentage."}]], "function": [{"name": "genetics.calculate_similarity", "description": "Calculates the genetic similarity between two species based on their DNA sequences.", "parameters": {"type": "dict", "properties": {"species1": {"type": "string", "description": "The first species to compare."}, "species2": {"type": "string", "description": "The second species to compare."}, "format": {"type": "string", "description": "The format of the result (percentage or fraction). Default is percentage."}}, "required": ["species1", "species2"]}}]}, "model_result_raw": "\n\n[genetics.calculate_similarity(species1=\"human\", species2=\"chimp\", format=\"percentage\")]", "model_result_decoded": [{"genetics.calculate_similarity": {"species1": "human", "species2": "chimp", "format": "percentage"}}], "possible_answer": [{"genetics.calculate_similarity": {"species1": ["Human", "human"], "species2": ["Chimp", "chimp", "Chimpanzee", "chimpanzee"], "format": ["percentage", ""]}}]}
{"id": "simple_66", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'ecology_data_precipitation_stats' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_66", "question": [[{"role": "user", "content": "Get me data on average precipitation in the Amazon rainforest for the last six months."}]], "function": [{"name": "ecology_data.precipitation_stats", "description": "Retrieve precipitation data for a specified location and time period.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location, e.g., 'Amazon rainforest'."}, "time_frame": {"type": "string", "enum": ["six_months", "year", "five_years"], "description": "The time period for which data is required."}}, "required": ["location", "time_frame"]}}]}, "model_result_raw": "\n\n[ecology_data.precipitation_stats(location=\"Amazon rainforest\", time_frame=\"six_months\")]", "model_result_decoded": [{"ecology_data.precipitation_stats": {"location": "Amazon rainforest", "time_frame": "six_months"}}], "possible_answer": [{"ecology_data.precipitation_stats": {"location": ["Amazon rainforest"], "time_frame": ["six_months"]}}]}
{"id": "simple_69", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'ecology_get_turtle_population' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_69", "question": [[{"role": "user", "content": "Find out the population and species of turtles in Mississippi river in 2020."}]], "function": [{"name": "ecology.get_turtle_population", "description": "Get the population and species of turtles in a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location."}, "year": {"type": "integer", "description": "The year of the data requested. Default is 2001."}, "species": {"type": "boolean", "description": "Whether to include species information. Default is false."}}, "required": ["location"]}}]}, "model_result_raw": "\n\n[ecology.get_turtle_population(location=\"Mississippi river\", year=2020, species=True)]", "model_result_decoded": [{"ecology.get_turtle_population": {"location": "Mississippi river", "year": 2020, "species": true}}], "possible_answer": [{"ecology.get_turtle_population": {"location": ["Mississippi river"], "year": [2020], "species": [true]}}]}
{"id": "simple_76", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'prediction_evolution' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_76", "question": [[{"role": "user", "content": "Get me the predictions of the evolutionary rate for Homo Sapiens for next 50 years using Darwin model"}]], "function": [{"name": "prediction.evolution", "description": "Predict the evolutionary rate for a specific species for a given timeframe.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species that the evolution rate will be predicted for."}, "years": {"type": "integer", "description": "Number of years for the prediction."}, "model": {"type": "string", "description": "The model used to make the prediction, options: 'Darwin', 'Lamarck', default is 'Darwin'."}}, "required": ["species", "years"]}}]}, "model_result_raw": "\n\n[prediction.evolution(species=\"Homo Sapiens\", years=50, model=\"Darwin\")]", "model_result_decoded": [{"prediction.evolution": {"species": "Homo Sapiens", "years": 50, "model": "Darwin"}}], "possible_answer": [{"prediction.evolution": {"species": ["Homo Sapiens", "homo sapiens", "Homo sapiens"], "years": [50], "model": ["Darwin"]}}]}
{"id": "simple_77", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'restaurant_find_nearby' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_77", "question": [[{"role": "user", "content": "Find a nearby restaurant that serves vegan food in Los Angeles."}]], "function": [{"name": "restaurant.find_nearby", "description": "Locate nearby restaurants based on specific dietary preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Los Angeles, CA"}, "dietary_preference": {"type": "array", "items": {"type": "string", "enum": ["Vegan", "Vegetarian", "Gluten-free", "Dairy-free", "Nut-free"]}, "description": "Dietary preference. Default is empty list."}}, "required": ["location"]}}]}, "model_result_raw": "\n\n[restaurant.find_nearby(location=\"Los Angeles, CA\", dietary_preference=[\"Vegan\"])]", "model_result_decoded": [{"restaurant.find_nearby": {"location": "Los Angeles, CA", "dietary_preference": ["Vegan"]}}], "possible_answer": [{"restaurant.find_nearby": {"location": ["Los Angeles, CA"], "dietary_preference": [["Vegan"]]}}]}
{"id": "simple_80", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'Manhattan, City'. Expected one of ['Manhattan, New York City', 'Manhattan', 'Manhattan, New York', 'Manhattan, NY', 'Manhattan, NYC']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_80", "question": [[{"role": "user", "content": "I want to find 5 restaurants nearby my location, Manhattan, offering Thai food and a vegan menu."}]], "function": [{"name": "find_restaurants", "description": "Locate nearby restaurants based on location and food preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The specific location or area. The location should be in the format of District, City."}, "food_type": {"type": "string", "description": "The type of food preferred."}, "number": {"type": "integer", "description": "Number of results to return."}, "dietary_requirements": {"type": "array", "items": {"type": "string"}, "description": "Special dietary requirements, e.g. vegan, gluten-free. Default is empty list."}}, "required": ["location", "food_type", "number"]}}]}, "model_result_raw": "\n\n[find_restaurants(location=\"Manhattan, City\", food_type=\"Thai\", number=5, dietary_requirements=[\"vegan\"])]", "model_result_decoded": [{"find_restaurants": {"location": "Manhattan, City", "food_type": "Thai", "number": 5, "dietary_requirements": ["vegan"]}}], "possible_answer": [{"find_restaurants": {"location": ["Manhattan, New York City", "Manhattan", "Manhattan, New York", "Manhattan, NY", "Manhattan, NYC"], "food_type": ["Thai"], "number": [5], "dietary_requirements": [["vegan"], ["Vegan"]]}}]}
{"id": "simple_81", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'map_routing_fastest_route' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_81", "question": [[{"role": "user", "content": "Find the fastest route from San Francisco to Los Angeles with toll roads avoided."}]], "function": [{"name": "map_routing.fastest_route", "description": "Finds the fastest route from one location to another, with an option to avoid toll roads.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting location for the journey."}, "end_location": {"type": "string", "description": "The destination for the journey."}, "avoid_tolls": {"type": "boolean", "description": "Option to avoid toll roads during the journey. Default is false."}}, "required": ["start_location", "end_location"]}}]}, "model_result_raw": "\n\n[map_routing.fastest_route(start_location=\"San Francisco\", end_location=\"Los Angeles\", avoid_tolls=True)]", "model_result_decoded": [{"map_routing.fastest_route": {"start_location": "San Francisco", "end_location": "Los Angeles", "avoid_tolls": true}}], "possible_answer": [{"map_routing.fastest_route": {"start_location": ["San Francisco", "SF"], "end_location": ["Los Angeles", "LA"], "avoid_tolls": [true]}}]}
{"id": "simple_82", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers'. Expected outer type array with inner type <class 'float'>. Parameter value: [12, 15, 18, 20, 21, 26, 30]."], "error_type": "type_error:nested", "prompt": {"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}, "model_result_raw": "\n\n[calculate_average(numbers=[12, 15, 18, 20, 21, 26, 30])]", "model_result_decoded": [{"calculate_average": {"numbers": [12, 15, 18, 20, 21, 26, 30]}}], "possible_answer": [{"calculate_average": {"numbers": [[12.0, 15.0, 18.0, 20.0, 21.0, 26.0, 30.0]]}}]}
{"id": "simple_85", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'geo_distance_calculate' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_85", "question": [[{"role": "user", "content": "What's the approximate distance between Boston, MA, and Washington, D.C. in mile?"}]], "function": [{"name": "geo_distance.calculate", "description": "Calculate the geographic distance between two given locations.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting location for the distance calculation. Specify the location in the format of City, State."}, "end_location": {"type": "string", "description": "The destination location for the distance calculation. Specify the location in the format of City, State."}, "units": {"type": "string", "description": "Optional. The desired units for the resulting distance ('miles' or 'kilometers'). Defaults to 'miles'."}}, "required": ["start_location", "end_location"]}}]}, "model_result_raw": "\n\n[geo_distance.calculate(start_location=\"Boston, MA\", end_location=\"Washington, D.C.\", units=\"miles\")]", "model_result_decoded": [{"geo_distance.calculate": {"start_location": "Boston, MA", "end_location": "Washington, D.C.", "units": "miles"}}], "possible_answer": [{"geo_distance.calculate": {"start_location": ["Boston, MA"], "end_location": ["Washington, D.C."], "units": ["miles", ""]}}]}
{"id": "simple_86", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'city_distance_find_shortest' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_86", "question": [[{"role": "user", "content": "Find the shortest distance between two cities, New York and Los Angeles, through the train and you can transfer."}]], "function": [{"name": "city_distance.find_shortest", "description": "Calculates the shortest distance between two cities via available public transportation.", "parameters": {"type": "dict", "properties": {"start_city": {"type": "string", "description": "The city you are starting from. The parameter is in the format of city name."}, "end_city": {"type": "string", "description": "The city you are heading to.The parameter is in the format of city name."}, "transportation": {"type": "string", "description": "Preferred mode of public transportation. Default is 'bus'."}, "allow_transfer": {"type": "boolean", "description": "Allows transfer between different transportation if true. Default is false."}}, "required": ["start_city", "end_city"]}}]}, "model_result_raw": "\n\n[city_distance.find_shortest(start_city=\"New York\", end_city=\"Los Angeles\", transportation=\"train\", allow_transfer=True)]", "model_result_decoded": [{"city_distance.find_shortest": {"start_city": "New York", "end_city": "Los Angeles", "transportation": "train", "allow_transfer": true}}], "possible_answer": [{"city_distance.find_shortest": {"start_city": ["New York"], "end_city": ["Los Angeles"], "transportation": ["train"], "allow_transfer": [true]}}]}
{"id": "simple_87", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'list'. Expected outer type array with inner type <class 'float'>. Parameter value: [5, 3, 4, 1, 2]."], "error_type": "type_error:nested", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": "\n\n[array_sort(list=[5, 3, 4, 1, 2], order='ascending')]", "model_result_decoded": [{"array_sort": {"list": [5, 3, 4, 1, 2], "order": "ascending"}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_90", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'employee_fetch_data' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_90", "question": [[{"role": "user", "content": "Retrieve Personal Info and Job History data of a specific employee whose ID is 345 in company 'ABC Ltd.'"}]], "function": [{"name": "employee.fetch_data", "description": "Fetches the detailed data for a specific employee in a given company.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "employee_id": {"type": "integer", "description": "The unique ID of the employee."}, "data_field": {"type": "array", "items": {"type": "string", "enum": ["Personal Info", "Job History", "Payroll", "Attendance"]}, "description": "Fields of data to be fetched for the employee (Optional). Default is ['Personal Info']"}}, "required": ["company_name", "employee_id"]}}]}, "model_result_raw": "\n\n[employee.fetch_data(company_name='ABC Ltd.', employee_id=345, data_field=['Personal Info', 'Job History'])]", "model_result_decoded": [{"employee.fetch_data": {"company_name": "ABC Ltd.", "employee_id": 345, "data_field": ["Personal Info", "Job History"]}}], "possible_answer": [{"employee.fetch_data": {"company_name": ["ABC Ltd."], "employee_id": [345], "data_field": [["Personal Info", "Job History"]]}}]}
{"id": "simple_92", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'imdb_find_movies_by_actor' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_92", "question": [[{"role": "user", "content": "Find all movies starring Leonardo DiCaprio in the year 2010 from IMDB database."}]], "function": [{"name": "imdb.find_movies_by_actor", "description": "Searches the database to find all movies by a specific actor within a certain year.", "parameters": {"type": "dict", "properties": {"actor_name": {"type": "string", "description": "The name of the actor."}, "year": {"type": "integer", "description": "The specific year to search in."}, "category": {"type": "string", "description": "The category of the film (e.g. Drama, Comedy, etc). Default is 'all'"}}, "required": ["actor_name", "year"]}}]}, "model_result_raw": "\n\n[imdb.find_movies_by_actor(actor_name=\"Leonardo DiCaprio\", year=2010)]", "model_result_decoded": [{"imdb.find_movies_by_actor": {"actor_name": "Leonardo DiCaprio", "year": 2010}}], "possible_answer": [{"imdb.find_movies_by_actor": {"actor_name": ["Leonardo DiCaprio"], "year": [2010], "category": ["", "all"]}}]}
{"id": "simple_96", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'database_query' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_96", "question": [[{"role": "user", "content": "Find records in database in user table where age is greater than 25 and job is 'engineer'."}]], "function": [{"name": "database.query", "description": "Query the database based on certain conditions.", "parameters": {"type": "dict", "properties": {"table": {"type": "string", "description": "Name of the table to query."}, "conditions": {"type": "array", "items": {"type": "dict", "properties": {"field": {"type": "string", "description": "The field to apply the condition."}, "operation": {"type": "string", "description": "The operation to be performed.", "enum": ["<", ">", "=", ">=", "<="]}, "value": {"type": "string", "description": "The value to be compared."}}, "required": ["field", "operation", "value"]}, "description": "Conditions for the query."}}, "required": ["table", "conditions"]}}]}, "model_result_raw": "\n\n[database.query(table='user', conditions=[{'field': 'age', 'operation': '>', 'value': '25'}, {'field': 'job', 'operation': '=', 'value': 'engineer'}])]", "model_result_decoded": [{"database.query": {"table": "user", "conditions": [{"field": "age", "operation": ">", "value": "25"}, {"field": "job", "operation": "=", "value": "engineer"}]}}], "possible_answer": [{"database.query": {"table": ["user"], "conditions": [[{"field": ["age"], "operation": [">"], "value": ["25"]}, {"field": ["job"], "operation": ["="], "value": ["engineer"]}]]}}]}
{"id": "simple_97", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'math_factorial' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_97", "question": [[{"role": "user", "content": "Calculate the factorial of the number 5"}]], "function": [{"name": "math.factorial", "description": "Calculate the factorial of a given number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number to compute factorial."}}, "required": ["number"]}}]}, "model_result_raw": "\n\n[math.factorial(number=5)]", "model_result_decoded": [{"math.factorial": {"number": 5}}], "possible_answer": [{"math.factorial": {"number": [5]}}]}
{"id": "simple_103", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'mathematics_calculate_area_under_curve' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}, "model_result_raw": "\n\n[mathematics.calculate_area_under_curve(polynomial=[3, 2, -4], limits=[-1, 2])]", "model_result_decoded": [{"mathematics.calculate_area_under_curve": {"polynomial": [3, 2, -4], "limits": [-1, 2]}}], "possible_answer": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3.0, 2.0, -4.0]], "limits": [[-1.0, 2.0]]}}]}
{"id": "simple_104", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'geometry_area_triangle' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_104", "question": [[{"role": "user", "content": "Calculate the area of a triangle with base 6 and height 10."}]], "function": [{"name": "geometry.area_triangle", "description": "Calculate the area of a triangle.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The length of the base of the triangle."}, "height": {"type": "integer", "description": "The height of the triangle from the base."}, "unit": {"type": "string", "description": "The measurement unit for the area. Defaults to square meters."}}, "required": ["base", "height"]}}]}, "model_result_raw": "\n\n[geometry.area_triangle(base=6, height=10)]", "model_result_decoded": [{"geometry.area_triangle": {"base": 6, "height": 10}}], "possible_answer": [{"geometry.area_triangle": {"base": [6], "height": [10], "unit": ["", "square meters"]}}]}
{"id": "simple_105", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'math_power' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_105", "question": [[{"role": "user", "content": "Calculate the power of 3 raised to the power 4."}]], "function": [{"name": "math.power", "description": "Calculate the power of one number raised to another.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The base number."}, "exponent": {"type": "integer", "description": "The exponent."}, "mod": {"type": "integer", "description": "The modulus. Default is 1. Calculates pow(base, exponent) % mod when provided."}}, "required": ["base", "exponent"]}}]}, "model_result_raw": "\n\n[math.power(base=3, exponent=4)]", "model_result_decoded": [{"math.power": {"base": 3, "exponent": 4}}], "possible_answer": [{"math.power": {"base": [3], "exponent": [4], "mod": ["", 1]}}]}
{"id": "simple_109", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'random_forest_train' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_109", "question": [[{"role": "user", "content": "Generate a random forest model with 100 trees and a depth of 5 on the provided data my_data."}]], "function": [{"name": "random_forest.train", "description": "Train a Random Forest Model on given data", "parameters": {"type": "dict", "properties": {"n_estimators": {"type": "integer", "description": "The number of trees in the forest."}, "max_depth": {"type": "integer", "description": "The maximum depth of the tree."}, "data": {"type": "any", "description": "The training data for the model."}}, "required": ["n_estimators", "max_depth", "data"]}}]}, "model_result_raw": "\n\n[random_forest.train(n_estimators=100, max_depth=5, data=my_data)]", "model_result_decoded": [{"random_forest.train": {"n_estimators": 100, "max_depth": 5, "data": "my_data"}}], "possible_answer": [{"random_forest.train": {"n_estimators": [100], "max_depth": [5], "data": ["my_data"]}}]}
{"id": "simple_111", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'random_normalvariate' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_111", "question": [[{"role": "user", "content": "Generate a random number from a normal distribution with mean 0 and standard deviation 1."}]], "function": [{"name": "random.normalvariate", "description": "Generates a random number from a normal distribution given the mean and standard deviation.", "parameters": {"type": "dict", "properties": {"mu": {"type": "integer", "description": "Mean of the normal distribution."}, "sigma": {"type": "integer", "description": "Standard deviation of the normal distribution."}}, "required": ["mu", "sigma"]}}]}, "model_result_raw": "\n\n[random.normalvariate(mu=0, sigma=1)]", "model_result_decoded": [{"random.normalvariate": {"mu": 0, "sigma": 1}}], "possible_answer": [{"random.normalvariate": {"mu": [0], "sigma": [1]}}]}
{"id": "simple_113", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'probability_dice_roll' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_113", "question": [[{"role": "user", "content": "What's the probability of rolling a six on a six-sided die twice in a row?"}]], "function": [{"name": "probability.dice_roll", "description": "Calculate the probability of rolling a certain number on a six-sided die a certain number of times in a row.", "parameters": {"type": "dict", "properties": {"desired_number": {"type": "integer", "description": "The number you want to roll."}, "number_of_rolls": {"type": "integer", "description": "How many times you want to roll that number in a row."}, "die_sides": {"type": "integer", "description": "The number of sides on the die (optional; default is 6)."}}, "required": ["desired_number", "number_of_rolls"]}}]}, "model_result_raw": "\n\n[probability.dice_roll(desired_number=6, number_of_rolls=2)]", "model_result_decoded": [{"probability.dice_roll": {"desired_number": 6, "number_of_rolls": 2}}], "possible_answer": [{"probability.dice_roll": {"desired_number": [6], "number_of_rolls": [2], "die_sides": [6, ""]}}]}
{"id": "simple_114", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'prob_dist_binomial' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_114", "question": [[{"role": "user", "content": "Find the probability of getting exactly 5 heads in 10 fair coin tosses."}]], "function": [{"name": "prob_dist.binomial", "description": "Compute the probability of having 'success' outcome from binomial distribution.", "parameters": {"type": "dict", "properties": {"trials": {"type": "integer", "description": "The number of independent experiments."}, "successes": {"type": "integer", "description": "The number of success events."}, "p": {"type": "float", "description": "The probability of success on any given trial, defaults to 0.5"}}, "required": ["trials", "successes"]}}]}, "model_result_raw": "\n\n[prob_dist.binomial(trials=10, successes=5, p=0.5)]", "model_result_decoded": [{"prob_dist.binomial": {"trials": 10, "successes": 5, "p": 0.5}}], "possible_answer": [{"prob_dist.binomial": {"trials": [10], "successes": [5], "p": [0.5, ""]}}]}
{"id": "simple_116", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'probabilities_calculate_single' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_116", "question": [[{"role": "user", "content": "What's the probability of drawing a king from a well shuffled standard deck of 52 cards?"}]], "function": [{"name": "probabilities.calculate_single", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "The total number of outcomes."}, "event_outcomes": {"type": "integer", "description": "The number of outcomes where the event occurs."}, "round": {"type": "integer", "description": "Round the answer to a specified number of decimal places. Defaults to 2."}}, "required": ["total_outcomes", "event_outcomes"]}}]}, "model_result_raw": "\n\n[probabilities.calculate_single(total_outcomes=52, event_outcomes=4)]", "model_result_decoded": [{"probabilities.calculate_single": {"total_outcomes": 52, "event_outcomes": 4}}], "possible_answer": [{"probabilities.calculate_single": {"total_outcomes": [52], "event_outcomes": [4], "round": [2, ""]}}]}
{"id": "simple_118", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'stats_t_test' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_118", "question": [[{"role": "user", "content": "Perform a two-sample t-test on my experiment data of Control [10, 15, 12, 14, 11] and Treated [18, 16, 17, 20, 22] group with alpha equals to 0.05"}]], "function": [{"name": "stats.t_test", "description": "Perform a two-sample t-test for two given arrays.", "parameters": {"type": "dict", "properties": {"array_1": {"type": "array", "items": {"type": "integer"}, "description": "First array of data."}, "array_2": {"type": "array", "items": {"type": "integer"}, "description": "Second array of data."}, "alpha": {"type": "float", "description": "Significance level for hypothesis testing."}}, "required": ["array_1", "array_2", "alpha"]}}]}, "model_result_raw": "\n\n[stats.t_test(array_1=[10, 15, 12, 14, 11], array_2=[18, 16, 17, 20, 22], alpha=0.05)]", "model_result_decoded": [{"stats.t_test": {"array_1": [10, 15, 12, 14, 11], "array_2": [18, 16, 17, 20, 22], "alpha": 0.05}}], "possible_answer": [{"stats.t_test": {"array_1": [[10, 15, 12, 14, 11]], "array_2": [[18, 16, 17, 20, 22]], "alpha": [0.05]}}]}
{"id": "simple_119", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'hypothesis_testing_ttest_ind' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_119", "question": [[{"role": "user", "content": "Perform a hypothesis test for two independent samples with scores of Sample1: [22,33,42,12,34] and Sample2: [23,45,44,14,38] at a significance level of 0.05."}]], "function": [{"name": "hypothesis_testing.ttest_ind", "description": "Conducts a hypothesis test for two independent samples.", "parameters": {"type": "dict", "properties": {"sample1": {"type": "array", "items": {"type": "integer"}, "description": "First set of observations (array of numbers)."}, "sample2": {"type": "array", "items": {"type": "integer"}, "description": "Second set of observations (array of numbers)."}, "significance_level": {"type": "float", "description": "Significance level of the test (default: 0.05)"}}, "required": ["sample1", "sample2"]}}]}, "model_result_raw": "\n\n[hypothesis_testing.ttest_ind(sample1=[22,33,42,12,34], sample2=[23,45,44,14,38], significance_level=0.05)]", "model_result_decoded": [{"hypothesis_testing.ttest_ind": {"sample1": [22, 33, 42, 12, 34], "sample2": [23, 45, 44, 14, 38], "significance_level": 0.05}}], "possible_answer": [{"hypothesis_testing.ttest_ind": {"sample1": [[22, 33, 42, 12, 34]], "sample2": [[23, 45, 44, 14, 38]], "significance_level": [0.05]}}]}
{"id": "simple_123", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'hypothesis_testing_two_sample_t_test' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_123", "question": [[{"role": "user", "content": "Perform a two-sample t-test to determine if there is a significant difference between the mean of group1 (e.g., 12.4, 15.6, 11.2, 18.9) and group2 (e.g., 10.5, 9.8, 15.2, 13.8) at the significance level 0.05."}]], "function": [{"name": "hypothesis_testing.two_sample_t_test", "description": "Perform a two-sample t-test to determine if there is a significant difference between the means of two independent samples.", "parameters": {"type": "dict", "properties": {"group1": {"type": "array", "items": {"type": "float"}, "description": "Sample observations from group 1."}, "group2": {"type": "array", "items": {"type": "float"}, "description": "Sample observations from group 2."}, "alpha": {"type": "float", "description": "Significance level for the t-test. Default is 0.05."}}, "required": ["group1", "group2"]}}]}, "model_result_raw": "\n\n[hypothesis_testing.two_sample_t_test(group1=[12.4, 15.6, 11.2, 18.9], group2=[10.5, 9.8, 15.2, 13.8], alpha=0.05)]", "model_result_decoded": [{"hypothesis_testing.two_sample_t_test": {"group1": [12.4, 15.6, 11.2, 18.9], "group2": [10.5, 9.8, 15.2, 13.8], "alpha": 0.05}}], "possible_answer": [{"hypothesis_testing.two_sample_t_test": {"group1": [[12.4, 15.6, 11.2, 18.9]], "group2": [[10.5, 9.8, 15.2, 13.8]], "alpha": [0.05, ""]}}]}
{"id": "simple_126", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'linear_regression_get_r_squared' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_126", "question": [[{"role": "user", "content": "What is the coefficient of determination (R-squared) for a model using engine size and fuel economy variables to predict car_price with a dataset in path C:/data/cars.csv?"}]], "function": [{"name": "linear_regression.get_r_squared", "description": "Calculate the coefficient of determination of a regression model.", "parameters": {"type": "dict", "properties": {"dataset_path": {"type": "string", "description": "Path to the CSV dataset file."}, "independent_variables": {"type": "array", "items": {"type": "string"}, "description": "The independent variables to use in the regression model."}, "dependent_variable": {"type": "string", "description": "The dependent variable to predict in the regression model."}}, "required": ["dataset_path", "independent_variables", "dependent_variable"]}}]}, "model_result_raw": "\n\n[linear_regression.get_r_squared(dataset_path=\"C:/data/cars.csv\", independent_variables=[\"engine_size\", \"fuel_economy\"], dependent_variable=\"car_price\")]", "model_result_decoded": [{"linear_regression.get_r_squared": {"dataset_path": "C:/data/cars.csv", "independent_variables": ["engine_size", "fuel_economy"], "dependent_variable": "car_price"}}], "possible_answer": [{"linear_regression.get_r_squared": {"dataset_path": ["C:/data/cars.csv"], "independent_variables": [["engine_size", "fuel_economy"]], "dependent_variable": ["car_price"]}}]}
{"id": "simple_128", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'finance_calculate_quarterly_dividend_per_share' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_128", "question": [[{"role": "user", "content": "What's the quarterly dividend per share of a company with 100 million outstanding shares and total dividend payout of 50 million USD?"}]], "function": [{"name": "finance.calculate_quarterly_dividend_per_share", "description": "Calculate quarterly dividend per share for a company given total dividend payout and outstanding shares", "parameters": {"type": "dict", "properties": {"total_payout": {"type": "integer", "description": "The total amount of dividends paid out in USD"}, "outstanding_shares": {"type": "integer", "description": "Total number of outstanding shares"}}, "required": ["total_payout", "outstanding_shares"], "optional": []}}]}, "model_result_raw": "\n\n[finance.calculate_quarterly_dividend_per_share(total_payout=50000000, outstanding_shares=*********)]", "model_result_decoded": [{"finance.calculate_quarterly_dividend_per_share": {"total_payout": 50000000, "outstanding_shares": *********}}], "possible_answer": [{"finance.calculate_quarterly_dividend_per_share": {"total_payout": [50000000], "outstanding_shares": [*********]}}]}
{"id": "simple_130", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'finance_calculator_npv' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_130", "question": [[{"role": "user", "content": "What's the NPV (Net Present Value) of a series of cash flows: [-50000, 10000, 15000, 20000, 25000, 30000] discounted at 8% annually?"}]], "function": [{"name": "finance_calculator.npv", "description": "Calculate the Net Present Value (NPV) for a series of cash flows discounted at a certain interest rate.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "integer"}, "description": "A list of cash flows."}, "discount_rate": {"type": "float", "description": "The annual interest rate used to discount the cash flows."}, "years": {"type": "array", "items": {"type": "integer"}, "description": "A list of years when the cash flow occurs. Default is empty array."}}, "required": ["cash_flows", "discount_rate"]}}]}, "model_result_raw": "\n\n[finance_calculator.npv(cash_flows=[-50000, 10000, 15000, 20000, 25000, 30000], discount_rate=0.08)]", "model_result_decoded": [{"finance_calculator.npv": {"cash_flows": [-50000, 10000, 15000, 20000, 25000, 30000], "discount_rate": 0.08}}], "possible_answer": [{"finance_calculator.npv": {"cash_flows": [[-50000, 10000, 15000, 20000, 25000, 30000]], "discount_rate": [0.08], "years": ["", []]}}]}
{"id": "simple_133", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'finance_predict_future_value' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_133", "question": [[{"role": "user", "content": "Predict the future value of a $5000 investment with an annual interest rate of 5% in 3 years with monthly compounding."}]], "function": [{"name": "finance.predict_future_value", "description": "Calculate the future value of an investment given its present value, interest rate, the number of compounding periods per year, and the time horizon.", "parameters": {"type": "dict", "properties": {"present_value": {"type": "integer", "description": "The present value of the investment."}, "annual_interest_rate": {"type": "float", "description": "The annual interest rate of the investment."}, "compounding_periods_per_year": {"type": "integer", "description": "The number of times that interest is compounded per year. Default is 1 (annually)."}, "time_years": {"type": "integer", "description": "The investment horizon in years."}}, "required": ["present_value", "annual_interest_rate", "time_years"]}}]}, "model_result_raw": "\n\n[finance.predict_future_value(present_value=5000, annual_interest_rate=0.05, compounding_periods_per_year=12, time_years=3)]", "model_result_decoded": [{"finance.predict_future_value": {"present_value": 5000, "annual_interest_rate": 0.05, "compounding_periods_per_year": 12, "time_years": 3}}], "possible_answer": [{"finance.predict_future_value": {"present_value": [5000], "annual_interest_rate": [0.05], "compounding_periods_per_year": [12], "time_years": [3]}}]}
{"id": "simple_134", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'investment_predictProfit' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_134", "question": [[{"role": "user", "content": "Predict the total expected profit of stocks XYZ in 5 years given I have invested $5000 and annual return rate is 7%."}]], "function": [{"name": "investment.predictProfit", "description": "Predict the profit for given investment after specified number of years.", "parameters": {"type": "dict", "properties": {"investment_amount": {"type": "integer", "description": "The amount invested in dollars."}, "annual_return": {"type": "float", "description": "The annual return rate of the investment."}, "years": {"type": "integer", "description": "The time period in years for which the investment is made."}}, "required": ["investment_amount", "annual_return", "years"]}}]}, "model_result_raw": "\n\n[investment.predictProfit(investment_amount=5000, annual_return=7.0, years=5)]", "model_result_decoded": [{"investment.predictProfit": {"investment_amount": 5000, "annual_return": 7.0, "years": 5}}], "possible_answer": [{"investment.predictProfit": {"investment_amount": [5000], "annual_return": [0.07], "years": [5]}}]}
{"id": "simple_144", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'market_performance_get_data' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_144", "question": [[{"role": "user", "content": "Find the market performance of the S&P 500 and the Dow Jones over the past 5 days."}]], "function": [{"name": "market_performance.get_data", "description": "Retrieve the market performance data for specified indexes over a specified time period.", "parameters": {"type": "dict", "properties": {"indexes": {"type": "array", "items": {"type": "string"}, "description": "Array of stock market indexes. Supported indexes are 'S&P 500', 'Dow Jones', 'NASDAQ', 'FTSE 100', 'DAX' etc."}, "days": {"type": "integer", "description": "Number of days in the past for which the performance data is required."}, "detailed": {"type": "boolean", "description": "Whether to return detailed performance data. If set to true, returns high, low, opening, and closing prices. If false, returns only closing prices. Default is false."}}, "required": ["indexes", "days"]}}]}, "model_result_raw": "\n\n[market_performance.get_data(indexes=[\"S&P 500\", \"Dow Jones\"], days=5)]", "model_result_decoded": [{"market_performance.get_data": {"indexes": ["S&P 500", "Dow Jones"], "days": 5}}], "possible_answer": [{"market_performance.get_data": {"indexes": [["S&P 500", "Dow Jones"]], "days": [5], "detailed": ["", true, false]}}]}
{"id": "simple_148", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'finance_calculate_future_value' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_148", "question": [[{"role": "user", "content": "Calculate the future value of an investment with an annual rate of return of 8%, an initial investment of $20000, and a time frame of 5 years."}]], "function": [{"name": "finance.calculate_future_value", "description": "Calculate the future value of an investment given an initial investment, annual rate of return, and a time frame.", "parameters": {"type": "dict", "properties": {"initial_investment": {"type": "integer", "description": "The initial investment amount."}, "rate_of_return": {"type": "float", "description": "The annual rate of return."}, "years": {"type": "integer", "description": "The time frame of the investment in years."}, "contribution": {"type": "integer", "description": "Optional: Additional regular contributions. Default is 0."}}, "required": ["initial_investment", "rate_of_return", "years"]}}]}, "model_result_raw": "\n\n[finance.calculate_future_value(initial_investment=20000, rate_of_return=0.08, years=5)]", "model_result_decoded": [{"finance.calculate_future_value": {"initial_investment": 20000, "rate_of_return": 0.08, "years": 5}}], "possible_answer": [{"finance.calculate_future_value": {"initial_investment": [20000], "rate_of_return": [0.08], "years": [5], "contribution": ["", 0]}}]}
{"id": "simple_156", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'crime_record_get_record' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_156", "question": [[{"role": "user", "content": "Look up details of a felony crime record for case number CA123456 in San Diego County"}]], "function": [{"name": "crime_record.get_record", "description": "Retrieve detailed felony crime records using a specific case number and location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The case number related to the crime."}, "county": {"type": "string", "description": "The county in which the crime occurred."}, "details": {"type": "boolean", "description": "To get a detailed report, set as true. Defaults to false."}}, "required": ["case_number", "county"]}}]}, "model_result_raw": "\n\n[crime_record.get_record(case_number=\"CA123456\", county=\"San Diego County\", details=True)]", "model_result_decoded": [{"crime_record.get_record": {"case_number": "CA123456", "county": "San Diego County", "details": true}}], "possible_answer": [{"crime_record.get_record": {"case_number": ["CA123456"], "county": ["San Diego", "San Diego County"], "details": [true]}}]}
{"id": "simple_157", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'criminal_history_check_felonies' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_157", "question": [[{"role": "user", "content": "Find out if an individual John Doe with a birthday 01-01-1980 has any prior felony convictions in California."}]], "function": [{"name": "criminal_history.check_felonies", "description": "This function checks if an individual has any prior felony convictions based on their full name and birth date.", "parameters": {"type": "dict", "properties": {"full_name": {"type": "string", "description": "The full name of the individual."}, "birth_date": {"type": "string", "description": "The birth date of the individual. Must be in MM-DD-YYYY format."}, "state": {"type": "string", "description": "The state to search the criminal record in. Default to 'None', which the function will search across all states."}}, "required": ["full_name", "birth_date"]}}]}, "model_result_raw": "\n\n[criminal_history.check_felonies(full_name=\"John Doe\", birth_date=\"01-01-1980\", state=\"California\")]", "model_result_decoded": [{"criminal_history.check_felonies": {"full_name": "John Doe", "birth_date": "01-01-1980", "state": "California"}}], "possible_answer": [{"criminal_history.check_felonies": {"full_name": ["John Doe"], "birth_date": ["01-01-1980"], "state": ["California", "CA"]}}]}
{"id": "simple_158", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_158", "question": [[{"role": "user", "content": "Find the information of criminal cases of Mr. X in New York between 2012 and 2015."}]], "function": [{"name": "get_criminal_records", "description": "Retrieve the criminal records of a specific person in a specific area during a certain time period.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY"}, "from_year": {"type": "integer", "description": "The start year of the time frame."}, "to_year": {"type": "integer", "description": "The end year of the time frame."}}, "required": ["name", "location", "from_year", "to_year"]}}]}, "model_result_raw": "\n\n[get_criminal_records(name=\"Mr. X\", location=\"New York\", from_year=2012, to_year=2015)]", "model_result_decoded": [{"get_criminal_records": {"name": "Mr. X", "location": "New York", "from_year": 2012, "to_year": 2015}}], "possible_answer": [{"get_criminal_records": {"name": ["Mr. X"], "location": ["New York, NY"], "from_year": [2012], "to_year": [2015]}}]}
{"id": "simple_163", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'property_records_get' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_163", "question": [[{"role": "user", "content": "Provide me with the property records of my house located at 123 main street, with parcel number 1234567890 in Santa Clara county. Include owners information in the response."}]], "function": [{"name": "property_records.get", "description": "Fetch property records based on location, parcel number and county.", "parameters": {"type": "dict", "properties": {"address": {"type": "string", "description": "Address of the property."}, "parcel_number": {"type": "string", "description": "Parcel number of the property."}, "county": {"type": "string", "description": "County where the property is located."}, "include_owner": {"type": "boolean", "description": "Include owner's name in the property record. Default is false.", "default": false}}, "required": ["address", "parcel_number", "county"]}}]}, "model_result_raw": "\n\n[property_records.get(address=\"123 main street\", parcel_number=\"1234567890\", county=\"Santa Clara\", include_owner=True)]", "model_result_decoded": [{"property_records.get": {"address": "123 main street", "parcel_number": "1234567890", "county": "Santa Clara", "include_owner": true}}], "possible_answer": [{"property_records.get": {"address": ["123 main street"], "parcel_number": ["1234567890"], "county": ["Santa Clara"], "include_owner": [true]}}]}
{"id": "simple_165", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'civil_cases_retrieve' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_165", "question": [[{"role": "user", "content": "Retrieve cases from 2020 about theft crimes in Los Angeles, California"}]], "function": [{"name": "civil_cases.retrieve", "description": "Retrieve civil cases based on given parameters, including year, crime type, and location.", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "Year of the cases"}, "crime_type": {"type": "string", "description": "Type of the crime."}, "location": {"type": "string", "description": "Location of the case in the format of city name."}}, "required": ["year", "crime_type", "location"]}}]}, "model_result_raw": "\n\n[civil_cases.retrieve(year=2020, crime_type=\"theft\", location=\"Los Angeles\")]", "model_result_decoded": [{"civil_cases.retrieve": {"year": 2020, "crime_type": "theft", "location": "Los Angeles"}}], "possible_answer": [{"civil_cases.retrieve": {"year": [2020], "crime_type": ["theft"], "location": ["Los Angeles", "Los Angeles, California"]}}]}
{"id": "simple_166", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'lawyer_find_nearby' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_166", "question": [[{"role": "user", "content": "Find a lawyer specializing in divorce cases and charge fee less than 400 dollars per hour in Chicago."}]], "function": [{"name": "lawyer.find_nearby", "description": "Locate nearby lawyers based on specific criteria like specialty, fee per hour and city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city and state, e.g. Chicago, IL."}, "specialty": {"type": "array", "items": {"type": "string", "enum": ["Civil", "Divorce", "Immigration", "Business", "Criminal"]}, "description": "Specialization of the lawyer."}, "fee": {"type": "integer", "description": "Hourly fee charged by lawyer"}}, "required": ["city", "specialty", "fee"]}}]}, "model_result_raw": "\n\n[lawyer.find_nearby(city=\"Chicago, IL\", specialty=[\"Divorce\"], fee=400)]", "model_result_decoded": [{"lawyer.find_nearby": {"city": "Chicago, IL", "specialty": ["Divorce"], "fee": 400}}], "possible_answer": [{"lawyer.find_nearby": {"city": ["Chicago, IL.", "Chicago, IL"], "specialty": [["Divorce"]], "fee": [400]}}]}
{"id": "simple_167", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'law_civil_get_case_details' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_167", "question": [[{"role": "user", "content": "Retrieve the details of a Supreme Court case titled 'Roe v. Wade'.Include dissent information."}]], "function": [{"name": "law.civil.get_case_details", "description": "Retrieve the details of a Supreme Court case given its title.", "parameters": {"type": "dict", "properties": {"case_title": {"type": "string", "description": "Title of the Supreme Court case."}, "include_dissent": {"type": "boolean", "description": "If true, the output will include details of the dissenting opinion."}}, "required": ["case_title", "include_dissent"]}}]}, "model_result_raw": "\n\n[law.civil.get_case_details(case_title='Roe v. Wade', include_dissent=True)]", "model_result_decoded": [{"law.civil.get_case_details": {"case_title": "Roe v. Wade", "include_dissent": true}}], "possible_answer": [{"law.civil.get_case_details": {"case_title": ["Roe v. Wade"], "include_dissent": [true]}}]}
{"id": "simple_169", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'court_case_search' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_169", "question": [[{"role": "user", "content": "Find the details of the court case identified by docket number 123456 in Texas. Don't return full text"}]], "function": [{"name": "court_case.search", "description": "Retrieves details about a court case using its docket number and location.", "parameters": {"type": "dict", "properties": {"docket_number": {"type": "string", "description": "The docket number for the case."}, "location": {"type": "string", "description": "The location where the case is registered, in the format: state, e.g., Texas"}, "full_text": {"type": "boolean", "default": "false", "description": "Option to return the full text of the case ruling."}}, "required": ["docket_number", "location"]}}]}, "model_result_raw": "\n\n[court_case.search(docket_number=\"123456\", location=\"Texas\", full_text=False)]", "model_result_decoded": [{"court_case.search": {"docket_number": "123456", "location": "Texas", "full_text": false}}], "possible_answer": [{"court_case.search": {"docket_number": ["123456"], "location": ["Texas"], "full_text": [false, ""]}}]}
{"id": "simple_170", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'law_case_search_find_historical' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_170", "question": [[{"role": "user", "content": "Find a historical law case about fraud from 2010 to 2015."}]], "function": [{"name": "law_case_search.find_historical", "description": "Search for a historical law case based on specific criteria like the subject and year.", "parameters": {"type": "dict", "properties": {"subject": {"type": "string", "description": "The subject matter of the case, e.g., 'fraud'"}, "from_year": {"type": "integer", "description": "The start year for the range of the case. The case should happen after this year."}, "to_year": {"type": "integer", "description": "The end year for the range of the case. The case should happen before this year."}}, "required": ["subject", "from_year", "to_year"]}}]}, "model_result_raw": "\n\n[law_case_search.find_historical(subject=\"fraud\", from_year=2010, to_year=2015)]", "model_result_decoded": [{"law_case_search.find_historical": {"subject": "fraud", "from_year": 2010, "to_year": 2015}}], "possible_answer": [{"law_case_search.find_historical": {"subject": ["fraud"], "from_year": [2010], "to_year": [2015]}}]}
{"id": "simple_172", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'legal_case_fetch' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_172", "question": [[{"role": "user", "content": "How to obtain the detailed case information of the 'R vs Adams' legal case?"}]], "function": [{"name": "legal_case.fetch", "description": "Fetch detailed legal case information from database.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "string", "description": "The ID of the legal case."}, "details": {"type": "boolean", "description": "True if need the detail info. "}}, "required": ["case_id", "details"]}}]}, "model_result_raw": "\n\n[legal_case.fetch(case_id='R vs Adams', details=True)]", "model_result_decoded": [{"legal_case.fetch": {"case_id": "R vs Adams", "details": true}}], "possible_answer": [{"legal_case.fetch": {"case_id": ["R vs Adams"], "details": [true]}}]}
{"id": "simple_175", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'lawyer_get_experience' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_175", "question": [[{"role": "user", "content": "How many months of experience a Lawyer John Doe has on handling Bankruptcy cases."}]], "function": [{"name": "lawyer.get_experience", "description": "Retrieve months of experience of a Lawyer on handling certain type of law cases.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The full name of the Lawyer."}, "law_type": {"type": "string", "description": "The type of law case. eg. Bankruptcy"}}, "required": ["name", "law_type"]}}]}, "model_result_raw": "\n\n[lawyer.get_experience(name=\"John Doe\", law_type=\"Bankruptcy\")]", "model_result_decoded": [{"lawyer.get_experience": {"name": "John Doe", "law_type": "Bankruptcy"}}], "possible_answer": [{"lawyer.get_experience": {"name": ["John Doe"], "law_type": ["Bankruptcy"]}}]}
{"id": "simple_176", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'lawsuit_details_find' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_176", "question": [[{"role": "user", "content": "Find details of patent lawsuits involving the company 'Apple Inc.' from the year 2010."}]], "function": [{"name": "lawsuit_details.find", "description": "Find details of lawsuits involving a specific company from a given year.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "Name of the company."}, "year": {"type": "integer", "description": "Year of the lawsuit."}, "case_type": {"type": "string", "description": "Type of the lawsuit, e.g., 'IPR', 'Patent', 'Commercial', etc. Default is 'all'."}}, "required": ["company_name", "year"]}}]}, "model_result_raw": "\n\n[lawsuit_details.find(company_name='Apple Inc.', year=2010, case_type='Patent')]", "model_result_decoded": [{"lawsuit_details.find": {"company_name": "Apple Inc.", "year": 2010, "case_type": "Patent"}}], "possible_answer": [{"lawsuit_details.find": {"company_name": ["Apple Inc."], "year": [2010], "case_type": ["Patent", "IPR"]}}]}
{"id": "simple_184", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'lawsuit_check_case' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_184", "question": [[{"role": "user", "content": "I need the details of the lawsuit case with case ID of 1234 and verify if it's already closed."}]], "function": [{"name": "lawsuit.check_case", "description": "Verify the details of a lawsuit case and check its status using case ID.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "integer", "description": "The identification number of the lawsuit case."}, "closed_status": {"type": "boolean", "description": "The status of the lawsuit case to be verified."}}, "required": ["case_id", "closed_status"]}}]}, "model_result_raw": "\n\n[lawsuit.check_case(case_id=1234, closed_status=True)]", "model_result_decoded": [{"lawsuit.check_case": {"case_id": 1234, "closed_status": true}}], "possible_answer": [{"lawsuit.check_case": {"case_id": [1234], "closed_status": [true]}}]}
{"id": "simple_188", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'weather_humidity_forecast' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_188", "question": [[{"role": "user", "content": "What is the humidity level in Miami, Florida in the upcoming 7 days?"}]], "function": [{"name": "weather.humidity_forecast", "description": "Retrieve a humidity forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the humidity for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}, "min_humidity": {"type": "integer", "description": "Minimum level of humidity (in percentage) to filter the result. Default is 0."}}, "required": ["location", "days"]}}]}, "model_result_raw": "\n\n[weather.humidity_forecast(location=\"Miami, Florida\", days=7)]", "model_result_decoded": [{"weather.humidity_forecast": {"location": "Miami, Florida", "days": 7}}], "possible_answer": [{"weather.humidity_forecast": {"location": ["Miami", "Miami, Florida"], "days": [7], "min_humidity": ["", 0]}}]}
{"id": "simple_193", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'local_nursery_find' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_193", "question": [[{"role": "user", "content": "Find the best local nurseries in Toronto with a good variety of annual plants."}]], "function": [{"name": "local_nursery.find", "description": "Locate local nurseries based on location and plant types availability.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city or locality where the nursery needs to be located."}, "plant_types": {"type": "array", "items": {"type": "string", "enum": ["Annual", "Perennial", "Shrub", "Tree", "Herbs", "Fruits"]}, "description": "Type of plants the nursery should provide."}}, "required": ["location", "plant_types"]}}]}, "model_result_raw": "\n\n[local_nursery.find(location=\"Toronto\", plant_types=[\"Annual\"])]", "model_result_decoded": [{"local_nursery.find": {"location": "Toronto", "plant_types": ["Annual"]}}], "possible_answer": [{"local_nursery.find": {"location": ["Toronto"], "plant_types": [["Annual"]]}}]}
{"id": "simple_199", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'environmental_data_air_quality_index' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_199", "question": [[{"role": "user", "content": "Find air quality index in San Jose for next three days."}]], "function": [{"name": "environmental_data.air_quality_index", "description": "Retrieves Air Quality Index (AQI) for specified location over a number of days.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Name of the city or town to retrieve air quality index for."}, "days": {"type": "integer", "description": "Number of days for which to retrieve data. If not provided, default to today."}}, "required": ["location"]}}]}, "model_result_raw": "\n\n[environmental_data.air_quality_index(location=\"San Jose\", days=3)]", "model_result_decoded": [{"environmental_data.air_quality_index": {"location": "San Jose", "days": 3}}], "possible_answer": [{"environmental_data.air_quality_index": {"location": ["San Jose", "'San Jose'"], "days": [3]}}]}
{"id": "simple_202", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Invalid syntax. Failed to decode AST. invalid syntax (<unknown>, line 1)"], "error_type": "ast_decoder:decoder_failed", "prompt": {"id": "simple_202", "question": [[{"role": "user", "content": "How many greenhouse gas emissions would I save if I switched to renewable energy sources for 3 months in California?"}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'Texas'."}}, "required": ["energy_type", "usage_duration"]}}]}, "model_result_raw": "\n\nThe function requires the 'energy_type' parameter, which was not specified in the question. Please provide the type of renewable energy source (e.g., solar, wind) to calculate the emissions savings.", "possible_answer": [{"calculate_emission_savings": {"energy_type": ["renewable"], "usage_duration": [3], "region": ["California", "CA"]}}]}
{"id": "simple_204", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'restaurant_find_nearby' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_204", "question": [[{"role": "user", "content": "Find restaurants near me within 10 miles that offer Chinese cuisine in Seattle."}]], "function": [{"name": "restaurant.find_nearby", "description": "Locate nearby restaurants based on specific criteria like cuisine type.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine in restaurant."}, "max_distance": {"type": "integer", "description": "Maximum distance (in miles) within which to search for restaurants. Default is 5."}}, "required": ["location", "cuisine"]}}]}, "model_result_raw": "\n\n[restaurant.find_nearby(location=\"Seattle, WA\", cuisine=\"Chinese\", max_distance=10)]", "model_result_decoded": [{"restaurant.find_nearby": {"location": "Seattle, WA", "cuisine": "Chinese", "max_distance": 10}}], "possible_answer": [{"restaurant.find_nearby": {"location": ["Seattle", "Seattle, WA"], "cuisine": ["Chinese"], "max_distance": [10]}}]}
{"id": "simple_206", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'parks_find_nearby' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_206", "question": [[{"role": "user", "content": "Find the nearest park with a tennis court in London."}]], "function": [{"name": "parks.find_nearby", "description": "Locate nearby parks based on specific criteria like tennis court availability.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. London, UK"}, "amenities": {"type": "array", "items": {"type": "string", "enum": ["Tennis Court", "Picnic Area", "Playground", "Running Track"]}, "description": "Preferred amenities in park. Default is ['Running Track']"}}, "required": ["location"]}}]}, "model_result_raw": "\n\n[parks.find_nearby(location=\"London, UK\", amenities=[\"Tennis Court\"])]", "model_result_decoded": [{"parks.find_nearby": {"location": "London, UK", "amenities": ["Tennis Court"]}}], "possible_answer": [{"parks.find_nearby": {"location": ["London", "London, UK"], "amenities": [["Tennis Court"]]}}]}
{"id": "simple_208", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'map_service_get_directions' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_208", "question": [[{"role": "user", "content": "Get me the directions from New York to Los Angeles avoiding highways and toll roads."}]], "function": [{"name": "map_service.get_directions", "description": "Retrieve directions from a starting location to an ending location, including options for route preferences.", "parameters": {"type": "dict", "properties": {"start": {"type": "string", "description": "Starting location for the route."}, "end": {"type": "string", "description": "Ending location for the route."}, "avoid": {"type": "array", "items": {"type": "string", "enum": ["tolls", "highways", "ferries"]}, "description": "Route features to avoid. Default is ['highways', 'ferries']"}}, "required": ["start", "end"]}}]}, "model_result_raw": "\n\n[map_service.get_directions(start=\"New York\", end=\"Los Angeles\", avoid=[\"highways\", \"tolls\"])]", "model_result_decoded": [{"map_service.get_directions": {"start": "New York", "end": "Los Angeles", "avoid": ["highways", "tolls"]}}], "possible_answer": [{"map_service.get_directions": {"start": ["New York", "NYC"], "end": ["Los Angeles", "LA"], "avoid": [["highways", "tolls"], ["tolls", "highways"]]}}]}
{"id": "simple_209", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'public_library_find_nearby' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_209", "question": [[{"role": "user", "content": "Locate the nearest public library in Boston, Massachusetts with English fiction section and free Wi-Fi."}]], "function": [{"name": "public_library.find_nearby", "description": "Locate nearby public libraries based on specific criteria like English fiction availability and Wi-Fi.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Boston, MA"}, "facilities": {"type": "array", "items": {"type": "string", "enum": ["Wi-Fi", "Reading Room", "Fiction", "Children Section", "Cafe"]}, "description": "Facilities and sections in public library."}}, "required": ["location", "facilities"]}}]}, "model_result_raw": "\n\n[public_library.find_nearby(location=\"Boston, MA\", facilities=[\"Fiction\", \"Wi-Fi\"])]", "model_result_decoded": [{"public_library.find_nearby": {"location": "Boston, MA", "facilities": ["Fiction", "Wi-Fi"]}}], "possible_answer": [{"public_library.find_nearby": {"location": ["Boston, MA", "Boston, Massachusetts"], "facilities": [["Fiction", "Wi-Fi"], ["Wi-Fi", "Fiction"]]}}]}
{"id": "simple_213", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'flight_book' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_213", "question": [[{"role": "user", "content": "Book a direct flight from San Francisco to London for 2022-04-27 afternoon"}]], "function": [{"name": "flight.book", "description": "Book a direct flight for a specific date and time from departure location to destination location.", "parameters": {"type": "dict", "properties": {"departure_location": {"type": "string", "description": "The location you are departing from."}, "destination_location": {"type": "string", "description": "The location you are flying to."}, "date": {"type": "string", "description": "The date of the flight. Accepts standard date format e.g., 2022-04-28."}, "time": {"type": "string", "description": "Preferred time of flight. Default is 'morning'."}, "direct_flight": {"type": "boolean", "description": "If set to true, only direct flights will be searched. Default is false."}}, "required": ["departure_location", "destination_location", "date"]}}]}, "model_result_raw": "\n\n[flight.book(departure_location=\"San Francisco\", destination_location=\"London\", date=\"2022-04-27\", time=\"afternoon\", direct_flight=True)]", "model_result_decoded": [{"flight.book": {"departure_location": "San Francisco", "destination_location": "London", "date": "2022-04-27", "time": "afternoon", "direct_flight": true}}], "possible_answer": [{"flight.book": {"departure_location": ["San Francisco", "SF"], "destination_location": ["London"], "date": ["2022-04-27", "04/27/2022", "Apr 27, 2022"], "time": ["afternoon", ""], "direct_flight": [true]}}]}
{"id": "simple_214", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'event_finder_find_upcoming' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_214", "question": [[{"role": "user", "content": "Search for upcoming month rock concerts in New York."}]], "function": [{"name": "event_finder.find_upcoming", "description": "Find upcoming events of a specific genre in a given location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the search will take place, e.g. New York, NY."}, "genre": {"type": "string", "description": "The genre of events."}, "days_ahead": {"type": "integer", "description": "The number of days from now to include in the search.", "default": 7}}, "required": ["location", "genre"]}}]}, "model_result_raw": "\n\n[event_finder.find_upcoming(location=\"New York, NY\", genre=\"rock\", days_ahead=30)]", "model_result_decoded": [{"event_finder.find_upcoming": {"location": "New York, NY", "genre": "rock", "days_ahead": 30}}], "possible_answer": [{"event_finder.find_upcoming": {"location": ["New York", "New York, NY", "NYC"], "genre": ["Rock", "rock"], "days_ahead": [30]}}]}
{"id": "simple_215", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'movie_details_brief' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_215", "question": [[{"role": "user", "content": "Give me a brief on movie 'Interstellar'"}]], "function": [{"name": "movie_details.brief", "description": "This function retrieves a brief about a specified movie.", "parameters": {"type": "dict", "properties": {"title": {"type": "string", "description": "Title of the movie"}, "extra_info": {"type": "boolean", "description": "Option to get additional information like Director, Cast, Awards etc.", "default": "false"}}, "required": ["title"]}}]}, "model_result_raw": "\n\n[movie_details.brief(title='Interstellar', extra_info=False)]", "model_result_decoded": [{"movie_details.brief": {"title": "Interstellar", "extra_info": false}}], "possible_answer": [{"movie_details.brief": {"title": ["Interstellar"], "extra_info": ["", false]}}]}
{"id": "simple_217", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'fMRI_analyze' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_217", "question": [[{"role": "user", "content": "Analyze my fMRI data in ~/data/myfMRI.nii from a multi-band sequence, that is smoothed at 6mm with an isotropic voxel size of 2mm."}]], "function": [{"name": "fMRI.analyze", "description": "This function takes in fMRI data to output analyzed data.", "parameters": {"type": "dict", "properties": {"data_source": {"type": "string", "description": "The path where the data is stored."}, "sequence_type": {"type": "string", "description": "Type of fMRI sequence"}, "smooth": {"type": "integer", "description": "Spatial smoothing FWHM. In mm."}, "voxel_size": {"type": "integer", "description": "Size of isotropic voxels in mm.", "default": 3}}, "required": ["data_source", "sequence_type", "smooth"]}}]}, "model_result_raw": "\n\n[fMRI.analyze(data_source='~/data/myfMRI.nii', sequence_type='multi-band', smooth=6, voxel_size=2)]", "model_result_decoded": [{"fMRI.analyze": {"data_source": "~/data/myfMRI.nii", "sequence_type": "multi-band", "smooth": 6, "voxel_size": 2}}], "possible_answer": [{"fMRI.analyze": {"data_source": ["~/data/myfMRI.nii"], "sequence_type": ["multi-band"], "smooth": [6], "voxel_size": [2]}}]}
{"id": "simple_218", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'patient_get_mri_report' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_218", "question": [[{"role": "user", "content": "Given patient with id 546382, retrieve their brain MRI report with the status 'concluded'."}]], "function": [{"name": "patient.get_mri_report", "description": "Fetch the brain MRI report of the patient for a given status.", "parameters": {"type": "dict", "properties": {"patient_id": {"type": "string", "description": "The patient identifier."}, "mri_type": {"type": "string", "description": "Type of the MRI. Default to be 'brain'.", "enum": ["brain", "spinal", "chest", "abdominal"]}, "status": {"type": "string", "description": "Status of the report, could be 'in progress', 'concluded' or 'draft'.", "enum": ["in progress", "concluded", "draft"]}}, "required": ["patient_id", "status"]}}]}, "model_result_raw": "\n\n[patient.get_mri_report(patient_id=\"546382\", mri_type=\"brain\", status=\"concluded\")]", "model_result_decoded": [{"patient.get_mri_report": {"patient_id": "546382", "mri_type": "brain", "status": "concluded"}}], "possible_answer": [{"patient.get_mri_report": {"patient_id": ["546382"], "mri_type": ["brain", ""], "status": ["concluded"]}}]}
{"id": "simple_223", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'group_dynamics_pattern' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_223", "question": [[{"role": "user", "content": "Find social behaviors and patterns in a group size of 50 with extroverted members being 15 and introverted members being 35."}]], "function": [{"name": "group_dynamics.pattern", "description": "Examine the social dynamics and interactions within a group based on the personality traits and group size.", "parameters": {"type": "dict", "properties": {"total": {"type": "integer", "description": "The total group size."}, "extroverts": {"type": "integer", "description": "The number of extroverted members in the group."}, "introverts": {"type": "integer", "description": "The number of introverted members in the group."}}, "required": ["total", "extroverts", "introverts"]}}]}, "model_result_raw": "\n\n[group_dynamics.pattern(total=50, extroverts=15, introverts=35)]", "model_result_decoded": [{"group_dynamics.pattern": {"total": 50, "extroverts": 15, "introverts": 35}}], "possible_answer": [{"group_dynamics.pattern": {"total": [50], "extroverts": [15], "introverts": [35]}}]}
{"id": "simple_224", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'social_media_analytics_most_followed' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_224", "question": [[{"role": "user", "content": "Find the most followed person on twitter who tweets about psychology related to behaviour and group dynamics."}]], "function": [{"name": "social_media_analytics.most_followed", "description": "Find the most followed Twitter user related to certain topics.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The main topic of interest."}, "sub_topics": {"type": "array", "items": {"type": "string"}, "description": "Sub-topics related to main topic. Default is empty."}, "region": {"type": "string", "description": "Region of interest for twitter search. Default is 'all'."}}, "required": ["topic"]}}]}, "model_result_raw": "\n\n[social_media_analytics.most_followed(topic=\"psychology\", sub_topics=[\"behaviour\", \"group dynamics\"])]", "model_result_decoded": [{"social_media_analytics.most_followed": {"topic": "psychology", "sub_topics": ["behaviour", "group dynamics"]}}], "possible_answer": [{"social_media_analytics.most_followed": {"topic": ["psychology"], "sub_topics": [["behaviour", "group dynamics"]], "region": ["", "all"]}}]}
{"id": "simple_225", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'psych_research_get_preference' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_225", "question": [[{"role": "user", "content": "What is the percentage of population preferring digital reading over physical books?"}]], "function": [{"name": "psych_research.get_preference", "description": "Gathers research data on public preference between two options, based on societal category.", "parameters": {"type": "dict", "properties": {"category": {"type": "string", "description": "The societal category the preference data is about. E.g. reading, transportation, food"}, "option_one": {"type": "string", "description": "The first option people could prefer."}, "option_two": {"type": "string", "description": "The second option people could prefer."}, "demographic": {"type": "string", "description": "Specific demographic of society to narrow down the research.", "default": "all"}}, "required": ["category", "option_one", "option_two"]}}]}, "model_result_raw": "\n\n[psych_research.get_preference(category=\"reading\", option_one=\"digital reading\", option_two=\"physical books\", demographic=\"all\")]", "model_result_decoded": [{"psych_research.get_preference": {"category": "reading", "option_one": "digital reading", "option_two": "physical books", "demographic": "all"}}], "possible_answer": [{"psych_research.get_preference": {"category": ["reading"], "option_one": ["digital reading", "digital"], "option_two": ["physical book", "physical", "physical books"], "demographic": ["", "all"]}}]}
{"id": "simple_231", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'history_get_key_events' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_231", "question": [[{"role": "user", "content": "Provide key war events in German history from 1871 to 1945."}]], "function": [{"name": "history.get_key_events", "description": "Retrieve key historical events within a specific period for a certain country.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The name of the country for which history is queried."}, "start_year": {"type": "integer", "description": "Start year of the period for which history is queried."}, "end_year": {"type": "integer", "description": "End year of the period for which history is queried."}, "event_type": {"type": "array", "items": {"type": "string", "enum": ["War", "Revolutions", "Diplomacy", "Economy"]}, "description": "Types of event. Default to 'all', which all types will be considered."}}, "required": ["country", "start_year", "end_year"]}}]}, "model_result_raw": "\n\n[history.get_key_events(country=\"Germany\", start_year=1871, end_year=1945, event_type=[\"War\"])]", "model_result_decoded": [{"history.get_key_events": {"country": "Germany", "start_year": 1871, "end_year": 1945, "event_type": ["War"]}}], "possible_answer": [{"history.get_key_events": {"country": ["Germany", "DE"], "start_year": [1871], "end_year": [1945], "event_type": [["War"]]}}]}
{"id": "simple_232", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'monarch_getMonarchOfYear' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_232", "question": [[{"role": "user", "content": "What was the full name king of England in 1800?"}]], "function": [{"name": "monarch.getMonarchOfYear", "description": "Retrieve the monarch of a specific location during a specified year.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location (e.g., country) whose monarch needs to be found."}, "year": {"type": "integer", "description": "The year to search the monarch."}, "fullName": {"type": "boolean", "default": false, "description": "If true, returns the full name and title of the monarch."}}, "required": ["location", "year"]}}]}, "model_result_raw": "\n\n[monarch.getMonarchOfYear(location=\"England\", year=1800, fullName=True)]", "model_result_decoded": [{"monarch.getMonarchOfYear": {"location": "England", "year": 1800, "fullName": true}}], "possible_answer": [{"monarch.getMonarchOfYear": {"location": ["England", "ENG"], "year": [1800], "fullName": [true]}}]}
{"id": "simple_233", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'european_history_get_event_date' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_233", "question": [[{"role": "user", "content": "When did the Treaty of Tordesillas take place? Put it in the format of YYYY."}]], "function": [{"name": "european_history.get_event_date", "description": "Retrieve the date of a specific event in European history.", "parameters": {"type": "dict", "properties": {"event_name": {"type": "string", "description": "The name of the event."}, "format": {"type": "string", "description": "Optional format of the returned date. Default is 'MM-DD-YYYY'."}}, "required": ["event_name"]}}]}, "model_result_raw": "\n\n[european_history.get_event_date(event_name=\"Treaty of Tordesillas\", format=\"YYYY\")]", "model_result_decoded": [{"european_history.get_event_date": {"event_name": "Treaty of Tordesillas", "format": "YYYY"}}], "possible_answer": [{"european_history.get_event_date": {"event_name": ["Treaty of Tordesillas"], "format": ["YYYY"]}}]}
{"id": "simple_234", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_234", "question": [[{"role": "user", "content": "Find important Wars in European history during the 19th century."}]], "function": [{"name": "history_eu.fetch_events", "description": "Fetches significant historical events within a specific time period in European history.", "parameters": {"type": "dict", "properties": {"century": {"type": "integer", "description": "The century you are interested in."}, "region": {"type": "string", "description": "The region of Europe you are interested in.", "enum": ["Northern", "Southern", "Eastern", "Western"]}, "category": {"type": "string", "description": "Category of the historical events. Default is 'Culture'.", "enum": ["Wars", "Culture", "Politics", "Scientific", "Others"]}}, "required": ["century", "region"]}}]}, "model_result_raw": "\n\n[history_eu.fetch_events(century=19, region='Northern', category='Wars'), history_eu.fetch_events(century=19, region='Southern', category='Wars'), history_eu.fetch_events(century=19, region='Eastern', category='Wars'), history_eu.fetch_events(century=19, region='Western', category='Wars')]", "model_result_decoded": [{"history_eu.fetch_events": {"century": 19, "region": "Northern", "category": "Wars"}}, {"history_eu.fetch_events": {"century": 19, "region": "Southern", "category": "Wars"}}, {"history_eu.fetch_events": {"century": 19, "region": "Eastern", "category": "Wars"}}, {"history_eu.fetch_events": {"century": 19, "region": "Western", "category": "Wars"}}], "possible_answer": [{"history_eu.fetch_events": {"century": [19], "region": ["Northern", "Southern", "Eastern", "Western"], "category": ["Wars"]}}]}
{"id": "simple_236", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'us_history_get_event_info' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_236", "question": [[{"role": "user", "content": "Get start date on the American Civil War."}]], "function": [{"name": "us_history.get_event_info", "description": "Retrieve detailed information about a significant event in U.S. history.", "parameters": {"type": "dict", "properties": {"event_name": {"type": "string", "description": "The name of the event."}, "specific_info": {"type": "string", "description": "Specific aspect of information related to event.", "enum": ["Start Date", "End Date", "Participants", "Result", "Notable Figures", "Importance in History"]}}, "required": ["event_name", "specific_info"]}}]}, "model_result_raw": "\n\n[us_history.get_event_info(event_name=\"American Civil War\", specific_info=\"Start Date\")]", "model_result_decoded": [{"us_history.get_event_info": {"event_name": "American Civil War", "specific_info": "Start Date"}}], "possible_answer": [{"us_history.get_event_info": {"event_name": ["American Civil War", "Civil War"], "specific_info": ["Start Date"]}}]}
{"id": "simple_238", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'us_history_get_president' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_238", "question": [[{"role": "user", "content": "Who was the president of the United States during the American Civil War?"}]], "function": [{"name": "us_history.get_president", "description": "Retrieve the U.S. president during a specific event in American history.", "parameters": {"type": "dict", "properties": {"event": {"type": "string", "description": "The event in U.S. history."}, "year": {"type": "integer", "description": "The specific year of the event."}}, "required": ["event", "year"]}}]}, "model_result_raw": "\n\n[us_history.get_president(event=\"American Civil War\", year=1861)]", "model_result_decoded": [{"us_history.get_president": {"event": "American Civil War", "year": 1861}}], "possible_answer": [{"us_history.get_president": {"event": ["American Civil War"], "year": [1861, 1862, 1863, 1864, 1865]}}]}
{"id": "simple_239", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'US_president_in_year' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_239", "question": [[{"role": "user", "content": "Who was the full name of the president of the United States in 1861?"}]], "function": [{"name": "US_president.in_year", "description": "Retrieve the name of the U.S. president in a given year.", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "The year in question."}, "full_name": {"type": "boolean", "default": true, "description": "Option to return full name with middle initial, if applicable."}}, "required": ["year"]}}]}, "model_result_raw": "\n\n[US_president.in_year(year=1861, full_name=True)]", "model_result_decoded": [{"US_president.in_year": {"year": 1861, "full_name": true}}], "possible_answer": [{"US_president.in_year": {"year": [1861], "full_name": [true, ""]}}]}
{"id": "simple_240", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'history_api_get_president_by_year' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_240", "question": [[{"role": "user", "content": "Who was the President of the United States in 1940?"}]], "function": [{"name": "history_api.get_president_by_year", "description": "Get the name of the U.S. President for a specified year.", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "The year you want to know the U.S. president of."}, "full_term_only": {"type": "boolean", "description": "Flag to determine if we should only return presidents that served a full term for the specified year.", "default": false}}, "required": ["year"]}}]}, "model_result_raw": "\n\n[history_api.get_president_by_year(year=1940)]", "model_result_decoded": [{"history_api.get_president_by_year": {"year": 1940}}], "possible_answer": [{"history_api.get_president_by_year": {"year": [1940], "full_term_only": ["", true, false]}}]}
{"id": "simple_244", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'publication_year_find' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_244", "question": [[{"role": "user", "content": "What year was the law of universal gravitation published by Isaac Newton?"}]], "function": [{"name": "publication_year.find", "description": "Fetches the year a particular scientific work was published.", "parameters": {"type": "dict", "properties": {"author": {"type": "string", "description": "Name of the author of the work."}, "work_title": {"type": "string", "description": "Title of the scientific work."}, "location": {"type": "string", "description": "Place of the publication, if known. Default to 'all'."}}, "required": ["author", "work_title"]}}]}, "model_result_raw": "\n\n[publication_year.find(author=\"Isaac Newton\", work_title=\"law of universal gravitation\")]", "model_result_decoded": [{"publication_year.find": {"author": "Isaac Newton", "work_title": "law of universal gravitation"}}], "possible_answer": [{"publication_year.find": {"author": ["Isaac Newton"], "work_title": ["Law of Universal Gravitation", "Universal Law of Gravitation", "The law of universal gravitation"], "location": ["", "all"]}}]}
{"id": "simple_245", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'discoverer_get' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_245", "question": [[{"role": "user", "content": "Who discovered radium?"}]], "function": [{"name": "discoverer.get", "description": "Retrieve the name of the discoverer of an element based on its name.", "parameters": {"type": "dict", "properties": {"element_name": {"type": "string", "description": "The name of the element."}, "year": {"type": "integer", "description": "Optional parameter that refers to the year of discovery. It could be helpful in case an element was discovered more than once. Default to 0, which means not use it."}, "first": {"type": "boolean", "default": true, "description": "Optional parameter indicating if the first discoverer's name should be retrieved."}}, "required": ["element_name"]}}]}, "model_result_raw": "\n\n[discoverer.get(element_name=\"radium\")]", "model_result_decoded": [{"discoverer.get": {"element_name": "radium"}}], "possible_answer": [{"discoverer.get": {"element_name": ["'radium'", "\"radium\"", "radium"], "year": ["", 0], "first": [true, ""]}}]}
{"id": "simple_246", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'science_history_get_discovery_details' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_246", "question": [[{"role": "user", "content": "Who discovered Gravity and what was the method used?"}]], "function": [{"name": "science_history.get_discovery_details", "description": "Retrieve the details of a scientific discovery based on the discovery name.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The name of the discovery, e.g. Gravity"}, "method_used": {"type": "string", "description": "The method used for the discovery, default value is 'default' which gives the most accepted method."}}, "required": ["discovery"]}}]}, "model_result_raw": "\n\n[science_history.get_discovery_details(discovery=\"Gravity\", method_used=\"default\")]", "model_result_decoded": [{"science_history.get_discovery_details": {"discovery": "Gravity", "method_used": "default"}}], "possible_answer": [{"science_history.get_discovery_details": {"discovery": ["Gravity"], "method_used": ["", "default"]}}]}
{"id": "simple_247", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'historical_contrib_get_contrib' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_247", "question": [[{"role": "user", "content": "What was Albert Einstein's contribution to science on March 17, 1915?"}]], "function": [{"name": "historical_contrib.get_contrib", "description": "Retrieve historical contribution made by a scientist on a specific date.", "parameters": {"type": "dict", "properties": {"scientist": {"type": "string", "description": "The scientist whose contributions need to be searched."}, "date": {"type": "string", "description": "The date when the contribution was made in yyyy-mm-dd format."}, "category": {"type": "string", "description": "The field of the contribution, such as 'Physics' or 'Chemistry'. Default is 'all'."}}, "required": ["scientist", "date"]}}]}, "model_result_raw": "\n\n[historical_contrib.get_contrib(scientist=\"Albert Einstein\", date=\"1915-03-17\", category=\"Science\")]", "model_result_decoded": [{"historical_contrib.get_contrib": {"scientist": "Albert Einstein", "date": "1915-03-17", "category": "Science"}}], "possible_answer": [{"historical_contrib.get_contrib": {"scientist": ["Albert Einstein"], "date": ["1915-03-17", "03/17/1915", "Mar.17,1915"], "category": ["", "all"]}}]}
{"id": "simple_248", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'science_history_get_invention' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_248", "question": [[{"role": "user", "content": "Who invented the theory of relativity and in which year?"}]], "function": [{"name": "science_history.get_invention", "description": "Retrieve the inventor and year of invention based on the invention's name.", "parameters": {"type": "dict", "properties": {"invention_name": {"type": "string", "description": "The name of the invention."}, "want_year": {"type": "boolean", "default": false, "description": "Return the year of invention if set to true."}}, "required": ["invention_name", "want_year"]}}]}, "model_result_raw": "\n\n[science_history.get_invention(invention_name=\"theory of relativity\", want_year=True)]", "model_result_decoded": [{"science_history.get_invention": {"invention_name": "theory of relativity", "want_year": true}}], "possible_answer": [{"science_history.get_invention": {"invention_name": ["theory of relativity", "Theory of Relativity"], "want_year": [true]}}]}
{"id": "simple_249", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'religion_history_info' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_249", "question": [[{"role": "user", "content": "Tell me more about Christianity and its history till the 14th century"}]], "function": [{"name": "religion.history_info", "description": "Provides comprehensive historical details about a specified religion till a specified century.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "The name of the religion for which historical details are needed."}, "till_century": {"type": "integer", "description": "The century till which historical details are needed."}, "include_people": {"type": "boolean", "description": "To include influential people related to the religion during that time period, default is False"}}, "required": ["religion", "till_century"]}}]}, "model_result_raw": "\n\n[religion.history_info(religion=\"Christianity\", till_century=14)]", "model_result_decoded": [{"religion.history_info": {"religion": "Christianity", "till_century": 14}}], "possible_answer": [{"religion.history_info": {"religion": ["Christianity"], "till_century": [14], "include_people": [false, ""]}}]}
{"id": "simple_255", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'religious_history_get_papal_biography' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_255", "question": [[{"role": "user", "content": "Get the biography and main contributions of Pope Innocent III."}]], "function": [{"name": "religious_history.get_papal_biography", "description": "Retrieve the biography and main religious and historical contributions of a Pope based on his papal name.", "parameters": {"type": "dict", "properties": {"papal_name": {"type": "string", "description": "The papal name of the Pope."}, "include_contributions": {"type": "boolean", "default": false, "description": "Include main contributions of the Pope in the response if true."}}, "required": ["papal_name", "include_contributions"]}}]}, "model_result_raw": "\n\n[religious_history.get_papal_biography(papal_name=\"Innocent III\", include_contributions=True)]", "model_result_decoded": [{"religious_history.get_papal_biography": {"papal_name": "Innocent III", "include_contributions": true}}], "possible_answer": [{"religious_history.get_papal_biography": {"papal_name": ["Innocent III", "Pope Innocent III"], "include_contributions": [true]}}]}
{"id": "simple_260", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'paint_requirement_calculate' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_260", "question": [[{"role": "user", "content": "Calculate how many gallons of paint is required to paint a wall with width of 20ft and height of 12ft, assuming 1 gallon covers approximately 350 sq.ft. Don't include window area of 15 sq.ft."}]], "function": [{"name": "paint_requirement.calculate", "description": "Calculate the amount of paint required to paint a given area. Account for coverage efficiency of the paint and exclusions (like windows).", "parameters": {"type": "dict", "properties": {"area": {"type": "dict", "properties": {"width": {"type": "integer", "description": "The width of the area to be painted in feet."}, "height": {"type": "integer", "description": "The height of the area to be painted in feet."}}, "description": "The area to be painted."}, "paint_coverage": {"type": "integer", "description": "Coverage area per gallon of the paint in square feet.", "default": 350}, "exclusion": {"type": "dict", "properties": {"type": {"type": "string", "description": "The type of the exclusion e.g window, door etc."}, "area": {"type": "integer", "description": "The area of the exclusion in square feet."}}, "description": "Area not to be painted. Default to not use any exclusion if not specified."}}, "required": ["area", "paint_coverage"]}}]}, "model_result_raw": "\n\n[paint_requirement.calculate(area={'width': 20, 'height': 12}, paint_coverage=350, exclusion={'type': 'window', 'area': 15})]", "model_result_decoded": [{"paint_requirement.calculate": {"area": {"width": 20, "height": 12}, "paint_coverage": 350, "exclusion": {"type": "window", "area": 15}}}], "possible_answer": [{"paint_requirement.calculate": {"area": [{"width": [20], "height": [12]}], "paint_coverage": [350], "exclusion": [{"type": ["window"], "area": [15]}]}}]}
{"id": "simple_264", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sculpture_get_details' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_264", "question": [[{"role": "user", "content": "Find the size of the sculpture with title 'David' by Michelangelo."}]], "function": [{"name": "sculpture.get_details", "description": "Retrieve details of a sculpture based on the artist and the title of the sculpture.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "The artist who made the sculpture."}, "title": {"type": "string", "description": "The title of the sculpture."}, "detail": {"type": "string", "description": "The specific detail wanted about the sculpture. Default is 'general information'."}}, "required": ["artist", "title"]}}]}, "model_result_raw": "\n\n[sculpture.get_details(artist=\"Michelangelo\", title=\"David\", detail=\"size\")]", "model_result_decoded": [{"sculpture.get_details": {"artist": "Michelangelo", "title": "David", "detail": "size"}}], "possible_answer": [{"sculpture.get_details": {"artist": ["Michelangelo"], "title": ["David"], "detail": ["size"]}}]}
{"id": "simple_268", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sculpture_locator_find_by_artist' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_268", "question": [[{"role": "user", "content": "Find me the sculptures of Michelangelo with material Marble in Rome, Italy."}]], "function": [{"name": "sculpture_locator.find_by_artist", "description": "Locate the sculptures of specific artist by material and location", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the Artist of the sculpture"}, "material": {"type": "string", "description": "Material of the sculpture."}, "location": {"type": "string", "description": "The location where you want to find the sculpture. Default is 'all' if not specified."}}, "required": ["artist", "material"]}}]}, "model_result_raw": "\n\n[sculpture_locator.find_by_artist(artist=\"Michelangelo\", material=\"Marble\", location=\"Rome, Italy\")]", "model_result_decoded": [{"sculpture_locator.find_by_artist": {"artist": "Michelangelo", "material": "Marble", "location": "Rome, Italy"}}], "possible_answer": [{"sculpture_locator.find_by_artist": {"artist": ["Michelangelo"], "material": ["Marble"], "location": ["Rome", "Rome, Italy"]}}]}
{"id": "simple_270", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'building_get_dimensions' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_270", "question": [[{"role": "user", "content": "Can you give me the height and width of Empire State building in feet?"}]], "function": [{"name": "building.get_dimensions", "description": "Retrieve the dimensions of a specific building based on its name.", "parameters": {"type": "dict", "properties": {"building_name": {"type": "string", "description": "The name of the building."}, "unit": {"type": "string", "description": "The unit in which you want the dimensions. Default is meter.", "enum": ["meter", "feet"]}}, "required": ["building_name", "unit"]}}]}, "model_result_raw": "\n\n[building.get_dimensions(building_name=\"Empire State Building\", unit=\"feet\")]", "model_result_decoded": [{"building.get_dimensions": {"building_name": "Empire State Building", "unit": "feet"}}], "possible_answer": [{"building.get_dimensions": {"building_name": ["Empire State Building", "Empire State"], "unit": ["feet"]}}]}
{"id": "simple_273", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'museum_get_hours' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_273", "question": [[{"role": "user", "content": "Find out the open hours for the Louvre Museum in Paris."}]], "function": [{"name": "museum.get_hours", "description": "Retrieve the open hours for a museum based on its name and location.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the museum."}, "location": {"type": "string", "description": "The city where the museum is located."}, "day": {"type": "string", "description": "Optional: Day of the week for specific open hours. Default 'Monday'."}}, "required": ["name", "location"]}}]}, "model_result_raw": "\n\n[museum.get_hours(name=\"Louvre Museum\", location=\"Paris\")]", "model_result_decoded": [{"museum.get_hours": {"name": "Louvre Museum", "location": "Paris"}}], "possible_answer": [{"museum.get_hours": {"name": ["Louvre Museum"], "location": ["Paris", "Paris, France"], "day": ["", "Monday"]}}]}
{"id": "simple_275", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'metropolitan_museum_get_top_artworks' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_275", "question": [[{"role": "user", "content": "Get the list of top 5 popular artworks at the Metropolitan Museum of Art. Please sort by popularity."}]], "function": [{"name": "metropolitan_museum.get_top_artworks", "description": "Fetches the list of popular artworks at the Metropolitan Museum of Art. Results can be sorted based on popularity.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number of artworks to fetch"}, "sort_by": {"type": "string", "description": "The criteria to sort the results on. Default is 'popularity'.", "enum": ["popularity", "chronological", "alphabetical"]}}, "required": ["number"]}}]}, "model_result_raw": "\n\n[metropolitan_museum.get_top_artworks(number=5, sort_by='popularity')]", "model_result_decoded": [{"metropolitan_museum.get_top_artworks": {"number": 5, "sort_by": "popularity"}}], "possible_answer": [{"metropolitan_museum.get_top_artworks": {"number": [5], "sort_by": ["popularity", ""]}}]}
{"id": "simple_276", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'museum_working_hours_get' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_276", "question": [[{"role": "user", "content": "Get the working hours of Louvre Museum in Paris."}]], "function": [{"name": "museum_working_hours.get", "description": "Get the working hours of a museum in a specific location.", "parameters": {"type": "dict", "properties": {"museum": {"type": "string", "description": "The name of the museum."}, "location": {"type": "string", "description": "The location of the museum."}, "day": {"type": "string", "description": "Specific day of the week. Default is 'Monday'"}}, "required": ["museum", "location"]}}]}, "model_result_raw": "\n\n[museum_working_hours.get(museum=\"Louvre Museum\", location=\"Paris\")]", "model_result_decoded": [{"museum_working_hours.get": {"museum": "Louvre Museum", "location": "Paris"}}], "possible_answer": [{"museum_working_hours.get": {"museum": ["Louvre Museum", "Louvre"], "location": ["Paris", "Paris, France"], "day": ["", "Monday"]}}]}
{"id": "simple_279", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'instrument_price_get' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_279", "question": [[{"role": "user", "content": "What's the retail price of a Fender American Professional II Stratocaster in Rosewood Finish?"}]], "function": [{"name": "instrument_price.get", "description": "Retrieve the current retail price of a specific musical instrument.", "parameters": {"type": "dict", "properties": {"brand": {"type": "string", "description": "The brand of the instrument."}, "model": {"type": "string", "description": "The specific model of the instrument."}, "finish": {"type": "string", "description": "The color or type of finish on the instrument."}}, "required": ["brand", "model", "finish"]}}]}, "model_result_raw": "\n\n[instrument_price.get(brand=\"Fender\", model=\"American Professional II Stratocaster\", finish=\"Rosewood\")]", "model_result_decoded": [{"instrument_price.get": {"brand": "Fender", "model": "American Professional II Stratocaster", "finish": "Rosewood"}}], "possible_answer": [{"instrument_price.get": {"brand": ["Fender"], "model": ["American Professional II Stratocaster"], "finish": ["Rosewood"]}}]}
{"id": "simple_283", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'guitar_price_find' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_283", "question": [[{"role": "user", "content": "Find the price of a used Gibson Les Paul guitar in excellent condition in the Chicago area."}]], "function": [{"name": "guitar_price.find", "description": "Retrieve the price of a specific used guitar model based on its condition and location.", "parameters": {"type": "dict", "properties": {"model": {"type": "string", "description": "The model of the guitar."}, "condition": {"type": "string", "enum": ["Poor", "Good", "Excellent"], "description": "The condition of the guitar."}, "location": {"type": "string", "description": "The location where the guitar is being sold."}}, "required": ["model", "condition", "location"]}}]}, "model_result_raw": "\n\n[guitar_price.find(model=\"Gibson Les Paul\", condition=\"Excellent\", location=\"Chicago area\")]", "model_result_decoded": [{"guitar_price.find": {"model": "Gibson Les Paul", "condition": "Excellent", "location": "Chicago area"}}], "possible_answer": [{"guitar_price.find": {"model": ["Gibson Les Paul"], "condition": ["Excellent"], "location": ["Chicago", "Chicago, IL", "Chicago, Illinois"]}}]}
{"id": "simple_284", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'concert_info_get' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_284", "question": [[{"role": "user", "content": "Get information about the pop concerts in New York for next month."}]], "function": [{"name": "concert_info.get", "description": "Retrieve information about concerts based on specific genre, location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the concert will take place."}, "date": {"type": "string", "description": "Time frame to get the concert for."}, "genre": {"type": "string", "description": "Genre of the concert.", "enum": ["Pop", "Rock", "Country", "Classical", "Electronic", "Hip-Hop"]}}, "required": ["location", "date", "genre"]}}]}, "model_result_raw": "\n\n[concert_info.get(location=\"New York\", date=\"next month\", genre=\"Pop\")]", "model_result_decoded": [{"concert_info.get": {"location": "New York", "date": "next month", "genre": "Pop"}}], "possible_answer": [{"concert_info.get": {"location": ["New York City, NY", "New York"], "date": ["next month", "2023-06-01", "06/01/2023", "Jun.1,2023", "June 2023"], "genre": ["Pop"]}}]}
{"id": "simple_286", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'concert_get_details' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_286", "question": [[{"role": "user", "content": "Get concert details for the artist Beyonce performing in San Diego next month (April 2022)."}]], "function": [{"name": "concert.get_details", "description": "Fetch the details for a particular concert based on the artist and location.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the artist/band who's performing."}, "location": {"type": "string", "description": "City where the concert is taking place."}, "date": {"type": "string", "description": "Date of the concert in 'mm-yyyy' format. Default is the current month if not specified."}}, "required": ["artist", "location"]}}]}, "model_result_raw": "\n\n[concert.get_details(artist=\"Beyonce\", location=\"San Diego\", date=\"04-2022\")]", "model_result_decoded": [{"concert.get_details": {"artist": "Beyonce", "location": "San Diego", "date": "04-2022"}}], "possible_answer": [{"concert.get_details": {"artist": ["Beyonce"], "location": ["San Diego", "San Diego, California", "CA"], "date": ["04-2022", "April 2022"]}}]}
{"id": "simple_287", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'concert_search' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_287", "question": [[{"role": "user", "content": "Find me a classical concert this weekend in Los Angeles with cheap tickets."}]], "function": [{"name": "concert.search", "description": "Locate a concert based on specific criteria like genre, location, and date.", "parameters": {"type": "dict", "properties": {"genre": {"type": "string", "description": "Genre of the concert."}, "location": {"type": "string", "description": "City of the concert."}, "date": {"type": "string", "description": "Date of the concert, e.g. this weekend, today, tomorrow.", "enum": ["this weekend", "next weekend", "this month", "next month", "today", "tomorrow", "the day after"]}, "price_range": {"type": "string", "enum": ["free", "cheap", "moderate", "expensive"], "description": "Expected price range of the concert tickets. Default is 'free'."}}, "required": ["genre", "location", "date"]}}]}, "model_result_raw": "\n\n[concert.search(genre=\"classical\", location=\"Los Angeles\", date=\"this weekend\", price_range=\"cheap\")]", "model_result_decoded": [{"concert.search": {"genre": "classical", "location": "Los Angeles", "date": "this weekend", "price_range": "cheap"}}], "possible_answer": [{"concert.search": {"genre": ["classical"], "location": ["Los Angeles", "LA"], "date": ["this weekend"], "price_range": ["cheap"]}}]}
{"id": "simple_288", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'concert_booking_book_ticket' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_288", "question": [[{"role": "user", "content": "Get me two tickets for next Eminem concert in New York City."}]], "function": [{"name": "concert_booking.book_ticket", "description": "Book concert tickets for a specific artist in a specified city.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "The artist you want to book tickets for."}, "city": {"type": "string", "description": "The city where the concert is."}, "num_tickets": {"type": "integer", "description": "Number of tickets required. Default is 1."}}, "required": ["artist", "city"]}}]}, "model_result_raw": "\n\n[concert_booking.book_ticket(artist=\"Eminem\", city=\"New York City\", num_tickets=2)]", "model_result_decoded": [{"concert_booking.book_ticket": {"artist": "Eminem", "city": "New York City", "num_tickets": 2}}], "possible_answer": [{"concert_booking.book_ticket": {"artist": ["Eminem"], "city": ["New York City", "New York City, NY", "NYC"], "num_tickets": [2]}}]}
{"id": "simple_289", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'concert_find_nearby' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_289", "question": [[{"role": "user", "content": "Find concerts near me in Seattle that plays jazz music."}]], "function": [{"name": "concert.find_nearby", "description": "Locate nearby concerts based on specific criteria like genre.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "genre": {"type": "string", "description": "Genre of music to be played at the concert."}}, "required": ["location", "genre"]}}]}, "model_result_raw": "\n\n[concert.find_nearby(location=\"Seattle, WA\", genre=\"jazz\")]", "model_result_decoded": [{"concert.find_nearby": {"location": "Seattle, WA", "genre": "jazz"}}], "possible_answer": [{"concert.find_nearby": {"location": ["Seattle", "Seattle, WA"], "genre": ["jazz", "Jazz"]}}]}
{"id": "simple_290", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'concert_find_details' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_290", "question": [[{"role": "user", "content": "What's the timing and location for The Weeknd's concert happening in December?"}]], "function": [{"name": "concert.find_details", "description": "Finds details of a concert event.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the artist performing."}, "month": {"type": "string", "description": "Month in which the concert is happening."}, "year": {"type": "integer", "description": "Year of the concert.", "default": 2022}}, "required": ["artist", "month"]}}]}, "model_result_raw": "\n\n[concert.find_details(artist=\"The Weeknd\", month=\"December\")]", "model_result_decoded": [{"concert.find_details": {"artist": "The Weeknd", "month": "December"}}], "possible_answer": [{"concert.find_details": {"artist": ["The Weeknd"], "month": ["December"], "year": ["", 2022]}}]}
{"id": "simple_291", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'music_generator_generate_melody' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_291", "question": [[{"role": "user", "content": "Generate a melody in C major scale, starting with the note C4, 16 measures long, at 120 beats per minute."}]], "function": [{"name": "music_generator.generate_melody", "description": "Generate a melody based on certain musical parameters.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key of the melody. E.g., 'C' for C major."}, "start_note": {"type": "string", "description": "The first note of the melody, specified in scientific pitch notation. E.g., 'C4'."}, "length": {"type": "integer", "description": "The number of measures in the melody."}, "tempo": {"type": "integer", "description": "The tempo of the melody, in beats per minute. Optional parameter. If not specified, defaults to 120."}}, "required": ["key", "start_note", "length"]}}]}, "model_result_raw": "\n\n[music_generator.generate_melody(key='C', start_note='C4', length=16, tempo=120)]", "model_result_decoded": [{"music_generator.generate_melody": {"key": "C", "start_note": "C4", "length": 16, "tempo": 120}}], "possible_answer": [{"music_generator.generate_melody": {"key": ["C"], "start_note": ["C4"], "length": [16], "tempo": [120, ""]}}]}
{"id": "simple_293", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'music_composer_create_mix' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_293", "question": [[{"role": "user", "content": "Create a mix track using notes of C major scale and duration of each note being quarter of a second with a duration of 3 minutes."}]], "function": [{"name": "music_composer.create_mix", "description": "Create a mix of a song based on a particular music scale and duration", "parameters": {"type": "dict", "properties": {"scale": {"type": "string", "description": "The musical scale to be used. E.g: C Major, A Minor, etc."}, "note_duration": {"type": "string", "description": "Duration of each note. Options: 'whole', 'half', 'quarter', 'eighth', 'sixteenth'.", "enum": ["whole", "half", "quarter", "eighth", "sixteenth"]}, "track_length": {"type": "integer", "description": "Length of the mix track in seconds."}}, "required": ["scale", "note_duration", "track_length"]}}]}, "model_result_raw": "\n\n[music_composer.create_mix(scale='C Major', note_duration='quarter', track_length=180)]", "model_result_decoded": [{"music_composer.create_mix": {"scale": "C Major", "note_duration": "quarter", "track_length": 180}}], "possible_answer": [{"music_composer.create_mix": {"scale": ["C Major"], "note_duration": ["quarter"], "track_length": [180]}}]}
{"id": "simple_294", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'music_generation_create_chord_progression' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_294", "question": [[{"role": "user", "content": "Generate a major chord progression in C key with four chords."}]], "function": [{"name": "music_generation.create_chord_progression", "description": "Create a chord progression in a specific key and number of chords.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key for the chord progression."}, "chords": {"type": "integer", "description": "Number of chords in the progression."}, "progression_type": {"type": "string", "description": "The type of the chord progression. Optional parameter. Default is 'major'."}}, "required": ["key", "chords"]}}]}, "model_result_raw": "\n\n[music_generation.create_chord_progression(key='C', chords=4)]", "model_result_decoded": [{"music_generation.create_chord_progression": {"key": "C", "chords": 4}}], "possible_answer": [{"music_generation.create_chord_progression": {"key": ["C"], "chords": [4], "progression_type": ["major", ""]}}]}
{"id": "simple_296", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'music_generator_generate_scale_progression' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_296", "question": [[{"role": "user", "content": "Generate a major C scale progression with tempo 80 BPM and duration 4 beats."}]], "function": [{"name": "music_generator.generate_scale_progression", "description": "Generate a music scale progression in a specific key with a given tempo and duration.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key in which to generate the scale progression."}, "tempo": {"type": "integer", "description": "The tempo of the scale progression in BPM."}, "duration": {"type": "integer", "description": "The duration of each note in beats."}, "scale_type": {"type": "string", "default": "major", "description": "The type of scale to generate. Defaults to 'major'."}}, "required": ["key", "tempo", "duration"]}}]}, "model_result_raw": "\n\n[music_generator.generate_scale_progression(key='C', tempo=80, duration=4, scale_type='major')]", "model_result_decoded": [{"music_generator.generate_scale_progression": {"key": "C", "tempo": 80, "duration": 4, "scale_type": "major"}}], "possible_answer": [{"music_generator.generate_scale_progression": {"key": ["C"], "tempo": [80], "duration": [4], "scale_type": ["major", ""]}}]}
{"id": "simple_297", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'music_theory_chordProgression' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_297", "question": [[{"role": "user", "content": "music.theory.chordProgression(progression=['I', 'V', 'vi', 'IV'])"}]], "function": [{"name": "music.theory.chordProgression", "description": "Identifies a potential key signature for the given chord progression.", "parameters": {"type": "dict", "properties": {"progression": {"type": "array", "items": {"type": "string"}, "description": "The chord progression in Roman numerals. Eg: ['I', 'V', 'vi', 'IV']."}, "returnAllPossibleKeys": {"type": "boolean", "description": "Flag indicating if the function should return all possible key signatures that fit the chord progression. If false, the function will return the first valid key it finds. Default is false."}, "assumeMajor": {"type": "boolean", "description": "Assumption if the key signature is Major. If true, the function will assume the key signature to be major and otherwise minor. Default is true."}}, "required": ["progression"]}}]}, "model_result_raw": "\n\n[music.theory.chordProgression(progression=['I', 'V', 'vi', 'IV'])]", "model_result_decoded": [{"music.theory.chordProgression": {"progression": ["I", "V", "vi", "IV"]}}], "possible_answer": [{"music.theory.chordProgression": {"progression": [["I", "V", "vi", "IV"]], "returnAllPossibleKeys": [true, false, ""], "assumeMajor": [true, false, ""]}}]}
{"id": "simple_298", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'music_theory_key_signature' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_298", "question": [[{"role": "user", "content": "What key signature does C# major have?"}]], "function": [{"name": "music_theory.key_signature", "description": "Return the key signature of a major or minor scale.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The root of the scale, e.g., 'C', 'F#', 'Ab'."}, "scale_type": {"type": "string", "enum": ["major", "minor"], "description": "Type of the scale, either 'major' or 'minor'. Default is 'major'."}}, "required": ["key"]}}]}, "model_result_raw": "\n\n[music_theory.key_signature(key='C#', scale_type='major')]", "model_result_decoded": [{"music_theory.key_signature": {"key": "C#", "scale_type": "major"}}], "possible_answer": [{"music_theory.key_signature": {"key": ["C#"], "scale_type": ["major", ""]}}]}
{"id": "simple_300", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'music_calculate_note_duration' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_300", "question": [[{"role": "user", "content": "Calculate the duration between two notes of 440Hz and 880Hz frequency based on harmonic rhythm."}]], "function": [{"name": "music.calculate_note_duration", "description": "Calculate the duration between two notes based on their frequencies and harmonic rhythm.", "parameters": {"type": "dict", "properties": {"first_note_frequency": {"type": "integer", "description": "The frequency of the first note in Hz."}, "second_note_frequency": {"type": "integer", "description": "The frequency of the second note in Hz."}, "tempo": {"type": "integer", "description": "The tempo of the music in beats per minute. Defaults to 120 beats per minute."}}, "required": ["first_note_frequency", "second_note_frequency"]}}]}, "model_result_raw": "\n\n[music.calculate_note_duration(first_note_frequency=440, second_note_frequency=880)]", "model_result_decoded": [{"music.calculate_note_duration": {"first_note_frequency": 440, "second_note_frequency": 880}}], "possible_answer": [{"music.calculate_note_duration": {"first_note_frequency": [440], "second_note_frequency": [880], "tempo": ["", 120]}}]}
{"id": "simple_303", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'soccer_stat_get_player_stats' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_303", "question": [[{"role": "user", "content": "Get the player stats of Cristiano Ronaldo in the 2019-2020 season"}]], "function": [{"name": "soccer_stat.get_player_stats", "description": "Retrieve soccer player statistics for a given season.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "Name of the player."}, "season": {"type": "string", "description": "Soccer season, usually specified by two years."}, "league": {"type": "string", "description": "Optional - the soccer league, defaults to all leagues if not specified."}}, "required": ["player_name", "season"]}}]}, "model_result_raw": "\n\n[soccer_stat.get_player_stats(player_name=\"Cristiano Ronaldo\", season=\"2019-2020\")]", "model_result_decoded": [{"soccer_stat.get_player_stats": {"player_name": "Cristiano Ronaldo", "season": "2019-2020"}}], "possible_answer": [{"soccer_stat.get_player_stats": {"player_name": ["Cristiano Ronaldo"], "season": ["2019-2020"], "league": [""]}}]}
{"id": "simple_304", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Invalid syntax. Failed to decode AST. invalid syntax (<unknown>, line 1)"], "error_type": "ast_decoder:decoder_failed", "prompt": {"id": "simple_304", "question": [[{"role": "user", "content": "Get point and rebound stats for player 'LeBron James' from last basketball game"}]], "function": [{"name": "player_stats.getLastGame", "description": "Get last game statistics for a specific player in basketball", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the basketball player."}, "team": {"type": "string", "description": "The team that player currently plays for."}, "metrics": {"type": "array", "items": {"type": "string", "enum": ["Points", "Rebounds", "Assists", "Blocks"]}, "description": "Specific metrics to retrieve. If no value is specified, all available metrics will be returned by default."}}, "required": ["player_name", "team"]}}]}, "model_result_raw": "\n\nThe provided function requires the 'team' parameter, which is missing in the question. Please specify the team that LeBron James currently plays for.", "possible_answer": [{"player_stats.getLastGame": {"player_name": ["LeBron James"], "team": ["Los Angeles Lakers", "LAL", "Lakers"], "metrics": [["Points", "Rebounds"]]}}]}
{"id": "simple_305", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_stats_get_performance' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_305", "question": [[{"role": "user", "content": "Calculate the overall goal and assist of soccer player Messi in La Liga 2020-2021 season"}]], "function": [{"name": "sports_stats.get_performance", "description": "Compute the performance score of a soccer player given his game stats for a specific tournament in a season.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "Name of the player."}, "tournament": {"type": "string", "description": "Name of the soccer tournament."}, "season": {"type": "string", "description": "Specific season in format 'YYYY-YYYY'."}, "performance_indicator": {"type": "array", "items": {"type": "string", "enum": ["Goals Scored", "Assists Made", "Saves Made", "Cards Received"]}, "description": "Array of performance indicators. Use as much as possible. Default to use all if not specified."}}, "required": ["player_name", "tournament", "season"]}}]}, "model_result_raw": "\n\n[sports_stats.get_performance(player_name=\"Messi\", tournament=\"La Liga\", season=\"2020-2021\", performance_indicator=[\"Goals Scored\", \"Assists Made\"])]", "model_result_decoded": [{"sports_stats.get_performance": {"player_name": "Messi", "tournament": "La Liga", "season": "2020-2021", "performance_indicator": ["Goals Scored", "Assists Made"]}}], "possible_answer": [{"sports_stats.get_performance": {"player_name": ["Messi", "Lionel Messi"], "tournament": ["La Liga"], "season": ["2020-2021"], "performance_indicator": [["Goals Scored", "Assists Made"]]}}]}
{"id": "simple_307", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'game_result_get_winner' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_307", "question": [[{"role": "user", "content": "Who won the basketball game between Lakers and Clippers on Jan 28, 2021?"}]], "function": [{"name": "game_result.get_winner", "description": "Get the winner of a specific basketball game.", "parameters": {"type": "dict", "properties": {"teams": {"type": "array", "items": {"type": "string"}, "description": "List of two teams who played the game."}, "date": {"type": "string", "description": "The date of the game, formatted as YYYY-MM-DD."}, "venue": {"type": "string", "optional": true, "description": "Optional: The venue of the game. Default is 'home'."}}, "required": ["teams", "date"]}}]}, "model_result_raw": "\n\n[game_result.get_winner(teams=[\"Lakers\", \"Clippers\"], date=\"2021-01-28\")]", "model_result_decoded": [{"game_result.get_winner": {"teams": ["Lakers", "Clippers"], "date": "2021-01-28"}}], "possible_answer": [{"game_result.get_winner": {"teams": [["Lakers", "Clippers"], ["Clippers", "Lakers"]], "date": ["2021-01-28", "01/28/2021", "Jan.28,2021"], "venue": ["", true]}}]}
{"id": "simple_308", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_match_schedule' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_308", "question": [[{"role": "user", "content": "What are the next five matches for Manchester United and who are they playing against in the English Premier League?"}]], "function": [{"name": "sports.match_schedule", "description": "Retrieve the match schedule for a specific sports team.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "num_matches": {"type": "integer", "description": "The number of upcoming matches you want to get."}, "league": {"type": "string", "description": "The sports league of the team. This is an optional parameter. Default is 'English Premier League'."}}, "required": ["team_name", "num_matches"]}}]}, "model_result_raw": "\n\n[sports.match_schedule(team_name=\"Manchester United\", num_matches=5, league=\"English Premier League\")]", "model_result_decoded": [{"sports.match_schedule": {"team_name": "Manchester United", "num_matches": 5, "league": "English Premier League"}}], "possible_answer": [{"sports.match_schedule": {"team_name": ["Manchester United", "Man United", "Man U", "MUFC"], "num_matches": [5], "league": ["English Premier League", ""]}}]}
{"id": "simple_309", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'nfl_data_player_record' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_309", "question": [[{"role": "user", "content": "Find me the record of Tom Brady in the 2020 NFL season."}]], "function": [{"name": "nfl_data.player_record", "description": "Retrieve the record of an NFL player in a specified season.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the NFL player."}, "season_year": {"type": "integer", "description": "The year of the NFL season."}, "team": {"type": "string", "description": "The NFL team that the player played for in that season. Default is all teams if not specified."}}, "required": ["player_name", "season_year"]}}]}, "model_result_raw": "\n\n[nfl_data.player_record(player_name=\"Tom Brady\", season_year=2020)]", "model_result_decoded": [{"nfl_data.player_record": {"player_name": "Tom Brady", "season_year": 2020}}], "possible_answer": [{"nfl_data.player_record": {"player_name": ["Tom Brady"], "season_year": [2020], "team": ["", "Tampa Bay Buccaneers"]}}]}
{"id": "simple_311", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_db_find_athlete' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_311", "question": [[{"role": "user", "content": "Find me the detailed profile of basketball player Lebron James"}]], "function": [{"name": "sports_db.find_athlete", "description": "Find the profile information of a sports athlete based on their full name.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The full name of the athlete."}, "team": {"type": "string", "description": "The team the athlete belongs to. Default to all teams if not specified."}, "sport": {"type": "string", "description": "The sport that athlete plays.", "enum": ["Basketball", "Baseball", "Football", "Soccer"]}}, "required": ["name", "sport"]}}]}, "model_result_raw": "\n\n[sports_db.find_athlete(name='Lebron James', sport='Basketball')]", "model_result_decoded": [{"sports_db.find_athlete": {"name": "Lebron James", "sport": "Basketball"}}], "possible_answer": [{"sports_db.find_athlete": {"name": ["Lebron James"], "sport": ["Basketball"], "team": [""]}}]}
{"id": "simple_313", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'celebrity_net_worth_get' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_313", "question": [[{"role": "user", "content": "What's the total worth in euro of Messi according to latest data?"}]], "function": [{"name": "celebrity_net_worth.get", "description": "Get the total net worth of a sports celebrity based on most recent data.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The full name of the sports celebrity."}, "currency": {"type": "string", "description": "The currency in which the net worth will be returned. Default is 'USD'."}}, "required": ["name", "currency"]}}]}, "model_result_raw": "\n\n[celebrity_net_worth.get(name=\"Messi\", currency=\"EUR\")]", "model_result_decoded": [{"celebrity_net_worth.get": {"name": "Messi", "currency": "EUR"}}], "possible_answer": [{"celebrity_net_worth.get": {"name": ["Lionel Messi", "Messi"], "currency": ["EUR", "euro"]}}]}
{"id": "simple_314", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_celebrity_get_major_achievements' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_314", "question": [[{"role": "user", "content": "Find all the major achievements of the footballer Lionel Messi."}]], "function": [{"name": "sports_celebrity.get_major_achievements", "description": "Returns a list of major achievements of a particular sports celebrity.", "parameters": {"type": "dict", "properties": {"celebrity_name": {"type": "string", "description": "Name of the sports celebrity."}, "sports": {"type": "string", "description": "Type of sports the celebrity involved in. Default is Football."}, "team": {"type": "string", "description": "Optional. Team where celebrity currently plays. Default is 'all'"}}, "required": ["celebrity_name"]}}]}, "model_result_raw": "\n\n[sports_celebrity.get_major_achievements(celebrity_name=\"Lionel Messi\", sports=\"Football\")]", "model_result_decoded": [{"sports_celebrity.get_major_achievements": {"celebrity_name": "Lionel Messi", "sports": "Football"}}], "possible_answer": [{"sports_celebrity.get_major_achievements": {"celebrity_name": ["Lionel Messi", "Messi"], "sports": ["Football", "Soccer", ""], "team": ["", "all"]}}]}
{"id": "simple_320", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_ranking_get_team_position' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_320", "question": [[{"role": "user", "content": "Fetch the basketball league standings, where Golden State Warriors stand in current 2022-2023 season with details"}]], "function": [{"name": "sports_ranking.get_team_position", "description": "Retrieve a team's position and stats in the basketball league for a given season.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team."}, "season": {"type": "string", "description": "The season for which data should be fetched."}, "detailed": {"type": "boolean", "description": "Flag to retrieve detailed stats or just the position.", "default": false}}, "required": ["team", "season"]}}]}, "model_result_raw": "\n\n[sports_ranking.get_team_position(team=\"Golden State Warriors\", season=\"2022-2023\", detailed=True)]", "model_result_decoded": [{"sports_ranking.get_team_position": {"team": "Golden State Warriors", "season": "2022-2023", "detailed": true}}], "possible_answer": [{"sports_ranking.get_team_position": {"team": ["Golden State Warriors", "GSW"], "season": ["2022-2023"], "detailed": [true]}}]}
{"id": "simple_322", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_ranking_get_current' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_322", "question": [[{"role": "user", "content": "Get the current ranking for Liverpool Football Club in the Premier League."}]], "function": [{"name": "sports_ranking.get_current", "description": "Retrieve the current ranking of a specific team in a particular league.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team whose ranking is sought."}, "league": {"type": "string", "description": "The league in which the team participates."}, "season": {"type": "string", "description": "The season for which the ranking is sought. Defaults to the current season '2023-2024' if not provided."}}, "required": ["team", "league"]}}]}, "model_result_raw": "\n\n[sports_ranking.get_current(team=\"Liverpool Football Club\", league=\"Premier League\")]", "model_result_decoded": [{"sports_ranking.get_current": {"team": "Liverpool Football Club", "league": "Premier League"}}], "possible_answer": [{"sports_ranking.get_current": {"team": ["Liverpool Football Club", "Liverpool", "LFC"], "league": ["Premier League", "EPL", "English Premier League"], "season": ["", "2023-2024"]}}]}
{"id": "simple_323", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_ranking_get_top_player' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_323", "question": [[{"role": "user", "content": "Who is ranked as the top player in woman tennis?"}]], "function": [{"name": "sports_ranking.get_top_player", "description": "Get the top player in a specific sport.", "parameters": {"type": "dict", "properties": {"sport": {"type": "string", "description": "The type of sport."}, "gender": {"type": "string", "description": "The gender of the sport category. Optional.", "default": "men"}}, "required": ["sport"]}}]}, "model_result_raw": "\n\n[sports_ranking.get_top_player(sport=\"tennis\", gender=\"women\")]", "model_result_decoded": [{"sports_ranking.get_top_player": {"sport": "tennis", "gender": "women"}}], "possible_answer": [{"sports_ranking.get_top_player": {"sport": ["tennis"], "gender": ["women"]}}]}
{"id": "simple_324", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'team_score_get_latest' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_324", "question": [[{"role": "user", "content": "Find the score of last game for Los Angeles Lakers including its opponent name."}]], "function": [{"name": "team_score.get_latest", "description": "Retrieve the score of the most recent game for a specified sports team.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "Name of the sports team."}, "include_opponent": {"type": "boolean", "description": "Include the name of the opponent team in the return.", "default": false}}, "required": ["team"]}}]}, "model_result_raw": "\n\n[team_score.get_latest(team=\"Los Angeles Lakers\", include_opponent=True)]", "model_result_decoded": [{"team_score.get_latest": {"team": "Los Angeles Lakers", "include_opponent": true}}], "possible_answer": [{"team_score.get_latest": {"team": ["Los Angeles Lakers", "Lakers"], "include_opponent": [true]}}]}
{"id": "simple_325", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_match_results' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_325", "question": [[{"role": "user", "content": "Who won the last match between Chicago Bulls and Los Angeles Lakers?"}]], "function": [{"name": "sports.match_results", "description": "Returns the results of a given match between two teams.", "parameters": {"type": "dict", "properties": {"team1": {"type": "string", "description": "The name of the first team."}, "team2": {"type": "string", "description": "The name of the second team."}, "season": {"type": "string", "description": "The season when the match happened. Default is the current season."}}, "required": ["team1", "team2"]}}]}, "model_result_raw": "\n\n[sports.match_results(team1=\"Chicago Bulls\", team2=\"Los Angeles Lakers\")]", "model_result_decoded": [{"sports.match_results": {"team1": "Chicago Bulls", "team2": "Los Angeles Lakers"}}], "possible_answer": [{"sports.match_results": {"team1": ["Chicago Bulls"], "team2": ["Los Angeles Lakers"], "season": [""]}}]}
{"id": "simple_327", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'sports_team_get_schedule' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_327", "question": [[{"role": "user", "content": "Give me the schedule of Manchester United for the next 6 games in Premier League."}]], "function": [{"name": "sports_team.get_schedule", "description": "Fetches the schedule of the specified sports team for the specified number of games in the given league.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "num_of_games": {"type": "integer", "description": "Number of games for which to fetch the schedule."}, "league": {"type": "string", "description": "The name of the sports league. If not provided, the function will fetch the schedule for all games, regardless of the league."}, "location": {"type": "string", "description": "Optional. The city or venue where games are to be held. If not provided, default that all venues will be considered."}}, "required": ["team_name", "num_of_games", "league"]}}]}, "model_result_raw": "\n\n[sports_team.get_schedule(team_name=\"Manchester United\", num_of_games=6, league=\"Premier League\")]", "model_result_decoded": [{"sports_team.get_schedule": {"team_name": "Manchester United", "num_of_games": 6, "league": "Premier League"}}], "possible_answer": [{"sports_team.get_schedule": {"team_name": ["Manchester United", "Man United", "Man U", "MUFC"], "num_of_games": [6], "league": ["Premier League"], "location": [""]}}]}
{"id": "simple_328", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'boardgame_get_info' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_328", "question": [[{"role": "user", "content": "Find the rating and player count of the board game 'Ticket to Ride'."}]], "function": [{"name": "boardgame.get_info", "description": "Retrieve detailed information of a board game.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "Name of the board game."}, "parameters": {"type": "array", "items": {"type": "string", "enum": ["player count", "playing time", "age", "mechanics", "rating"]}, "description": "Game characteristics interested."}, "language": {"type": "string", "description": "The preferred language for the game information, default is English"}}, "required": ["name", "parameters"]}}]}, "model_result_raw": "\n\n[boardgame.get_info(name='Ticket to Ride', parameters=['player count', 'rating'])]", "model_result_decoded": [{"boardgame.get_info": {"name": "Ticket to Ride", "parameters": ["player count", "rating"]}}], "possible_answer": [{"boardgame.get_info": {"name": ["Ticket to Ride"], "parameters": [["rating", "player count"], ["player count", "rating"]], "language": ["", "English"]}}]}
{"id": "simple_331", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'board_game_chess_get_top_players' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_331", "question": [[{"role": "user", "content": "Find the top chess players in New York with a rating above 2300."}]], "function": [{"name": "board_game.chess.get_top_players", "description": "Find top chess players in a location based on rating.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city you want to find the players from."}, "minimum_rating": {"type": "integer", "description": "Minimum rating to filter the players."}, "number_of_players": {"type": "integer", "default": 10, "description": "Number of players you want to retrieve, default value is 10"}}, "required": ["location", "minimum_rating"]}}]}, "model_result_raw": "\n\n[board_game.chess.get_top_players(location=\"New York\", minimum_rating=2300)]", "model_result_decoded": [{"board_game.chess.get_top_players": {"location": "New York", "minimum_rating": 2300}}], "possible_answer": [{"board_game.chess.get_top_players": {"location": ["New York", "New York City", "New York City, NY", "NYC"], "minimum_rating": [2300], "number_of_players": ["", 10]}}]}
{"id": "simple_332", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'chess_rating' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_332", "question": [[{"role": "user", "content": "What's the chess classical rating of Magnus Carlsen?"}]], "function": [{"name": "chess.rating", "description": "Fetches the current chess rating of a given player", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The full name of the chess player."}, "variant": {"type": "string", "description": "The variant of chess for which rating is requested (e.g., 'classical', 'blitz', 'bullet'). Default is 'classical'."}}, "required": ["player_name"]}}]}, "model_result_raw": "\n\n[chess.rating(player_name=\"Magnus Carlsen\", variant=\"classical\")]", "model_result_decoded": [{"chess.rating": {"player_name": "Magnus Carlsen", "variant": "classical"}}], "possible_answer": [{"chess.rating": {"player_name": ["Magnus Carlsen"], "variant": ["classical", ""]}}]}
{"id": "simple_334", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'blackjack_check_winner' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_334", "question": [[{"role": "user", "content": "Check who is the winner in a game of blackjack given player having A and 10, dealer having 10 and 9. The Ace is considered 1."}]], "function": [{"name": "blackjack.check_winner", "description": "Checks and determines the winner in a game of blackjack.", "parameters": {"type": "dict", "properties": {"player_cards": {"type": "array", "items": {"type": "string"}, "description": "Cards held by the player."}, "dealer_cards": {"type": "array", "items": {"type": "string"}, "description": "Cards held by the dealer."}, "ace_value": {"type": "integer", "description": "The value considered for the ace card, can be either 1 or 11.", "default": 11}}, "required": ["player_cards", "dealer_cards"]}}]}, "model_result_raw": "\n\n[blackjack.check_winner(player_cards=[\"A\", \"10\"], dealer_cards=[\"10\", \"9\"], ace_value=1)]", "model_result_decoded": [{"blackjack.check_winner": {"player_cards": ["A", "10"], "dealer_cards": ["10", "9"], "ace_value": 1}}], "possible_answer": [{"blackjack.check_winner": {"player_cards": [["A", "10"]], "dealer_cards": [["10", "9"]], "ace_value": [1]}}]}
{"id": "simple_336", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'cards_shuffle_and_draw' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_336", "question": [[{"role": "user", "content": "Shuffle a deck of cards, and draw 3 cards from the top."}]], "function": [{"name": "cards.shuffle_and_draw", "description": "Shuffle a standard deck of 52 cards and draw a specified number of cards from the top.", "parameters": {"type": "dict", "properties": {"num_cards": {"type": "integer", "description": "Number of cards to be drawn. The default is 1 if no value is provided."}}, "required": ["num_cards"]}}]}, "model_result_raw": "\n\n[cards.shuffle_and_draw(num_cards=3)]", "model_result_decoded": [{"cards.shuffle_and_draw": {"num_cards": 3}}], "possible_answer": [{"cards.shuffle_and_draw": {"num_cards": [3]}}]}
{"id": "simple_338", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'card_game_probability_calculate' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_338", "question": [[{"role": "user", "content": "What is the probability of drawing a heart card from a deck of 52 cards?"}]], "function": [{"name": "card_game_probability.calculate", "description": "Calculate the probability of drawing a certain card or suit from a deck of cards.", "parameters": {"type": "dict", "properties": {"total_cards": {"type": "integer", "description": "Total number of cards in the deck."}, "desired_cards": {"type": "integer", "description": "Number of cards in the deck that satisfy the conditions."}, "cards_drawn": {"type": "integer", "default": 1, "description": "Number of cards drawn from the deck."}}, "required": ["total_cards", "desired_cards"]}}]}, "model_result_raw": "\n\n[card_game_probability.calculate(total_cards=52, desired_cards=13, cards_drawn=1)]", "model_result_decoded": [{"card_game_probability.calculate": {"total_cards": 52, "desired_cards": 13, "cards_drawn": 1}}], "possible_answer": [{"card_game_probability.calculate": {"total_cards": [52], "desired_cards": [13], "cards_drawn": ["", 1]}}]}
{"id": "simple_339", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'poker_probability_full_house' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_339", "question": [[{"role": "user", "content": "What is the probability of getting a full house in poker?"}]], "function": [{"name": "poker_probability.full_house", "description": "Calculate the probability of getting a full house in a poker game.", "parameters": {"type": "dict", "properties": {"deck_size": {"type": "integer", "description": "The size of the deck. Default is 52."}, "hand_size": {"type": "integer", "description": "The size of the hand. Default is 5."}}, "required": ["deck_size", "hand_size"]}}]}, "model_result_raw": "\n\n[poker_probability.full_house(deck_size=52, hand_size=5)]", "model_result_decoded": [{"poker_probability.full_house": {"deck_size": 52, "hand_size": 5}}], "possible_answer": [{"poker_probability.full_house": {"deck_size": [52], "hand_size": [5]}}]}
{"id": "simple_340", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'card_games_poker_determine_winner' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_340", "question": [[{"role": "user", "content": "Determine the winner in a Poker game with John having a Hand of 8\u2665, 10\u2665, J\u2665, Q\u2665, K\u2665 and Mike having 9\u2660, J\u2660, 10\u2660, Q\u2660, K\u2660."}]], "function": [{"name": "card_games.poker_determine_winner", "description": "Determines the winner in a game of Poker based on the cards in each players' hands.", "parameters": {"type": "dict", "properties": {"player1": {"type": "string", "description": "The first player's name."}, "hand1": {"type": "array", "items": {"type": "string"}, "description": "The list of cards (as strings) in first player's hand. E.g ['10\u2660', 'J\u2660']"}, "player2": {"type": "string", "description": "The second player's name."}, "hand2": {"type": "array", "items": {"type": "string"}, "description": "The list of cards (as strings) in second player's hand. E.g ['9\u2665', '10\u2665']"}}, "required": ["player1", "hand1", "player2", "hand2"]}}]}, "model_result_raw": "\n\n[poker_determine_winner(player1=\"John\", hand1=[\"8\u2665\", \"10\u2665\", \"J\u2665\", \"Q\u2665\", \"K\u2665\"], player2=\"Mike\", hand2=[\"9\u2660\", \"J\u2660\", \"10\u2660\", \"Q\u2660\", \"K\u2660\"])]", "model_result_decoded": [{"poker_determine_winner": {"player1": "John", "hand1": ["8\u2665", "10\u2665", "J\u2665", "Q\u2665", "K\u2665"], "player2": "Mike", "hand2": ["9\u2660", "J\u2660", "10\u2660", "Q\u2660", "K\u2660"]}}], "possible_answer": [{"card_games.poker_determine_winner": {"player1": ["John"], "hand1": [["8\u2665", "10\u2665", "J\u2665", "Q\u2665", "K\u2665"]], "player2": ["Mike"], "hand2": [["9\u2660", "J\u2660", "10\u2660", "Q\u2660", "K\u2660"]]}}]}
{"id": "simple_341", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'deck_of_cards_odds' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_341", "question": [[{"role": "user", "content": "What are the odds of drawing a heart card from a deck without joker?"}]], "function": [{"name": "deck_of_cards.odds", "description": "Compute the probability of drawing a specific suit from a given deck of cards.", "parameters": {"type": "dict", "properties": {"suit": {"type": "string", "description": "The card suit. Valid values include: 'spades', 'clubs', 'hearts', 'diamonds'."}, "deck_type": {"type": "string", "description": "Type of deck, normal deck includes joker, and without_joker deck excludes joker.", "default": "normal"}}, "required": ["suit", "deck_type"]}}]}, "model_result_raw": "\n\n[deck_of_cards.odds(suit='hearts', deck_type='without_joker')]", "model_result_decoded": [{"deck_of_cards.odds": {"suit": "hearts", "deck_type": "without_joker"}}], "possible_answer": [{"deck_of_cards.odds": {"suit": ["hearts"], "deck_type": ["without_joker", "normal"]}}]}
{"id": "simple_342", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'game_list_get_games' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_342", "question": [[{"role": "user", "content": "Find all multi-player games released in 2019 with an ESRB rating of 'Everyone'"}]], "function": [{"name": "game_list.get_games", "description": "Get a list of video games based on release year, multiplayer functionality and ESRB rating", "parameters": {"type": "dict", "properties": {"release_year": {"type": "integer", "description": "The year the game was released."}, "multiplayer": {"type": "boolean", "description": "Whether the game has multiplayer functionality."}, "ESRB_rating": {"type": "string", "description": "The ESRB rating of the game."}}, "required": ["release_year", "multiplayer", "ESRB_rating"]}}]}, "model_result_raw": "\n\n[game_list.get_games(release_year=2019, multiplayer=True, ESRB_rating='Everyone')]", "model_result_decoded": [{"game_list.get_games": {"release_year": 2019, "multiplayer": true, "ESRB_rating": "Everyone"}}], "possible_answer": [{"game_list.get_games": {"release_year": [2019], "multiplayer": [true], "ESRB_rating": ["Everyone"]}}]}
{"id": "simple_343", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'game_stats_fetch_player_statistics' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_343", "question": [[{"role": "user", "content": "Fetch player statistics of 'Zelda' on Switch for user 'Sam'."}]], "function": [{"name": "game_stats.fetch_player_statistics", "description": "Fetch player statistics for a specific video game for a given user.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the video game."}, "username": {"type": "string", "description": "The username of the player."}, "platform": {"type": "string", "description": "The platform user is playing on.", "default": "PC"}}, "required": ["game", "username"]}}]}, "model_result_raw": "\n\n[game_stats.fetch_player_statistics(game='Zelda', username='Sam', platform='Switch')]", "model_result_decoded": [{"game_stats.fetch_player_statistics": {"game": "Zelda", "username": "Sam", "platform": "Switch"}}], "possible_answer": [{"game_stats.fetch_player_statistics": {"game": ["Zelda"], "username": ["Sam"], "platform": ["Switch"]}}]}
{"id": "simple_347", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'soccer_get_last_match' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_347", "question": [[{"role": "user", "content": "Get me the details of the last game played by Liverpool F.C. Include its statistics."}]], "function": [{"name": "soccer.get_last_match", "description": "Retrieve the details of the last match played by a specified soccer club.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the soccer club."}, "include_stats": {"type": "boolean", "description": "If true, include match statistics like possession, shots on target etc. Default is false."}}, "required": ["team_name"]}}]}, "model_result_raw": "\n\n[soccer.get_last_match(team_name=\"Liverpool F.C.\", include_stats=True)]", "model_result_decoded": [{"soccer.get_last_match": {"team_name": "Liverpool F.C.", "include_stats": true}}], "possible_answer": [{"soccer.get_last_match": {"team_name": ["Liverpool F.C.", "Liverpool"], "include_stats": [true]}}]}
{"id": "simple_349", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'game_score_highest' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_349", "question": [[{"role": "user", "content": "Find the highest score achieved by any player in the online game 'Overwatch' on PC globally."}]], "function": [{"name": "game_score.highest", "description": "Retrieve the highest score achieved by any player in a specific game.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the online game."}, "platform": {"type": "string", "description": "The platform where the game is played, e.g. PC, Xbox, Playstation"}, "region": {"type": "string", "description": "The geographic region of the player. Defaults to 'Global'"}}, "required": ["game", "platform"]}}]}, "model_result_raw": "\n\n[game_score.highest(game='Overwatch', platform='PC', region='Global')]", "model_result_decoded": [{"game_score.highest": {"game": "Overwatch", "platform": "PC", "region": "Global"}}], "possible_answer": [{"game_score.highest": {"game": ["Overwatch"], "platform": ["PC"], "region": ["Global", ""]}}]}
{"id": "simple_352", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'gamespot_getAverageUserScore' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_352", "question": [[{"role": "user", "content": "Get the average user score for the game 'The Legend of Zelda: Breath of the Wild' from GameSpot."}]], "function": [{"name": "gamespot.getAverageUserScore", "description": "Retrieve the average user score of a game from GameSpot.", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "The name of the game."}, "platform": {"type": "string", "description": "The platform the game was released on (e.g., Nintendo Switch, PS5, etc.)", "default": "all platforms"}}, "required": ["game_name", "platform"]}}]}, "model_result_raw": "\n\n[gamespot.getAverageUserScore(game_name='The Legend of Zelda: Breath of the Wild', platform='all platforms')]", "model_result_decoded": [{"gamespot.getAverageUserScore": {"game_name": "The Legend of Zelda: Breath of the Wild", "platform": "all platforms"}}], "possible_answer": [{"gamespot.getAverageUserScore": {"game_name": ["The Legend of Zelda: Breath of the Wild"], "platform": ["Nintendo Switch", "all platforms"]}}]}
{"id": "simple_355", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'recipe_info_get_calories' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_355", "question": [[{"role": "user", "content": "How many calories in the Beef Lasagna Recipe from Foodnetwork.com?"}]], "function": [{"name": "recipe_info.get_calories", "description": "Retrieve the amount of calories from a specific recipe in a food website.", "parameters": {"type": "dict", "properties": {"website": {"type": "string", "description": "The food website that has the recipe."}, "recipe": {"type": "string", "description": "Name of the recipe."}, "optional_meal_time": {"type": "string", "description": "Specific meal time of the day for the recipe (optional, could be 'Breakfast', 'Lunch', 'Dinner'). Default is all if not specified."}}, "required": ["website", "recipe"]}}]}, "model_result_raw": "\n\n[recipe_info.get_calories(website=\"Foodnetwork.com\", recipe=\"Beef Lasagna\")]", "model_result_decoded": [{"recipe_info.get_calories": {"website": "Foodnetwork.com", "recipe": "Beef Lasagna"}}], "possible_answer": [{"recipe_info.get_calories": {"website": ["Foodnetwork.com"], "recipe": ["Beef Lasagna"], "optional_meal_time": [""]}}]}
{"id": "simple_356", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'recipe_finder_find' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_356", "question": [[{"role": "user", "content": "Find me a recipe that serves 2 people, is vegan, and takes under 30 minutes to prepare."}]], "function": [{"name": "recipe_finder.find", "description": "Find a recipe based on dietary preferences, number of servings, and preparation time.", "parameters": {"type": "dict", "properties": {"servings": {"type": "integer", "description": "The number of people that the recipe should serve."}, "diet": {"type": "string", "description": "Any dietary restrictions like 'vegan', 'vegetarian', 'gluten-free' etc."}, "prep_time": {"type": "integer", "description": "The maximum amount of time (in minutes) the preparation should take. Default is 60 minutes."}}, "required": ["servings", "diet"]}}]}, "model_result_raw": "\n\n[recipe_finder.find(servings=2, diet='vegan', prep_time=30)]", "model_result_decoded": [{"recipe_finder.find": {"servings": 2, "diet": "vegan", "prep_time": 30}}], "possible_answer": [{"recipe_finder.find": {"servings": [2], "diet": ["vegan"], "prep_time": [30]}}]}
{"id": "simple_363", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'restaurant_search_find_closest' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_363", "question": [[{"role": "user", "content": "Find the closest sushi restaurant with a patio in Boston."}]], "function": [{"name": "restaurant_search.find_closest", "description": "Locate the closest sushi restaurant based on certain criteria, such as the presence of a patio.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city, for instance Boston, MA"}, "cuisine": {"type": "string", "description": "Type of food like Sushi."}, "amenities": {"type": "array", "items": {"type": "string", "enum": ["Patio", "Wi-Fi", "Happy Hour", "Wheelchair Accessible"]}, "description": "Preferred amenities in the restaurant. Default 'Wi-Fi'."}}, "required": ["location", "cuisine"]}}]}, "model_result_raw": "\n\n[restaurant_search.find_closest(location=\"Boston, MA\", cuisine=\"Sushi\", amenities=[\"Patio\"])]", "model_result_decoded": [{"restaurant_search.find_closest": {"location": "Boston, MA", "cuisine": "Sushi", "amenities": ["Patio"]}}], "possible_answer": [{"find_closest": {"location": ["Boston", "Boston, MA"], "cuisine": ["Sushi", "sushi"], "amenities": [["Patio"]]}}]}
{"id": "simple_365", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'cooking_conversion_convert' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_365", "question": [[{"role": "user", "content": "How many ounces in 2 pounds of butter?"}]], "function": [{"name": "cooking_conversion.convert", "description": "Convert cooking measurements from one unit to another.", "parameters": {"type": "dict", "properties": {"quantity": {"type": "integer", "description": "The quantity to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from."}, "to_unit": {"type": "string", "description": "The unit to convert to."}, "item": {"type": "string", "description": "The item to be converted."}}, "required": ["quantity", "from_unit", "to_unit", "item"]}}]}, "model_result_raw": "\n\n[cooking_conversion.convert(quantity=2, from_unit='pounds', to_unit='ounces', item='butter')]", "model_result_decoded": [{"cooking_conversion.convert": {"quantity": 2, "from_unit": "pounds", "to_unit": "ounces", "item": "butter"}}], "possible_answer": [{"cooking_conversion.convert": {"quantity": [2], "from_unit": ["pound", "pounds", "lb", "lbs"], "to_unit": ["ounce", "ounces", "oz"], "item": ["butter"]}}]}
{"id": "simple_366", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'recipe_unit_conversion' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_366", "question": [[{"role": "user", "content": "How many teaspoons are in 2 tablespoons for measurement in my recipe?"}]], "function": [{"name": "recipe.unit_conversion", "description": "Convert a value from one kitchen unit to another for cooking purposes.", "parameters": {"type": "dict", "properties": {"value": {"type": "integer", "description": "The value to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from. Supports 'teaspoon', 'tablespoon', 'cup', etc."}, "to_unit": {"type": "string", "description": "The unit to convert to. Supports 'teaspoon', 'tablespoon', 'cup', etc."}, "precision": {"type": "integer", "description": "The precision to round the output to, in case of a non-integer result. Optional, default is 1."}}, "required": ["value", "from_unit", "to_unit"]}}]}, "model_result_raw": "\n\n[recipe.unit_conversion(value=2, from_unit='tablespoon', to_unit='teaspoon')]", "model_result_decoded": [{"recipe.unit_conversion": {"value": 2, "from_unit": "tablespoon", "to_unit": "teaspoon"}}], "possible_answer": [{"recipe.unit_conversion": {"value": [2], "from_unit": ["tablespoon", "tbsp"], "to_unit": ["teaspoon", "tsp"], "precision": [1, ""]}}]}
{"id": "simple_367", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'recipe_type': 'brownies'. Expected one of ['dessert']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_367", "question": [[{"role": "user", "content": "Find me a vegan recipe for brownies which prep time is under 30 minutes."}]], "function": [{"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}]}, "model_result_raw": "\n\n[find_recipe(dietary_restrictions=\"vegan\", recipe_type=\"brownies\", time=30)]", "model_result_decoded": [{"find_recipe": {"dietary_restrictions": "vegan", "recipe_type": "brownies", "time": 30}}], "possible_answer": [{"find_recipe": {"dietary_restrictions": ["vegan"], "recipe_type": ["dessert"], "time": [30]}}]}
{"id": "simple_369", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'grocery_store_find_nearby' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_369", "question": [[{"role": "user", "content": "Find a grocery store near me with organic fruits and vegetables in Houston."}]], "function": [{"name": "grocery_store.find_nearby", "description": "Locate nearby grocery stores based on specific criteria like organic fruits and vegetables.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Houston, TX"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["Organic", "Vegetables", "Fruits", "Dairy", "Seafood", "Bakery"]}, "description": "Categories of items to be found in the grocery store. Default is all if not specified."}}, "required": ["location"]}}]}, "model_result_raw": "\n\n[grocery_store.find_nearby(location=\"Houston\", categories=[\"Organic\", \"Fruits\", \"Vegetables\"])]", "model_result_decoded": [{"grocery_store.find_nearby": {"location": "Houston", "categories": ["Organic", "Fruits", "Vegetables"]}}], "possible_answer": [{"grocery_store.find_nearby": {"location": ["Houston", "Houston, TX"], "categories": [["Organic", "Vegetables", "Fruits"], ["Organic", "Fruits", "Vegetables"], ["Vegetables", "Fruits", "Organic"], ["Fruits", "Vegetables", "Organic"], ["Fruits", "Organic", "Vegetables"], ["Vegetables", "Organic", "Fruits"]]}}]}
{"id": "simple_370", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'safeway_order' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": "\n\n[safeway.order(location=\"Palo Alto, CA\", items=[\"olive oil\", \"five pound bag of rice\"], quantity=[3, 1])]", "model_result_decoded": [{"safeway.order": {"location": "Palo Alto, CA", "items": ["olive oil", "five pound bag of rice"], "quantity": [3, 1]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_371", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'whole_foods_check_price' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_371", "question": [[{"role": "user", "content": "Check the price of tomatoes and lettuce at the Whole Foods in Los Angeles."}]], "function": [{"name": "whole_foods.check_price", "description": "Check the price of items at a specific Whole Foods location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Location of the Whole Foods store."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items for which the price needs to be checked."}}, "required": ["location", "items"]}}]}, "model_result_raw": "\n\n[whole_foods.check_price(location=\"Los Angeles\", items=[\"tomatoes\", \"lettuce\"])]", "model_result_decoded": [{"whole_foods.check_price": {"location": "Los Angeles", "items": ["tomatoes", "lettuce"]}}], "possible_answer": [{"whole_foods.check_price": {"location": ["Los Angeles", "LA"], "items": [["tomatoes", "lettuce"]]}}]}
{"id": "simple_372", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'whole_foods_find_top_brands' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_372", "question": [[{"role": "user", "content": "Find the top five organic bananas brands on the basis of rating from Whole Foods store."}]], "function": [{"name": "whole_foods.find_top_brands", "description": "Get top brands based on a specific product from Whole Foods", "parameters": {"type": "dict", "properties": {"product": {"type": "string", "description": "The product for which the top brands should be fetched."}, "number": {"type": "integer", "description": "Number of top brands to be fetched. Default is 5"}, "organic": {"type": "boolean", "description": "If the product should be organic. Default is false"}}, "required": ["product"]}}]}, "model_result_raw": "\n\n[whole_foods.find_top_brands(product=\"bananas\", number=5, organic=True)]", "model_result_decoded": [{"whole_foods.find_top_brands": {"product": "bananas", "number": 5, "organic": true}}], "possible_answer": [{"whole_foods.find_top_brands": {"product": ["bananas"], "number": [5, ""], "organic": [true]}}]}
{"id": "simple_373", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'walmart_purchase' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_373", "question": [[{"role": "user", "content": "I want to buy apples, rice, and 12 pack of bottled water from a Walmart near San Jose. Show me the product information and stock availability."}]], "function": [{"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is not use it if not specified."}}, "required": ["loc", "product_list"]}}]}, "model_result_raw": "\n\n[walmart.purchase(loc=\"San Jose\", product_list=[\"apples\", \"rice\", \"bottled water\"], pack_size=[1, 1, 12])]", "model_result_decoded": [{"walmart.purchase": {"loc": "San Jose", "product_list": ["apples", "rice", "bottled water"], "pack_size": [1, 1, 12]}}], "possible_answer": [{"walmart.purchase": {"loc": ["San Jose", "San Jose, CA"], "product_list": [["apples", "rice", "bottled water"], ["apples", "rice", "water"]], "pack_size": [[1, 1, 12]]}}]}
{"id": "simple_374", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'grocery_info_nutritional_info' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_374", "question": [[{"role": "user", "content": "Check the amount of protein, calories and carbs in an avocado from Walmart."}]], "function": [{"name": "grocery_info.nutritional_info", "description": "Retrieve nutritional information for a given food item from a particular store", "parameters": {"type": "dict", "properties": {"store": {"type": "string", "description": "The store where the item is available"}, "food": {"type": "string", "description": "Food item for which information is needed."}, "information": {"type": "array", "items": {"type": "string", "enum": ["Protein", "Calories", "Carbohydrates", "Fat", "Fiber"]}, "description": "Nutritional details required."}}, "required": ["store", "food", "information"]}}]}, "model_result_raw": "\n\n[grocery_info.nutritional_info(store=\"Walmart\", food=\"avocado\", information=[\"Protein\", \"Calories\", \"Carbohydrates\"])]", "model_result_decoded": [{"grocery_info.nutritional_info": {"store": "Walmart", "food": "avocado", "information": ["Protein", "Calories", "Carbohydrates"]}}], "possible_answer": [{"grocery_info.nutritional_info": {"store": ["Walmart"], "food": ["avocado", "Avocado"], "information": [["Protein", "Calories", "Carbohydrates"]]}}]}
{"id": "simple_375", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'walmart_check_price' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_375", "question": [[{"role": "user", "content": "Check the total price for three pumpkins and two dozen eggs at Walmart."}]], "function": [{"name": "walmart.check_price", "description": "Calculate total price for given items and their quantities at Walmart.", "parameters": {"type": "dict", "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "List of items to be priced."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item corresponding to the items list."}, "store_location": {"type": "string", "description": "The store location for specific pricing (optional). Default to all if not specified."}}, "required": ["items", "quantities"]}}]}, "model_result_raw": "\n\n[walmart.check_price(items=[\"pumpkins\", \"eggs\"], quantities=[3, 24])]", "model_result_decoded": [{"walmart.check_price": {"items": ["pumpkins", "eggs"], "quantities": [3, 24]}}], "possible_answer": [{"walmart.check_price": {"items": [["pumpkins", "eggs"], ["pumpkin", "dozen eggs"]], "quantities": [[3, 24], [3, 2]], "store_location": [""]}}]}
{"id": "simple_378", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'timezone_convert' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_378", "question": [[{"role": "user", "content": "Convert time 3pm from New York time zone to London time zone."}]], "function": [{"name": "timezone.convert", "description": "Convert time from one time zone to another.", "parameters": {"type": "dict", "properties": {"time": {"type": "string", "description": "The local time you want to convert, e.g. 3pm"}, "from_timezone": {"type": "string", "description": "The time zone you want to convert from."}, "to_timezone": {"type": "string", "description": "The time zone you want to convert to."}}, "required": ["time", "from_timezone", "to_timezone"]}}]}, "model_result_raw": "\n\n[timezone.convert(time=\"3pm\", from_timezone=\"New York\", to_timezone=\"London\")]", "model_result_decoded": [{"timezone.convert": {"time": "3pm", "from_timezone": "New York", "to_timezone": "London"}}], "possible_answer": [{"timezone.convert": {"time": ["3pm"], "from_timezone": ["America/New_York", "New York", "NYC", "New York City"], "to_timezone": ["Europe/London", "London"]}}]}
{"id": "simple_381", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'hilton_hotel_check_availability' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_381", "question": [[{"role": "user", "content": "Check if any Hilton Hotel is available for two adults in Paris from 2023 April 4th to April 8th?"}]], "function": [{"name": "hilton_hotel.check_availability", "description": "Check hotel availability for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you want to check hotel availability."}, "check_in_date": {"type": "string", "description": "The check-in date in the format YYYY-MM-DD."}, "check_out_date": {"type": "string", "description": "The check-out date in the format YYYY-MM-DD."}, "no_of_adults": {"type": "integer", "description": "The number of adults for the hotel booking."}, "hotel_chain": {"type": "string", "description": "The hotel chain where you want to book the hotel.", "default": "Hilton"}}, "required": ["location", "check_in_date", "check_out_date", "no_of_adults"]}}]}, "model_result_raw": "\n\n[hilton_hotel.check_availability(location=\"Paris\", check_in_date=\"2023-04-04\", check_out_date=\"2023-04-08\", no_of_adults=2)]", "model_result_decoded": [{"hilton_hotel.check_availability": {"location": "Paris", "check_in_date": "2023-04-04", "check_out_date": "2023-04-08", "no_of_adults": 2}}], "possible_answer": [{"hilton_hotel.check_availability": {"location": ["Paris"], "check_in_date": ["2023-04-04"], "check_out_date": ["2023-04-08"], "no_of_adults": [2], "hotel_chain": ["Hilton", ""]}}]}
{"id": "simple_383", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'hotel_name': 'The Plaza hotel'. Expected one of ['The Plaza']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_383", "question": [[{"role": "user", "content": "I would like to book a single room for two nights at The Plaza hotel."}]], "function": [{"name": "book_room", "description": "Book a room in a specified hotel.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "room_type": {"type": "string", "description": "The type of room to be booked."}, "num_nights": {"type": "integer", "description": "The number of nights to book the room for."}}, "required": ["hotel_name", "room_type", "num_nights"]}}]}, "model_result_raw": "\n\n[book_room(hotel_name=\"The Plaza hotel\", room_type=\"single\", num_nights=2)]", "model_result_decoded": [{"book_room": {"hotel_name": "The Plaza hotel", "room_type": "single", "num_nights": 2}}], "possible_answer": [{"book_room": {"hotel_name": ["The Plaza"], "room_type": ["Single", "single"], "num_nights": [2]}}]}
{"id": "simple_384", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'hotel_booking_book' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_384", "question": [[{"role": "user", "content": "Book a hotel room for two adults and one child in Paris, France from July 10, 2022 to July 20, 2022."}]], "function": [{"name": "hotel_booking.book", "description": "Book a hotel room given the city, date, and the number of adults and children.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city where the hotel is located."}, "from_date": {"type": "string", "description": "The start date of the booking. The format is MM-DD-YYYY."}, "to_date": {"type": "string", "description": "The end date of the booking. The format is MM-DD-YYYY."}, "adults": {"type": "integer", "description": "The number of adults for the booking."}, "children": {"type": "integer", "description": "The number of children for the booking."}, "room_type": {"type": "string", "description": "The type of the room, default is 'Standard'. Options are 'Standard', 'Deluxe', 'Suite'.", "default": "Standard"}}, "required": ["city", "from_date", "to_date", "adults", "children"]}}]}, "model_result_raw": "\n\n[hotel_booking.book(city=\"Paris\", from_date=\"07-10-2022\", to_date=\"07-20-2022\", adults=2, children=1)]", "model_result_decoded": [{"hotel_booking.book": {"city": "Paris", "from_date": "07-10-2022", "to_date": "07-20-2022", "adults": 2, "children": 1}}], "possible_answer": [{"hotel_booking.book": {"city": ["Paris", "Paris, France"], "from_date": ["07-10-2022", "2022-07-10", "10/07/2022", "Jul.10,2022"], "to_date": ["07-20-2022", "2022-07-20", "20/07/2022", "Jul.20,2022"], "adults": [2], "children": [1], "room_type": ["Standard", ""]}}]}
{"id": "simple_385", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'hotel_bookings_book_room' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_385", "question": [[{"role": "user", "content": "Book a hotel room with king size bed in Los Angeles for 2 nights starting from 15th October,2023."}]], "function": [{"name": "hotel_bookings.book_room", "description": "Book a hotel room based on specific criteria like location, room type, and check-in and check-out dates.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where you want to book the hotel, e.g. Los Angeles, CA"}, "room_type": {"type": "string", "description": "Preferred type of room in the hotel, e.g. king size, queen size, deluxe, suite etc."}, "check_in_date": {"type": "string", "description": "Check-in date for the hotel. Format - DD-MM-YYYY."}, "no_of_nights": {"type": "integer", "description": "Number of nights for the stay."}, "no_of_rooms": {"type": "integer", "description": "Number of rooms to book. Default is 1.", "default": 1}}, "required": ["location", "room_type", "check_in_date", "no_of_nights"]}}]}, "model_result_raw": "\n\n[hotel_bookings.book_room(location=\"Los Angeles, CA\", room_type=\"king size\", check_in_date=\"15-10-2023\", no_of_nights=2)]", "model_result_decoded": [{"hotel_bookings.book_room": {"location": "Los Angeles, CA", "room_type": "king size", "check_in_date": "15-10-2023", "no_of_nights": 2}}], "possible_answer": [{"hotel_bookings.book_room": {"location": ["Los Angeles", "Los Angeles, CA", "LA"], "room_type": ["King Size", "king size"], "check_in_date": ["15-10-2023", "15th October", "2023-10-15", "10/15/2023", "Oct.15,2023"], "no_of_nights": [2], "no_of_rooms": ["", 1]}}]}
{"id": "simple_388", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'currency_exchange_convert' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_388", "question": [[{"role": "user", "content": "How many Canadian dollars can I get for 500 US dollars?"}]], "function": [{"name": "currency_exchange.convert", "description": "Convert an amount from a base currency to a target currency based on the current exchange rate.", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The currency to convert from."}, "target_currency": {"type": "string", "description": "The currency to convert to."}, "amount": {"type": "integer", "description": "The amount in base currency to convert"}}, "required": ["base_currency", "target_currency", "amount"]}}]}, "model_result_raw": "\n\n[currency_exchange.convert(base_currency=\"USD\", target_currency=\"CAD\", amount=500)]", "model_result_decoded": [{"currency_exchange.convert": {"base_currency": "USD", "target_currency": "CAD", "amount": 500}}], "possible_answer": [{"currency_exchange.convert": {"base_currency": ["USD"], "target_currency": ["CAD"], "amount": [500]}}]}
{"id": "simple_390", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'currency_conversion_convert' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_390", "question": [[{"role": "user", "content": "Convert 150 Euros to Canadian dollars."}]], "function": [{"name": "currency_conversion.convert", "description": "Convert a value from one currency to another.", "parameters": {"type": "dict", "properties": {"amount": {"type": "integer", "description": "The amount to be converted."}, "from_currency": {"type": "string", "description": "The currency to convert from."}, "to_currency": {"type": "string", "description": "The currency to convert to."}}, "required": ["amount", "from_currency", "to_currency"]}}]}, "model_result_raw": "\n\n[currency_conversion.convert(amount=150, from_currency='EUR', to_currency='CAD')]", "model_result_decoded": [{"currency_conversion.convert": {"amount": 150, "from_currency": "EUR", "to_currency": "CAD"}}], "possible_answer": [{"currency_conversion.convert": {"amount": [150], "from_currency": ["EUR", "Euros"], "to_currency": ["CAD", "Canadian dollars"]}}]}
{"id": "simple_394", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'maps_get_distance_duration' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_394", "question": [[{"role": "user", "content": "Get me the travel distance and duration from the Eiffel Tower to the Louvre Museum"}]], "function": [{"name": "maps.get_distance_duration", "description": "Retrieve the travel distance and estimated travel time from one location to another via car", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "Starting point of the journey"}, "end_location": {"type": "string", "description": "Ending point of the journey"}, "traffic": {"type": "boolean", "description": "If true, considers current traffic. Default is false."}}, "required": ["start_location", "end_location"]}}]}, "model_result_raw": "\n\n[maps.get_distance_duration(start_location=\"Eiffel Tower\", end_location=\"Louvre Museum\")]", "model_result_decoded": [{"maps.get_distance_duration": {"start_location": "Eiffel Tower", "end_location": "Louvre Museum"}}], "possible_answer": [{"maps.get_distance_duration": {"start_location": ["Eiffel Tower"], "end_location": ["Louvre Museum"], "traffic": ["", false]}}]}
{"id": "simple_395", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'parking_lot_find_nearest' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_395", "question": [[{"role": "user", "content": "Find the nearest parking lot within 2 miles of Central Park in New York."}]], "function": [{"name": "parking_lot.find_nearest", "description": "Locate the nearest parking lot based on a specific location and radius.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The reference location e.g. Central Park, NY"}, "radius": {"type": "integer", "description": "The maximum distance from the location in miles. Default is 5 miles"}, "type": {"type": "string", "description": "The type of parking lot. Default is 'public'."}}, "required": ["location", "radius"]}}]}, "model_result_raw": "\n\n[parking_lot.find_nearest(location=\"Central Park, NY\", radius=2)]", "model_result_decoded": [{"parking_lot.find_nearest": {"location": "Central Park, NY", "radius": 2}}], "possible_answer": [{"parking_lot.find_nearest": {"location": ["Central Park, NY"], "radius": [2], "type": ["public", ""]}}]}
{"id": "simple_396", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'hospital_locate' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_396", "question": [[{"role": "user", "content": "Find a hospital within 5 km radius around Denver, Colorado with pediatrics department."}]], "function": [{"name": "hospital.locate", "description": "Locate nearby hospitals based on location and radius. Options to include specific departments are available.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Denver, CO"}, "radius": {"type": "integer", "description": "The radius within which you want to find the hospital in kms."}, "department": {"type": "string", "description": "Specific department within the hospital. Default is 'General Medicine'.", "enum": ["General Medicine", "Emergency", "Pediatrics", "Cardiology", "Orthopedics"]}}, "required": ["location", "radius"]}}]}, "model_result_raw": "\n\n[hospital.locate(location=\"Denver, CO\", radius=5, department=\"Pediatrics\")]", "model_result_decoded": [{"hospital.locate": {"location": "Denver, CO", "radius": 5, "department": "Pediatrics"}}], "possible_answer": [{"hospital.locate": {"location": ["Denver, Colorado", "Denver, CO"], "radius": [5], "department": ["Pediatrics"]}}]}
{"id": "simple_397", "model_name": "Qwen_Qwen3-30B-A3B", "test_category": "simple", "valid": false, "error": ["Function name 'distance_calculator_calculate' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "simple_397", "question": [[{"role": "user", "content": "Find the distance between New York and Boston, accounting for terrain."}]], "function": [{"name": "distance_calculator.calculate", "description": "Calculate the distance between two locations, considering terrain.", "parameters": {"type": "dict", "properties": {"origin": {"type": "string", "description": "Starting location of the distance measurement."}, "destination": {"type": "string", "description": "Destination location of the distance measurement."}, "consider_terrain": {"type": "boolean", "description": "Whether to account for terrain in distance calculation, defaults to false."}}, "required": ["origin", "destination"]}}]}, "model_result_raw": "\n\n[distance_calculator.calculate(origin=\"New York\", destination=\"Boston\", consider_terrain=True)]", "model_result_decoded": [{"distance_calculator.calculate": {"origin": "New York", "destination": "Boston", "consider_terrain": true}}], "possible_answer": [{"distance_calculator.calculate": {"origin": ["New York", "New York City", "New York City, NY", "New York, NY", "NYC"], "destination": ["Boston"], "consider_terrain": [true]}}]}