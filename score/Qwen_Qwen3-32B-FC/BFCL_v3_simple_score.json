{"accuracy": 0.945, "correct_count": 378, "total_count": 400}
{"id": "simple_13", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": [{"calculate_area_under_curve": "{\"function\": \"lambda x: x**2\", \"interval\": [1, 3]}"}], "model_result_decoded": [{"calculate_area_under_curve": {"function": "lambda x: x**2", "interval": [1, 3]}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_50", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'substance': 'water'. Expected one of ['ice']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_50", "question": [[{"role": "user", "content": "What is the change in entropy in Joules per Kelvin of a 1kg ice block at 0\u00b0C if it is heated to 100\u00b0C under 1 atmosphere of pressure?"}]], "function": [{"name": "entropy_change.calculate", "description": "Calculate the change in entropy for a mass of a specific substance under set initial and final conditions.", "parameters": {"type": "dict", "properties": {"substance": {"type": "string", "description": "The substance for which the change in entropy is calculated."}, "mass": {"type": "integer", "description": "The mass of the substance in kg."}, "initial_temperature": {"type": "integer", "description": "The initial temperature of the substance in degree Celsius."}, "final_temperature": {"type": "integer", "description": "The final temperature of the substance in degree Celsius."}, "pressure": {"type": "integer", "default": 1, "description": "The pressure the substance is under in atmospheres."}}, "required": ["substance", "mass", "initial_temperature", "final_temperature"]}}]}, "model_result_raw": [{"entropy_change_calculate": "{\"substance\": \"water\", \"mass\": 1, \"initial_temperature\": 0, \"final_temperature\": 100, \"pressure\": 1}"}], "model_result_decoded": [{"entropy_change_calculate": {"substance": "water", "mass": 1, "initial_temperature": 0, "final_temperature": 100, "pressure": 1}}], "possible_answer": [{"entropy_change.calculate": {"substance": ["ice"], "mass": [1], "initial_temperature": [0], "final_temperature": [100], "pressure": ["", 1]}}]}
{"id": "simple_51", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'isothermal': False. Expected one of ['', True]."], "error_type": "value_error:others", "prompt": {"id": "simple_51", "question": [[{"role": "user", "content": "Calculate the entropy change for a certain process given an initial temperature of 300K, a final temperature of 400K, and a heat capacity of 5J/K."}]], "function": [{"name": "calculate_entropy_change", "description": "Calculate the entropy change for an isothermal and reversible process.", "parameters": {"type": "dict", "properties": {"initial_temp": {"type": "integer", "description": "The initial temperature in Kelvin."}, "final_temp": {"type": "integer", "description": "The final temperature in Kelvin."}, "heat_capacity": {"type": "integer", "description": "The heat capacity in J/K."}, "isothermal": {"type": "boolean", "description": "Whether the process is isothermal. Default is True."}}, "required": ["initial_temp", "final_temp", "heat_capacity"]}}]}, "model_result_raw": [{"calculate_entropy_change": "{\"initial_temp\": 300, \"final_temp\": 400, \"heat_capacity\": 5, \"isothermal\": false}"}], "model_result_decoded": [{"calculate_entropy_change": {"initial_temp": 300, "final_temp": 400, "heat_capacity": 5, "isothermal": false}}], "possible_answer": [{"calculate_entropy_change": {"initial_temp": [300], "final_temp": [400], "heat_capacity": [5], "isothermal": ["", true]}}]}
{"id": "simple_55", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'cell_type': 'human cell'. Expected one of ['human']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_55", "question": [[{"role": "user", "content": "Find me detailed information about the structure of human cell"}]], "function": [{"name": "biology.get_cell_info", "description": "Retrieve information about the structure and functioning of a specified type of cell", "parameters": {"type": "dict", "properties": {"cell_type": {"type": "string", "description": "Type of cell you want information about"}, "detailed": {"type": "boolean", "description": "Indicate if you want a detailed description of the cell", "default": "false"}}, "required": ["cell_type"]}}]}, "model_result_raw": [{"biology_get_cell_info": "{\"cell_type\": \"human cell\", \"detailed\": true}"}], "model_result_decoded": [{"biology_get_cell_info": {"cell_type": "human cell", "detailed": true}}], "possible_answer": [{"biology.get_cell_info": {"cell_type": ["human"], "detailed": [true]}}]}
{"id": "simple_82", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers'. Expected outer type array with inner type <class 'float'>. Parameter value: [12, 15, 18, 20, 21, 26, 30]."], "error_type": "type_error:nested", "prompt": {"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}, "model_result_raw": [{"calculate_average": "{\"numbers\": [12, 15, 18, 20, 21, 26, 30]}"}], "model_result_decoded": [{"calculate_average": {"numbers": [12, 15, 18, 20, 21, 26, 30]}}], "possible_answer": [{"calculate_average": {"numbers": [[12.0, 15.0, 18.0, 20.0, 21.0, 26.0, 30.0]]}}]}
{"id": "simple_87", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'list'. Expected outer type array with inner type <class 'float'>. Parameter value: [5, 3, 4, 1, 2]."], "error_type": "type_error:nested", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": [{"array_sort": "{\"list\": [5, 3, 4, 1, 2], \"order\": \"ascending\"}"}], "model_result_decoded": [{"array_sort": {"list": [5, 3, 4, 1, 2], "order": "ascending"}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_91", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'condition': 'operates on Sundays'. Expected one of ['open on Sundays', 'opens on Sundays']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_91", "question": [[{"role": "user", "content": "Get the highest rated sushi restaurant in Boston, that opens on Sundays."}]], "function": [{"name": "get_restaurant", "description": "Retrieve highest rated restaurant given cuisine, location, and a condition.", "parameters": {"type": "dict", "properties": {"cuisine": {"type": "string", "description": "Cuisine of the restaurant."}, "location": {"type": "string", "description": "City where restaurant is located."}, "condition": {"type": "string", "description": "Condition to be met by the restaurant (e.g., operating days, amenities, etc.)"}}, "required": ["cuisine", "location", "condition"]}}]}, "model_result_raw": [{"get_restaurant": "{\"cuisine\": \"sushi\", \"location\": \"Boston\", \"condition\": \"operates on Sundays\"}"}], "model_result_decoded": [{"get_restaurant": {"cuisine": "sushi", "location": "Boston", "condition": "operates on Sundays"}}], "possible_answer": [{"get_restaurant": {"cuisine": ["sushi"], "location": ["Boston"], "condition": ["open on Sundays", "opens on Sundays"]}}]}
{"id": "simple_103", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'polynomial'. Expected outer type array with inner type <class 'float'>. Parameter value: [3, 2, -4]."], "error_type": "type_error:nested", "prompt": {"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}, "model_result_raw": [{"mathematics_calculate_area_under_curve": "{\"polynomial\": [3, 2, -4], \"limits\": [-1, 2]}"}], "model_result_decoded": [{"mathematics_calculate_area_under_curve": {"polynomial": [3, 2, -4], "limits": [-1, 2]}}], "possible_answer": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3.0, 2.0, -4.0]], "limits": [[-1.0, 2.0]]}}]}
{"id": "simple_137", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'annual_growth_rate': 6.0. Expected one of [0.06]."], "error_type": "value_error:others", "prompt": {"id": "simple_137", "question": [[{"role": "user", "content": "Calculate the projected return on a $5000 investment in ABC company's stock, if the expected annual growth rate is 6% and the holding period is 5 years."}]], "function": [{"name": "calculate_stock_return", "description": "Calculate the projected return of a stock investment given the investment amount, the annual growth rate and holding period in years.", "parameters": {"type": "dict", "properties": {"investment_amount": {"type": "integer", "description": "The amount of money to invest."}, "annual_growth_rate": {"type": "float", "description": "The expected annual growth rate of the stock."}, "holding_period": {"type": "integer", "description": "The number of years you intend to hold the stock."}, "dividends": {"type": "boolean", "description": "Optional. True if the calculation should take into account potential dividends. Default is false."}}, "required": ["investment_amount", "annual_growth_rate", "holding_period"]}}]}, "model_result_raw": [{"calculate_stock_return": "{\"investment_amount\": 5000, \"annual_growth_rate\": 6.0, \"holding_period\": 5}"}], "model_result_decoded": [{"calculate_stock_return": {"investment_amount": 5000, "annual_growth_rate": 6.0, "holding_period": 5}}], "possible_answer": [{"calculate_stock_return": {"investment_amount": [5000], "annual_growth_rate": [0.06], "holding_period": [5], "dividends": ["", false]}}]}
{"id": "simple_166", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'fee': 399. Expected one of [400]."], "error_type": "value_error:others", "prompt": {"id": "simple_166", "question": [[{"role": "user", "content": "Find a lawyer specializing in divorce cases and charge fee less than 400 dollars per hour in Chicago."}]], "function": [{"name": "lawyer.find_nearby", "description": "Locate nearby lawyers based on specific criteria like specialty, fee per hour and city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city and state, e.g. Chicago, IL."}, "specialty": {"type": "array", "items": {"type": "string", "enum": ["Civil", "Divorce", "Immigration", "Business", "Criminal"]}, "description": "Specialization of the lawyer."}, "fee": {"type": "integer", "description": "Hourly fee charged by lawyer"}}, "required": ["city", "specialty", "fee"]}}]}, "model_result_raw": [{"lawyer_find_nearby": "{\"city\": \"Chicago, IL\", \"specialty\": [\"Divorce\"], \"fee\": 399}"}], "model_result_decoded": [{"lawyer_find_nearby": {"city": "Chicago, IL", "specialty": ["Divorce"], "fee": 399}}], "possible_answer": [{"lawyer.find_nearby": {"city": ["Chicago, IL.", "Chicago, IL"], "specialty": [["Divorce"]], "fee": [400]}}]}
{"id": "simple_172", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_172", "question": [[{"role": "user", "content": "How to obtain the detailed case information of the 'R vs Adams' legal case?"}]], "function": [{"name": "legal_case.fetch", "description": "Fetch detailed legal case information from database.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "string", "description": "The ID of the legal case."}, "details": {"type": "boolean", "description": "True if need the detail info. "}}, "required": ["case_id", "details"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"legal_case.fetch": {"case_id": ["R vs Adams"], "details": [true]}}]}
{"id": "simple_197", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'time': '12:00 PM'. Expected one of ['12pm', '12:00']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_197", "question": [[{"role": "user", "content": "Find the air quality index in San Diego at 12pm."}]], "function": [{"name": "get_air_quality_index", "description": "Retrieve the air quality index at a specified location and time.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location to get the air quality index for."}, "time": {"type": "string", "description": "The specific time to check the air quality. Default is the current time."}}, "required": ["location", "time"]}}]}, "model_result_raw": [{"get_air_quality_index": "{\"location\": \"San Diego\", \"time\": \"12:00 PM\"}"}], "model_result_decoded": [{"get_air_quality_index": {"location": "San Diego", "time": "12:00 PM"}}], "possible_answer": [{"get_air_quality_index": {"location": ["San Diego"], "time": ["12pm", "12:00"]}}]}
{"id": "simple_202", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'energy_type': 'solar'. Expected one of ['renewable']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_202", "question": [[{"role": "user", "content": "How many greenhouse gas emissions would I save if I switched to renewable energy sources for 3 months in California?"}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'Texas'."}}, "required": ["energy_type", "usage_duration"]}}]}, "model_result_raw": [{"calculate_emission_savings": "{\"energy_type\": \"solar\", \"usage_duration\": 3, \"region\": \"California\"}"}], "model_result_decoded": [{"calculate_emission_savings": {"energy_type": "solar", "usage_duration": 3, "region": "California"}}], "possible_answer": [{"calculate_emission_savings": {"energy_type": ["renewable"], "usage_duration": [3], "region": ["California", "CA"]}}]}
{"id": "simple_224", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'sub_topics': ['behavior', 'group dynamics']. Expected one of [['behaviour', 'group dynamics']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_224", "question": [[{"role": "user", "content": "Find the most followed person on twitter who tweets about psychology related to behaviour and group dynamics."}]], "function": [{"name": "social_media_analytics.most_followed", "description": "Find the most followed Twitter user related to certain topics.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The main topic of interest."}, "sub_topics": {"type": "array", "items": {"type": "string"}, "description": "Sub-topics related to main topic. Default is empty."}, "region": {"type": "string", "description": "Region of interest for twitter search. Default is 'all'."}}, "required": ["topic"]}}]}, "model_result_raw": [{"social_media_analytics_most_followed": "{\"topic\": \"psychology\", \"sub_topics\": [\"behavior\", \"group dynamics\"]}"}], "model_result_decoded": [{"social_media_analytics_most_followed": {"topic": "psychology", "sub_topics": ["behavior", "group dynamics"]}}], "possible_answer": [{"social_media_analytics.most_followed": {"topic": ["psychology"], "sub_topics": [["behaviour", "group dynamics"]], "region": ["", "all"]}}]}
{"id": "simple_234", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_234", "question": [[{"role": "user", "content": "Find important Wars in European history during the 19th century."}]], "function": [{"name": "history_eu.fetch_events", "description": "Fetches significant historical events within a specific time period in European history.", "parameters": {"type": "dict", "properties": {"century": {"type": "integer", "description": "The century you are interested in."}, "region": {"type": "string", "description": "The region of Europe you are interested in.", "enum": ["Northern", "Southern", "Eastern", "Western"]}, "category": {"type": "string", "description": "Category of the historical events. Default is 'Culture'.", "enum": ["Wars", "Culture", "Politics", "Scientific", "Others"]}}, "required": ["century", "region"]}}]}, "model_result_raw": [{"history_eu_fetch_events": "{\"century\": 19, \"region\": \"Northern\", \"category\": \"Wars\"}"}, {"history_eu_fetch_events": "{\"century\": 19, \"region\": \"Southern\", \"category\": \"Wars\"}"}, {"history_eu_fetch_events": "{\"century\": 19, \"region\": \"Eastern\", \"category\": \"Wars\"}"}, {"history_eu_fetch_events": "{\"century\": 19, \"region\": \"Western\", \"category\": \"Wars\"}"}], "model_result_decoded": [{"history_eu_fetch_events": {"century": 19, "region": "Northern", "category": "Wars"}}, {"history_eu_fetch_events": {"century": 19, "region": "Southern", "category": "Wars"}}, {"history_eu_fetch_events": {"century": 19, "region": "Eastern", "category": "Wars"}}, {"history_eu_fetch_events": {"century": 19, "region": "Western", "category": "Wars"}}], "possible_answer": [{"history_eu.fetch_events": {"century": [19], "region": ["Northern", "Southern", "Eastern", "Western"], "category": ["Wars"]}}]}
{"id": "simple_244", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'work_title': 'Philosophi\u00e6 Naturalis Principia Mathematica'. Expected one of ['Law of Universal Gravitation', 'Universal Law of Gravitation', 'The law of universal gravitation']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_244", "question": [[{"role": "user", "content": "What year was the law of universal gravitation published by Isaac Newton?"}]], "function": [{"name": "publication_year.find", "description": "Fetches the year a particular scientific work was published.", "parameters": {"type": "dict", "properties": {"author": {"type": "string", "description": "Name of the author of the work."}, "work_title": {"type": "string", "description": "Title of the scientific work."}, "location": {"type": "string", "description": "Place of the publication, if known. Default to 'all'."}}, "required": ["author", "work_title"]}}]}, "model_result_raw": [{"publication_year_find": "{\"author\": \"Isaac Newton\", \"work_title\": \"Philosophi\u00e6 Naturalis Principia Mathematica\"}"}], "model_result_decoded": [{"publication_year_find": {"author": "Isaac Newton", "work_title": "Philosophi\u00e6 Naturalis Principia Mathematica"}}], "possible_answer": [{"publication_year.find": {"author": ["Isaac Newton"], "work_title": ["Law of Universal Gravitation", "Universal Law of Gravitation", "The law of universal gravitation"], "location": ["", "all"]}}]}
{"id": "simple_249", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'include_people': True. Expected one of [False, '']."], "error_type": "value_error:others", "prompt": {"id": "simple_249", "question": [[{"role": "user", "content": "Tell me more about Christianity and its history till the 14th century"}]], "function": [{"name": "religion.history_info", "description": "Provides comprehensive historical details about a specified religion till a specified century.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "The name of the religion for which historical details are needed."}, "till_century": {"type": "integer", "description": "The century till which historical details are needed."}, "include_people": {"type": "boolean", "description": "To include influential people related to the religion during that time period, default is False"}}, "required": ["religion", "till_century"]}}]}, "model_result_raw": [{"religion_history_info": "{\"religion\": \"Christianity\", \"till_century\": 14, \"include_people\": true}"}], "model_result_decoded": [{"religion_history_info": {"religion": "Christianity", "till_century": 14, "include_people": true}}], "possible_answer": [{"religion.history_info": {"religion": ["Christianity"], "till_century": [14], "include_people": [false, ""]}}]}
{"id": "simple_284", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'date': 'November 2023'. Expected one of ['next month', '2023-06-01', '06/01/2023', 'Jun.1,2023', 'June 2023']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_284", "question": [[{"role": "user", "content": "Get information about the pop concerts in New York for next month."}]], "function": [{"name": "concert_info.get", "description": "Retrieve information about concerts based on specific genre, location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the concert will take place."}, "date": {"type": "string", "description": "Time frame to get the concert for."}, "genre": {"type": "string", "description": "Genre of the concert.", "enum": ["Pop", "Rock", "Country", "Classical", "Electronic", "Hip-Hop"]}}, "required": ["location", "date", "genre"]}}]}, "model_result_raw": [{"concert_info_get": "{\"location\": \"New York\", \"date\": \"November 2023\", \"genre\": \"Pop\"}"}], "model_result_decoded": [{"concert_info_get": {"location": "New York", "date": "November 2023", "genre": "Pop"}}], "possible_answer": [{"concert_info.get": {"location": ["New York City, NY", "New York"], "date": ["next month", "2023-06-01", "06/01/2023", "Jun.1,2023", "June 2023"], "genre": ["Pop"]}}]}
{"id": "simple_367", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'recipe_type': 'brownies'. Expected one of ['dessert']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_367", "question": [[{"role": "user", "content": "Find me a vegan recipe for brownies which prep time is under 30 minutes."}]], "function": [{"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}]}, "model_result_raw": [{"find_recipe": "{\"dietary_restrictions\": \"vegan\", \"recipe_type\": \"brownies\", \"time\": 30}"}], "model_result_decoded": [{"find_recipe": {"dietary_restrictions": "vegan", "recipe_type": "brownies", "time": 30}}], "possible_answer": [{"find_recipe": {"dietary_restrictions": ["vegan"], "recipe_type": ["dessert"], "time": [30]}}]}
{"id": "simple_370", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'quantity': [3, 5]. Expected one of [[3, 1]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": [{"safeway_order": "{\"location\": \"Palo Alto, CA\", \"items\": [\"olive oil\", \"rice\"], \"quantity\": [3, 5]}"}], "model_result_decoded": [{"safeway_order": {"location": "Palo Alto, CA", "items": ["olive oil", "rice"], "quantity": [3, 5]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_383", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'hotel_name': 'The Plaza Hotel'. Expected one of ['The Plaza']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_383", "question": [[{"role": "user", "content": "I would like to book a single room for two nights at The Plaza hotel."}]], "function": [{"name": "book_room", "description": "Book a room in a specified hotel.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "room_type": {"type": "string", "description": "The type of room to be booked."}, "num_nights": {"type": "integer", "description": "The number of nights to book the room for."}}, "required": ["hotel_name", "room_type", "num_nights"]}}]}, "model_result_raw": [{"book_room": "{\"hotel_name\": \"The Plaza Hotel\", \"room_type\": \"single\", \"num_nights\": 2}"}], "model_result_decoded": [{"book_room": {"hotel_name": "The Plaza Hotel", "room_type": "single", "num_nights": 2}}], "possible_answer": [{"book_room": {"hotel_name": ["The Plaza"], "room_type": ["Single", "single"], "num_nights": [2]}}]}
{"id": "simple_399", "model_name": "Qwen_Qwen3-32B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'rating': 5. Expected one of [4]."], "error_type": "value_error:others", "prompt": {"id": "simple_399", "question": [[{"role": "user", "content": "Find me the best Italian restaurants in New York City with average customer ratings of more than 4 and accepts credit cards."}]], "function": [{"name": "restaurant_search", "description": "Locates top rated restaurants based on specific criteria such as type of cuisine, ratings, and facilities.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York City, NY"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine e.g., Italian, Indian, American, etc."}, "rating": {"type": "integer", "description": "Minimum average customer rating out of 5"}, "accepts_credit_cards": {"type": "boolean", "description": "If the restaurant should accept credit cards."}}, "required": ["location", "cuisine", "rating", "accepts_credit_cards"]}}]}, "model_result_raw": [{"restaurant_search": "{\"location\": \"New York City, NY\", \"cuisine\": \"Italian\", \"rating\": 5, \"accepts_credit_cards\": true}"}], "model_result_decoded": [{"restaurant_search": {"location": "New York City, NY", "cuisine": "Italian", "rating": 5, "accepts_credit_cards": true}}], "possible_answer": [{"restaurant_search": {"location": ["New York City", "New York City, NY", "NYC"], "cuisine": ["Italian"], "rating": [4], "accepts_credit_cards": [true]}}]}