{"accuracy": 0.9175, "correct_count": 367, "total_count": 400}
{"id": "simple_5", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'root_type': 'real'. Expected one of ['all']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_5", "question": [[{"role": "user", "content": "Find all the roots of a quadratic equation given coefficients a = 3, b = -11, and c = -4."}]], "function": [{"name": "solve_quadratic", "description": "Solve a quadratic equation given coefficients a, b, and c. If optional 'root_type' is 'real', the function will only return real roots. If not specified, function may return complex roots.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "The coefficient of the squared term in the quadratic equation."}, "b": {"type": "integer", "description": "The coefficient of the linear term in the quadratic equation."}, "c": {"type": "integer", "description": "The constant term in the quadratic equation."}, "root_type": {"type": "string", "description": "The type of roots to return: 'real' for real roots, 'all' for both real and complex roots. Default value is 'real'."}}, "required": ["a", "b", "c"]}}]}, "model_result_raw": [{"solve_quadratic": "{\"a\": 3, \"b\": -11, \"c\": -4, \"root_type\": \"real\"}"}], "model_result_decoded": [{"solve_quadratic": {"a": 3, "b": -11, "c": -4, "root_type": "real"}}], "possible_answer": [{"solve_quadratic": {"a": [3], "b": [-11], "c": [-4], "root_type": ["all"]}}]}
{"id": "simple_13", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": [{"calculate_area_under_curve": "{\"function\": \"y = x**2\", \"interval\": [1, 3], \"method\": \"trapezoidal\"}"}], "model_result_decoded": [{"calculate_area_under_curve": {"function": "y = x**2", "interval": [1, 3], "method": "trapezoidal"}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_35", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'operating_hours': 11. Expected one of [23]."], "error_type": "value_error:others", "prompt": {"id": "simple_35", "question": [[{"role": "user", "content": "Find an all vegan restaurant in New York that opens until at least 11 PM."}]], "function": [{"name": "vegan_restaurant.find_nearby", "description": "Locate nearby vegan restaurants based on specific criteria like operating hours.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York, NY, you should format it as City, State."}, "operating_hours": {"type": "integer", "description": "Preferred latest closing time of the restaurant. E.g. if 11 is given, then restaurants that close at or after 11 PM will be considered. This is in 24 hour format. Default is 24."}}, "required": ["location"]}}]}, "model_result_raw": [{"vegan_restaurant_find_nearby": "{\"location\": \"New York, NY\", \"operating_hours\": 11}"}], "model_result_decoded": [{"vegan_restaurant_find_nearby": {"location": "New York, NY", "operating_hours": 11}}], "possible_answer": [{"vegan_restaurant.find_nearby": {"location": ["New York, NY"], "operating_hours": [23]}}]}
{"id": "simple_55", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'cell_type': 'human cell'. Expected one of ['human']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_55", "question": [[{"role": "user", "content": "Find me detailed information about the structure of human cell"}]], "function": [{"name": "biology.get_cell_info", "description": "Retrieve information about the structure and functioning of a specified type of cell", "parameters": {"type": "dict", "properties": {"cell_type": {"type": "string", "description": "Type of cell you want information about"}, "detailed": {"type": "boolean", "description": "Indicate if you want a detailed description of the cell", "default": "false"}}, "required": ["cell_type"]}}]}, "model_result_raw": [{"biology_get_cell_info": "{\"cell_type\": \"human cell\", \"detailed\": true}"}], "model_result_decoded": [{"biology_get_cell_info": {"cell_type": "human cell", "detailed": true}}], "possible_answer": [{"biology.get_cell_info": {"cell_type": ["human"], "detailed": [true]}}]}
{"id": "simple_58", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'specific_function': False. Expected one of [True]."], "error_type": "value_error:others", "prompt": {"id": "simple_58", "question": [[{"role": "user", "content": "What is the function of ATP synthase in mitochondria?"}]], "function": [{"name": "cell_biology.function_lookup", "description": "Look up the function of a given molecule in a specified organelle.", "parameters": {"type": "dict", "properties": {"molecule": {"type": "string", "description": "The molecule of interest."}, "organelle": {"type": "string", "description": "The organelle of interest."}, "specific_function": {"type": "boolean", "description": "If set to true, a specific function of the molecule within the organelle will be provided, if such information exists."}}, "required": ["molecule", "organelle", "specific_function"]}}]}, "model_result_raw": [{"cell_biology_function_lookup": "{\"molecule\": \"ATP synthase\", \"organelle\": \"mitochondria\", \"specific_function\": false}"}], "model_result_decoded": [{"cell_biology_function_lookup": {"molecule": "ATP synthase", "organelle": "mitochondria", "specific_function": false}}], "possible_answer": [{"cell_biology.function_lookup": {"molecule": ["ATP synthase"], "organelle": ["mitochondria"], "specific_function": [true]}}]}
{"id": "simple_82", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers'. Expected outer type array with inner type <class 'float'>. Parameter value: [12, 15, 18, 20, 21, 26, 30]."], "error_type": "type_error:nested", "prompt": {"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}, "model_result_raw": [{"calculate_average": "{\"numbers\": [12, 15, 18, 20, 21, 26, 30]}"}], "model_result_decoded": [{"calculate_average": {"numbers": [12, 15, 18, 20, 21, 26, 30]}}], "possible_answer": [{"calculate_average": {"numbers": [[12.0, 15.0, 18.0, 20.0, 21.0, 26.0, 30.0]]}}]}
{"id": "simple_87", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'list'. Expected outer type array with inner type <class 'float'>. Parameter value: [5, 3, 4, 1, 2]."], "error_type": "type_error:nested", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": [{"array_sort": "{\"list\": [5, 3, 4, 1, 2], \"order\": \"ascending\"}"}], "model_result_decoded": [{"array_sort": {"list": [5, 3, 4, 1, 2], "order": "ascending"}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_103", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'polynomial'. Expected outer type array with inner type <class 'float'>. Parameter value: [3, 2, -4]."], "error_type": "type_error:nested", "prompt": {"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}, "model_result_raw": [{"mathematics_calculate_area_under_curve": "{\"polynomial\": [3, 2, -4], \"limits\": [-1, 2]}"}], "model_result_decoded": [{"mathematics_calculate_area_under_curve": {"polynomial": [3, 2, -4], "limits": [-1, 2]}}], "possible_answer": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3.0, 2.0, -4.0]], "limits": [[-1.0, 2.0]]}}]}
{"id": "simple_136", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'annual_rate': 0.05. Expected one of [5.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_136", "question": [[{"role": "user", "content": "Find the compound interest for an investment of $10000 with an annual interest rate of 5% compounded monthly for 5 years."}]], "function": [{"name": "compound_interest", "description": "Calculate compound interest for a certain time period.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The initial amount of money that was invested or loaned out."}, "annual_rate": {"type": "float", "description": "The interest rate for a year as a percentage."}, "compounding_freq": {"type": "string", "enum": ["monthly", "quarterly", "annually"], "description": "The number of times that interest is compounded per unit period."}, "time_in_years": {"type": "integer", "description": "The time the money is invested for in years."}}, "required": ["principal", "annual_rate", "compounding_freq", "time_in_years"]}}]}, "model_result_raw": [{"compound_interest": "{\"principal\": 10000, \"annual_rate\": 0.05, \"compounding_freq\": \"monthly\", \"time_in_years\": 5}"}], "model_result_decoded": [{"compound_interest": {"principal": 10000, "annual_rate": 0.05, "compounding_freq": "monthly", "time_in_years": 5}}], "possible_answer": [{"compound_interest": {"principal": [10000], "annual_rate": [5.0], "compounding_freq": ["monthly"], "time_in_years": [5]}}]}
{"id": "simple_151", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'top_n': 5. Expected one of [1]."], "error_type": "value_error:others", "prompt": {"id": "simple_151", "question": [[{"role": "user", "content": "Find the highest grossing bank in the U.S for year 2020."}]], "function": [{"name": "highest_grossing_banks", "description": "Retrieve the highest grossing banks in a specified country and year.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country to get the data from."}, "year": {"type": "integer", "description": "The year to get the data from."}, "top_n": {"type": "integer", "description": "Top n banks in terms of grossing. Default is 5"}}, "required": ["country", "year"]}}]}, "model_result_raw": [{"highest_grossing_banks": "{\"country\": \"U.S.\", \"year\": 2020, \"top_n\": 5}"}], "model_result_decoded": [{"highest_grossing_banks": {"country": "U.S.", "year": 2020, "top_n": 5}}], "possible_answer": [{"highest_grossing_banks": {"country": ["U.S", "United States", "USA", "U.S."], "year": [2020], "top_n": [1]}}]}
{"id": "simple_156", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'details': False. Expected one of [True]."], "error_type": "value_error:others", "prompt": {"id": "simple_156", "question": [[{"role": "user", "content": "Look up details of a felony crime record for case number CA123456 in San Diego County"}]], "function": [{"name": "crime_record.get_record", "description": "Retrieve detailed felony crime records using a specific case number and location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The case number related to the crime."}, "county": {"type": "string", "description": "The county in which the crime occurred."}, "details": {"type": "boolean", "description": "To get a detailed report, set as true. Defaults to false."}}, "required": ["case_number", "county"]}}]}, "model_result_raw": [{"crime_record_get_record": "{\"case_number\": \"CA123456\", \"county\": \"San Diego County\", \"details\": false}"}], "model_result_decoded": [{"crime_record_get_record": {"case_number": "CA123456", "county": "San Diego County", "details": false}}], "possible_answer": [{"crime_record.get_record": {"case_number": ["CA123456"], "county": ["San Diego", "San Diego County"], "details": [true]}}]}
{"id": "simple_158", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_158", "question": [[{"role": "user", "content": "Find the information of criminal cases of Mr. X in New York between 2012 and 2015."}]], "function": [{"name": "get_criminal_records", "description": "Retrieve the criminal records of a specific person in a specific area during a certain time period.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY"}, "from_year": {"type": "integer", "description": "The start year of the time frame."}, "to_year": {"type": "integer", "description": "The end year of the time frame."}}, "required": ["name", "location", "from_year", "to_year"]}}]}, "model_result_raw": [{"get_criminal_records": "{\"name\": \"Mr. X\", \"location\": \"New York\", \"from_year\": 2012, \"to_year\": 2015}"}], "model_result_decoded": [{"get_criminal_records": {"name": "Mr. X", "location": "New York", "from_year": 2012, "to_year": 2015}}], "possible_answer": [{"get_criminal_records": {"name": ["Mr. X"], "location": ["New York, NY"], "from_year": [2012], "to_year": [2015]}}]}
{"id": "simple_159", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'act_name': 'Criminal Law Amendment Act of 2013'. Expected one of ['Criminal Law Amendment Act', 'Criminal Law Amendment']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_159", "question": [[{"role": "user", "content": "Give me the details of Criminal Law Amendment Act of 2013."}]], "function": [{"name": "get_act_details", "description": "Retrieve the details of a particular legal act based on its name and year of amendment if any.", "parameters": {"type": "dict", "properties": {"act_name": {"type": "string", "description": "The name of the act."}, "amendment_year": {"type": "integer", "description": "Year of amendment if any. If not provided, the latest amendment year will be considered."}}, "required": ["act_name", "amendment_year"]}}]}, "model_result_raw": [{"get_act_details": "{\"act_name\": \"Criminal Law Amendment Act of 2013\", \"amendment_year\": 2013}"}], "model_result_decoded": [{"get_act_details": {"act_name": "Criminal Law Amendment Act of 2013", "amendment_year": 2013}}], "possible_answer": [{"get_act_details": {"act_name": ["Criminal Law Amendment Act", "Criminal Law Amendment"], "amendment_year": [2013]}}]}
{"id": "simple_166", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'city': 'Chicago'. Expected one of ['Chicago, IL.', 'Chicago, IL']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_166", "question": [[{"role": "user", "content": "Find a lawyer specializing in divorce cases and charge fee less than 400 dollars per hour in Chicago."}]], "function": [{"name": "lawyer.find_nearby", "description": "Locate nearby lawyers based on specific criteria like specialty, fee per hour and city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city and state, e.g. Chicago, IL."}, "specialty": {"type": "array", "items": {"type": "string", "enum": ["Civil", "Divorce", "Immigration", "Business", "Criminal"]}, "description": "Specialization of the lawyer."}, "fee": {"type": "integer", "description": "Hourly fee charged by lawyer"}}, "required": ["city", "specialty", "fee"]}}]}, "model_result_raw": [{"lawyer_find_nearby": "{\"city\": \"Chicago\", \"specialty\": [\"Divorce\"], \"fee\": 399}"}], "model_result_decoded": [{"lawyer_find_nearby": {"city": "Chicago", "specialty": ["Divorce"], "fee": 399}}], "possible_answer": [{"lawyer.find_nearby": {"city": ["Chicago, IL.", "Chicago, IL"], "specialty": [["Divorce"]], "fee": [400]}}]}
{"id": "simple_196", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'date': '2022/08/16'. Expected one of ['08-16-2022']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_196", "question": [[{"role": "user", "content": "What is the air quality index in London 2022/08/16?"}]], "function": [{"name": "air_quality", "description": "Retrieve the air quality index for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality index for."}, "date": {"type": "string", "description": "The date (month-day-year) you want to get the air quality index for."}}, "required": ["location", "date"]}}]}, "model_result_raw": [{"air_quality": "{\"location\": \"London\", \"date\": \"2022/08/16\"}"}], "model_result_decoded": [{"air_quality": {"location": "London", "date": "2022/08/16"}}], "possible_answer": [{"air_quality": {"location": ["London"], "date": ["08-16-2022"]}}]}
{"id": "simple_200", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'fuel_type': 'gas-fueled'. Expected one of ['gas']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_200", "question": [[{"role": "user", "content": "How much CO2 is produced annually by a gas-fueled car that travels 12,000 miles per year, with fuel efficiency of 25 MPG ?"}]], "function": [{"name": "calculate_emissions", "description": "Calculates the annual carbon dioxide emissions produced by a vehicle based on the distance traveled, the fuel type and the fuel efficiency of the vehicle.", "parameters": {"type": "dict", "properties": {"distance": {"type": "integer", "description": "The distance travelled in miles."}, "fuel_type": {"type": "string", "description": "Type of fuel used by the vehicle."}, "fuel_efficiency": {"type": "float", "description": "The vehicle's fuel efficiency in miles per gallon."}, "efficiency_reduction": {"type": "integer", "description": "The percentage decrease in fuel efficiency per year (optional). Default is 0"}}, "required": ["distance", "fuel_type", "fuel_efficiency"]}}]}, "model_result_raw": [{"calculate_emissions": "{\"distance\": 12000, \"fuel_type\": \"gas-fueled\", \"fuel_efficiency\": 25.0}"}], "model_result_decoded": [{"calculate_emissions": {"distance": 12000, "fuel_type": "gas-fueled", "fuel_efficiency": 25.0}}], "possible_answer": [{"calculate_emissions": {"distance": [12000], "fuel_type": ["gas"], "fuel_efficiency": ["", 25.0], "efficiency_reduction": [0, ""]}}]}
{"id": "simple_203", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'detail': False. Expected one of [True]."], "error_type": "value_error:others", "prompt": {"id": "simple_203", "question": [[{"role": "user", "content": "Can you find me the latest information about air quality index and pollution data for Chicago?"}]], "function": [{"name": "get_air_quality", "description": "Retrieve real-time air quality and pollution data for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality data for."}, "detail": {"type": "boolean", "description": "If true, additional data like PM2.5, PM10, ozone levels, and pollution sources will be retrieved. Default is false."}}, "required": ["location"]}}]}, "model_result_raw": [{"get_air_quality": "{\"location\": \"Chicago\", \"detail\": false}"}], "model_result_decoded": [{"get_air_quality": {"location": "Chicago", "detail": false}}], "possible_answer": [{"get_air_quality": {"location": ["Chicago"], "detail": [true]}}]}
{"id": "simple_217", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'voxel_size' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_217", "question": [[{"role": "user", "content": "Analyze my fMRI data in ~/data/myfMRI.nii from a multi-band sequence, that is smoothed at 6mm with an isotropic voxel size of 2mm."}]], "function": [{"name": "fMRI.analyze", "description": "This function takes in fMRI data to output analyzed data.", "parameters": {"type": "dict", "properties": {"data_source": {"type": "string", "description": "The path where the data is stored."}, "sequence_type": {"type": "string", "description": "Type of fMRI sequence"}, "smooth": {"type": "integer", "description": "Spatial smoothing FWHM. In mm."}, "voxel_size": {"type": "integer", "description": "Size of isotropic voxels in mm.", "default": 3}}, "required": ["data_source", "sequence_type", "smooth"]}}]}, "model_result_raw": [{"fMRI_analyze": "{\"data_source\": \"~/data/myfMRI.nii\", \"sequence_type\": \"multi-band\", \"smooth\": 6}"}], "model_result_decoded": [{"fMRI_analyze": {"data_source": "~/data/myfMRI.nii", "sequence_type": "multi-band", "smooth": 6}}], "possible_answer": [{"fMRI.analyze": {"data_source": ["~/data/myfMRI.nii"], "sequence_type": ["multi-band"], "smooth": [6], "voxel_size": [2]}}]}
{"id": "simple_224", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'sub_topics': ['behavior', 'group dynamics']. Expected one of [['behaviour', 'group dynamics']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_224", "question": [[{"role": "user", "content": "Find the most followed person on twitter who tweets about psychology related to behaviour and group dynamics."}]], "function": [{"name": "social_media_analytics.most_followed", "description": "Find the most followed Twitter user related to certain topics.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The main topic of interest."}, "sub_topics": {"type": "array", "items": {"type": "string"}, "description": "Sub-topics related to main topic. Default is empty."}, "region": {"type": "string", "description": "Region of interest for twitter search. Default is 'all'."}}, "required": ["topic"]}}]}, "model_result_raw": [{"social_media_analytics_most_followed": "{\"topic\": \"psychology\", \"sub_topics\": [\"behavior\", \"group dynamics\"], \"region\": \"all\"}"}], "model_result_decoded": [{"social_media_analytics_most_followed": {"topic": "psychology", "sub_topics": ["behavior", "group dynamics"], "region": "all"}}], "possible_answer": [{"social_media_analytics.most_followed": {"topic": ["psychology"], "sub_topics": [["behaviour", "group dynamics"]], "region": ["", "all"]}}]}
{"id": "simple_267", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York City, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_267", "question": [[{"role": "user", "content": "Find the top rated modern sculpture exhibition happening in New York in the upcoming month."}]], "function": [{"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the exhibition is held, e.g., New York City, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events if not specified."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'low'"}}, "required": ["location", "art_form"]}}]}, "model_result_raw": [{"find_exhibition": "{\"location\": \"New York\", \"art_form\": \"modern sculpture\", \"month\": \"upcoming\", \"user_ratings\": \"high\"}"}], "model_result_decoded": [{"find_exhibition": {"location": "New York", "art_form": "modern sculpture", "month": "upcoming", "user_ratings": "high"}}], "possible_answer": [{"find_exhibition": {"location": ["New York City, NY"], "art_form": ["sculpture", "modern sculpture"], "month": [""], "user_ratings": ["high"]}}]}
{"id": "simple_284", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'date': '2023-10'. Expected one of ['next month', '2023-06-01', '06/01/2023', 'Jun.1,2023', 'June 2023']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_284", "question": [[{"role": "user", "content": "Get information about the pop concerts in New York for next month."}]], "function": [{"name": "concert_info.get", "description": "Retrieve information about concerts based on specific genre, location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the concert will take place."}, "date": {"type": "string", "description": "Time frame to get the concert for."}, "genre": {"type": "string", "description": "Genre of the concert.", "enum": ["Pop", "Rock", "Country", "Classical", "Electronic", "Hip-Hop"]}}, "required": ["location", "date", "genre"]}}]}, "model_result_raw": [{"concert_info_get": "{\"location\": \"New York\", \"date\": \"2023-10\", \"genre\": \"Pop\"}"}], "model_result_decoded": [{"concert_info_get": {"location": "New York", "date": "2023-10", "genre": "Pop"}}], "possible_answer": [{"concert_info.get": {"location": ["New York City, NY", "New York"], "date": ["next month", "2023-06-01", "06/01/2023", "Jun.1,2023", "June 2023"], "genre": ["Pop"]}}]}
{"id": "simple_299", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'key': 'C sharp major'. Expected one of ['C#', 'C sharp']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_299", "question": [[{"role": "user", "content": "What is the musical scale associated with C sharp major?"}]], "function": [{"name": "musical_scale", "description": "Get the musical scale of a specific key in music theory.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The musical key for which the scale will be found."}, "scale_type": {"type": "string", "default": "major", "description": "The type of musical scale."}}, "required": ["key"]}}]}, "model_result_raw": [{"musical_scale": "{\"key\": \"C sharp major\"}"}], "model_result_decoded": [{"musical_scale": {"key": "C sharp major"}}], "possible_answer": [{"musical_scale": {"key": ["C#", "C sharp"], "scale_type": ["major", ""]}}]}
{"id": "simple_304", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'team': 'NBA'. Expected one of ['Los Angeles Lakers', 'LAL', 'Lakers']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_304", "question": [[{"role": "user", "content": "Get point and rebound stats for player 'LeBron James' from last basketball game"}]], "function": [{"name": "player_stats.getLastGame", "description": "Get last game statistics for a specific player in basketball", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the basketball player."}, "team": {"type": "string", "description": "The team that player currently plays for."}, "metrics": {"type": "array", "items": {"type": "string", "enum": ["Points", "Rebounds", "Assists", "Blocks"]}, "description": "Specific metrics to retrieve. If no value is specified, all available metrics will be returned by default."}}, "required": ["player_name", "team"]}}]}, "model_result_raw": [{"player_stats_getLastGame": "{\"player_name\": \"LeBron James\", \"team\": \"NBA\", \"metrics\": [\"Points\", \"Rebounds\"]}"}], "model_result_decoded": [{"player_stats_getLastGame": {"player_name": "LeBron James", "team": "NBA", "metrics": ["Points", "Rebounds"]}}], "possible_answer": [{"player_stats.getLastGame": {"player_name": ["LeBron James"], "team": ["Los Angeles Lakers", "LAL", "Lakers"], "metrics": [["Points", "Rebounds"]]}}]}
{"id": "simple_354", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'cooking_time': 25. Expected one of [30]."], "error_type": "value_error:others", "prompt": {"id": "simple_354", "question": [[{"role": "user", "content": "Find a vegan soup recipe that takes under 30 minutes to make."}]], "function": [{"name": "get_vegan_recipe", "description": "Retrieve a vegan soup recipe based on the provided cooking time.", "parameters": {"type": "dict", "properties": {"dish_type": {"type": "string", "description": "The type of dish, e.g. soup, dessert, etc.", "enum": ["soup", "main dish", "dessert", "salad"]}, "cooking_time": {"type": "integer", "description": "The maximum cooking time for the recipe in minutes."}, "ingredient_preference": {"type": "array", "items": {"type": "string"}, "description": "Preferred ingredients to be included in the recipe, if any. Default to not use it if not provided."}}, "required": ["dish_type", "cooking_time"]}}]}, "model_result_raw": [{"get_vegan_recipe": "{\"dish_type\": \"soup\", \"cooking_time\": 25}"}], "model_result_decoded": [{"get_vegan_recipe": {"dish_type": "soup", "cooking_time": 25}}], "possible_answer": [{"get_vegan_recipe": {"dish_type": ["soup"], "cooking_time": [30], "ingredient_preference": [""]}}]}
{"id": "simple_356", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'prep_time': 29. Expected one of [30]."], "error_type": "value_error:others", "prompt": {"id": "simple_356", "question": [[{"role": "user", "content": "Find me a recipe that serves 2 people, is vegan, and takes under 30 minutes to prepare."}]], "function": [{"name": "recipe_finder.find", "description": "Find a recipe based on dietary preferences, number of servings, and preparation time.", "parameters": {"type": "dict", "properties": {"servings": {"type": "integer", "description": "The number of people that the recipe should serve."}, "diet": {"type": "string", "description": "Any dietary restrictions like 'vegan', 'vegetarian', 'gluten-free' etc."}, "prep_time": {"type": "integer", "description": "The maximum amount of time (in minutes) the preparation should take. Default is 60 minutes."}}, "required": ["servings", "diet"]}}]}, "model_result_raw": [{"recipe_finder_find": "{\"servings\": 2, \"diet\": \"vegan\", \"prep_time\": 29}"}], "model_result_decoded": [{"recipe_finder_find": {"servings": 2, "diet": "vegan", "prep_time": 29}}], "possible_answer": [{"recipe_finder.find": {"servings": [2], "diet": ["vegan"], "prep_time": [30]}}]}
{"id": "simple_358", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'time_limit': 29. Expected one of [30]."], "error_type": "value_error:others", "prompt": {"id": "simple_358", "question": [[{"role": "user", "content": "Find a gluten-free cookie recipe that takes less than 30 minutes to prepare."}]], "function": [{"name": "recipe_search", "description": "Search for a cooking recipe based on specific dietary needs and time constraint.", "parameters": {"type": "dict", "properties": {"diet": {"type": "array", "items": {"type": "string", "enum": ["Gluten Free", "Dairy Free", "Vegan", "Vegetarian"]}, "description": "Specific dietary need."}, "time_limit": {"type": "integer", "description": "The maximum time to prepare the recipe in minutes. Default is 60 minutes."}, "dish": {"type": "string", "description": "The name of the dish to search for. Default is not use if not specified."}}, "required": ["dish", "diet"]}}]}, "model_result_raw": [{"recipe_search": "{\"diet\": [\"Gluten Free\"], \"time_limit\": 29, \"dish\": \"cookie\"}"}], "model_result_decoded": [{"recipe_search": {"diet": ["Gluten Free"], "time_limit": 29, "dish": "cookie"}}], "possible_answer": [{"recipe_search": {"diet": [["Gluten Free"], ["GF"], ["gluten free"]], "time_limit": [30], "dish": ["cookie"]}}]}
{"id": "simple_360", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'maxCalories': 499. Expected one of [500]."], "error_type": "value_error:others", "prompt": {"id": "simple_360", "question": [[{"role": "user", "content": "Find a recipe for pasta carbonara which contains only less than 500 calories."}]], "function": [{"name": "find_recipe", "description": "Locate a recipe based on name and its calorie content", "parameters": {"type": "dict", "properties": {"recipeName": {"type": "string", "description": "The recipe's name."}, "maxCalories": {"type": "integer", "description": "The maximum calorie content of the recipe.", "default": 1000}}, "required": ["recipeName"]}}]}, "model_result_raw": [{"find_recipe": "{\"recipeName\": \"pasta carbonara\", \"maxCalories\": 499}"}], "model_result_decoded": [{"find_recipe": {"recipeName": "pasta carbonara", "maxCalories": 499}}], "possible_answer": [{"find_recipe": {"recipeName": ["pasta carbonara"], "maxCalories": [500]}}]}
{"id": "simple_367", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'recipe_type': 'main course'. Expected one of ['dessert']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_367", "question": [[{"role": "user", "content": "Find me a vegan recipe for brownies which prep time is under 30 minutes."}]], "function": [{"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}]}, "model_result_raw": [{"find_recipe": "{\"dietary_restrictions\": \"vegan\", \"recipe_type\": \"main course\", \"time\": 30}"}], "model_result_decoded": [{"find_recipe": {"dietary_restrictions": "vegan", "recipe_type": "main course", "time": 30}}], "possible_answer": [{"find_recipe": {"dietary_restrictions": ["vegan"], "recipe_type": ["dessert"], "time": [30]}}]}
{"id": "simple_369", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'categories': ['Organic', 'Fruits']. Expected one of [['Organic', 'Vegetables', 'Fruits'], ['Organic', 'Fruits', 'Vegetables'], ['Vegetables', 'Fruits', 'Organic'], ['Fruits', 'Vegetables', 'Organic'], ['Fruits', 'Organic', 'Vegetables'], ['Vegetables', 'Organic', 'Fruits']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_369", "question": [[{"role": "user", "content": "Find a grocery store near me with organic fruits and vegetables in Houston."}]], "function": [{"name": "grocery_store.find_nearby", "description": "Locate nearby grocery stores based on specific criteria like organic fruits and vegetables.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Houston, TX"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["Organic", "Vegetables", "Fruits", "Dairy", "Seafood", "Bakery"]}, "description": "Categories of items to be found in the grocery store. Default is all if not specified."}}, "required": ["location"]}}]}, "model_result_raw": [{"grocery_store_find_nearby": "{\"location\": \"Houston, TX\", \"categories\": [\"Organic\", \"Fruits\"]}"}], "model_result_decoded": [{"grocery_store_find_nearby": {"location": "Houston, TX", "categories": ["Organic", "Fruits"]}}], "possible_answer": [{"grocery_store.find_nearby": {"location": ["Houston", "Houston, TX"], "categories": [["Organic", "Vegetables", "Fruits"], ["Organic", "Fruits", "Vegetables"], ["Vegetables", "Fruits", "Organic"], ["Fruits", "Vegetables", "Organic"], ["Fruits", "Organic", "Vegetables"], ["Vegetables", "Organic", "Fruits"]]}}]}
{"id": "simple_370", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'quantity': [3, 5]. Expected one of [[3, 1]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": [{"safeway_order": "{\"location\": \"Palo Alto, CA\", \"items\": [\"olive oil\", \"rice\"], \"quantity\": [3, 5]}"}], "model_result_decoded": [{"safeway_order": {"location": "Palo Alto, CA", "items": ["olive oil", "rice"], "quantity": [3, 5]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_373", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'pack_size': [12]. Expected one of [[1, 1, 12]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_373", "question": [[{"role": "user", "content": "I want to buy apples, rice, and 12 pack of bottled water from a Walmart near San Jose. Show me the product information and stock availability."}]], "function": [{"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is not use it if not specified."}}, "required": ["loc", "product_list"]}}]}, "model_result_raw": [{"walmart_purchase": "{\"loc\": \"San Jose\", \"product_list\": [\"apples\", \"rice\", \"bottled water\"], \"pack_size\": [12]}"}], "model_result_decoded": [{"walmart_purchase": {"loc": "San Jose", "product_list": ["apples", "rice", "bottled water"], "pack_size": [12]}}], "possible_answer": [{"walmart.purchase": {"loc": ["San Jose", "San Jose, CA"], "product_list": [["apples", "rice", "bottled water"], ["apples", "rice", "water"]], "pack_size": [[1, 1, 12]]}}]}
{"id": "simple_376", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Optional parameter 'display_format' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_376", "question": [[{"role": "user", "content": "What time is it currently in London, UK in 24 hour format?"}]], "function": [{"name": "time_zone_converter", "description": "Retrieve the current time of a specific city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city you want to know the current time for."}, "country": {"type": "string", "description": "The country where the city is located."}, "display_format": {"type": "string", "description": "The time display format: '12h' or '24h'. Default is '24h'."}}, "required": ["city", "country"]}}]}, "model_result_raw": [{"time_zone_converter": "{\"city\": \"London\", \"country\": \"UK\"}"}], "model_result_decoded": [{"time_zone_converter": {"city": "London", "country": "UK"}}], "possible_answer": [{"time_zone_converter": {"city": ["London"], "country": ["UK", "United Kingdom"], "display_format": ["24h", "24 hour"]}}]}
{"id": "simple_387", "model_name": "Qwen_Qwen3-1.7B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'end_date': '2022-06-03'. Expected one of ['2022-06-04', '06/04/2022', 'Jun.4,2022']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_387", "question": [[{"role": "user", "content": "Book a hotel room at the Plaza Hotel in New York for 3 nights starting from 1st June 2022"}]], "function": [{"name": "hotel_booking", "description": "Books a hotel room for a specific date range.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY."}, "start_date": {"type": "string", "description": "The start date of the reservation. Use format 'YYYY-MM-DD'."}, "end_date": {"type": "string", "description": "The end date of the reservation. Use format 'YYYY-MM-DD'."}, "rooms": {"type": "integer", "default": 1, "description": "The number of rooms to reserve."}}, "required": ["hotel_name", "location", "start_date", "end_date"]}}]}, "model_result_raw": [{"hotel_booking": "{\"hotel_name\": \"Plaza Hotel\", \"location\": \"New York, NY\", \"start_date\": \"2022-06-01\", \"end_date\": \"2022-06-03\", \"rooms\": 3}"}], "model_result_decoded": [{"hotel_booking": {"hotel_name": "Plaza Hotel", "location": "New York, NY", "start_date": "2022-06-01", "end_date": "2022-06-03", "rooms": 3}}], "possible_answer": [{"hotel_booking": {"hotel_name": ["Plaza Hotel"], "location": ["New York City, NY", "New York, NY"], "start_date": ["2022-06-01", "06/01/2022", "Jun.1,2022"], "end_date": ["2022-06-04", "06/04/2022", "Jun.4,2022"], "rooms": [1, ""]}}]}