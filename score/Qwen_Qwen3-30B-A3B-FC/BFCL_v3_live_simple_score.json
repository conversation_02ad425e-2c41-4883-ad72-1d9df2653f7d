{"accuracy": 0.875968992248062, "correct_count": 226, "total_count": 258}
{"id": "live_simple_10-3-6", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'location': 'Yosemite National Park, CA'. Expected one of ['Mariposa, CA']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_10-3-6", "question": [[{"role": "user", "content": "Could you tell me the current weather in Yosemite National Park which locates at Mariposa, CA, and make sure to give me the temperature in Celsius?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)' or 'Location, State', such as 'San Francisco, CA' if State exists. 'City, Country' if State for the city doesn't exist. "}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}, "model_result_raw": [{"get_current_weather": "{\"location\": \"Yosemite National Park, CA\", \"unit\": \"celsius\"}"}], "model_result_decoded": [{"get_current_weather": {"location": "Yosemite National Park, CA", "unit": "celsius"}}], "possible_answer": [{"get_current_weather": {"location": ["Mariposa, CA"], "unit": ["celsius"]}}]}
{"id": "live_simple_13-3-9", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'location': 'Shanghai, CN'. Expected one of ['Shanghai, China', '\\\\u4e0a\\\\u6d77,\\\\u4e2d\\\\u56fd']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_13-3-9", "question": [[{"role": "user", "content": "\u6211\u60f3\u77e5\u9053\u4e0a\u6d77\u76ee\u524d\u7684\u5929\u6c14\u72b6\u51b5\uff0c\u53ef\u4ee5\u5e2e\u6211\u67e5\u8be2\u5417\uff1f\u987a\u4fbf\u4f7f\u7528\u6444\u6c0f\u5ea6\u6765\u663e\u793a\u6e29\u5ea6\u3002"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}, "model_result_raw": [{"get_current_weather": "{\"location\": \"Shanghai, CN\", \"unit\": \"celsius\"}"}], "model_result_decoded": [{"get_current_weather": {"location": "Shanghai, CN", "unit": "celsius"}}], "possible_answer": [{"get_current_weather": {"location": ["Shanghai, China", "\\u4e0a\\u6d77,\\u4e2d\\u56fd"], "unit": ["celsius"]}}]}
{"id": "live_simple_26-6-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'loc': '123 \u0110\u01b0\u1eddng \u0110\u1ea1i h\u1ecdc, Berkeley, CA 94704'. Expected one of ['123 \u0110\u01b0\u1eddng \u0110\u1ea1i h\u1ecdc, Berkeley, CA', '123 University Street, Berkeley, CA']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_26-6-0", "question": [[{"role": "user", "content": "T\u00f4i c\u1ea7n m\u1ed9t chuy\u1ebfn Uber lo\u1ea1i 'Plus' t\u1eeb \u0111\u1ecba ch\u1ec9 123 \u0110\u01b0\u1eddng \u0110\u1ea1i h\u1ecdc, Berkeley, CA 94704 v\u00e0 t\u00f4i kh\u00f4ng mu\u1ed1n ch\u1edd qu\u00e1 10 ph\u00fat."}]], "function": [{"name": "uber.ride", "description": "T\u00ecm chuy\u1ebfn \u0111i ph\u00f9 h\u1ee3p cho kh\u00e1ch h\u00e0ng d\u1ef1a tr\u00ean v\u1ecb tr\u00ed, lo\u1ea1i chuy\u1ebfn \u0111i v\u00e0 kho\u1ea3ng th\u1eddi gian kh\u00e1ch h\u00e0ng s\u1eb5n s\u00e0ng ch\u1edd \u0111\u1ee3i l\u00e0m th\u00f4ng s\u1ed1'", "parameters": {"type": "dict", "required": ["loc", "type", "time"], "properties": {"loc": {"type": "string", "description": "The starting location of the Uber ride in the format of 'Address, City, State'. For example, '123 Main St, Springfield, IL'."}, "type": {"type": "string", "description": "The type of Uber ride the user is requesting.", "enum": ["plus", "comfort", "black"]}, "time": {"type": "integer", "description": "The maximum amount of time the customer is willing to wait for the ride, in minutes."}}}}]}, "model_result_raw": [{"uber_ride": "{\"loc\": \"123 \u0110\u01b0\u1eddng \u0110\u1ea1i h\u1ecdc, Berkeley, CA 94704\", \"type\": \"plus\", \"time\": 10}"}], "model_result_decoded": [{"uber_ride": {"loc": "123 \u0110\u01b0\u1eddng \u0110\u1ea1i h\u1ecdc, Berkeley, CA 94704", "type": "plus", "time": 10}}], "possible_answer": [{"uber.ride": {"loc": ["123 \u0110\u01b0\u1eddng \u0110\u1ea1i h\u1ecdc, Berkeley, CA", "123 University Street, Berkeley, CA"], "type": ["plus"], "time": [10]}}]}
{"id": "live_simple_31-8-1", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'sortBy': 'LastUpdatedDateTime'. Expected one of ['DESC']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_31-8-1", "question": [[{"role": "user", "content": "Could you retrieve the list of exports for my bot with ID 'B12345' and for version 'v1'? I'm interested in seeing them in descending order by the last updated date."}]], "function": [{"name": "aws.lexv2_models.list_exports", "description": "Lists the exports for a bot, bot locale, or custom vocabulary within Amazon Lex. This data is retained for a duration of 7 days.", "parameters": {"type": "dict", "required": ["botId", "botVersion"], "properties": {"botId": {"type": "string", "description": "The unique identifier for the bot as assigned by Amazon Lex."}, "botVersion": {"type": "string", "description": "The specific version of the bot for which to list the exports."}, "sortBy": {"type": "string", "description": "Determines the field to sort the list of exports by. Can be sorted by 'LastUpdatedDateTime' in ascending or descending order.", "enum": ["ASC", "DESC"], "default": "ASC"}, "filterName": {"type": "string", "description": "The name of the field to filter the exports by. Possible values are 'Bot', 'BotLocale', or 'CustomVocabulary'.", "default": null}, "filterOperator": {"type": "string", "description": "The operator to use for filtering. Use 'EQ' for equality or 'CO' for containing the specified value.", "enum": ["EQ", "CO"], "default": "EQ"}, "filterValue": {"type": "string", "description": "The value to use for filtering the exports based on the filterName.", "default": null}, "maxResults": {"type": "integer", "description": "The maximum number of exports to return in each page of results. If there are fewer results than the max page size, only the actual number of results are returned.", "default": 50}, "nextToken": {"type": "string", "description": "A token to retrieve the next page of results if the response from the ListExports operation contains more results than specified in the maxResults parameter.", "default": null}, "localeId": {"type": "string", "description": "Specifies the locale of the resources that should be exported. If not specified, both bot locales and custom vocabularies are exported.", "default": null}}}}]}, "model_result_raw": [{"aws_lexv2_models_list_exports": "{\"botId\": \"B12345\", \"botVersion\": \"v1\", \"sortBy\": \"LastUpdatedDateTime\", \"sortOrder\": \"DESC\"}"}], "model_result_decoded": [{"aws_lexv2_models_list_exports": {"botId": "B12345", "botVersion": "v1", "sortBy": "LastUpdatedDateTime", "sortOrder": "DESC"}}], "possible_answer": [{"aws.lexv2_models.list_exports": {"botId": ["B12345"], "botVersion": ["v1"], "sortBy": ["DESC"], "filterName": ["", null], "filterOperator": ["", "EQ"], "filterValue": ["", null], "maxResults": ["", 50], "nextToken": ["", null], "localeId": ["", null]}}]}
{"id": "live_simple_40-17-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Missing dict key parameter: 'airCleanOperationMode'."], "error_type": "value_error:dict_key", "prompt": {"id": "live_simple_40-17-0", "question": [[{"role": "user", "content": "I'd like to start the air purifying function in my living room, please set the air conditioner to 'AIR_CLEAN' mode with strongest wind strength, with monitoring."}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an LG ThinQ appliance, such as an air conditioner, by setting various operation modes and target settings.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the settings and modes to control the LG ThinQ appliance.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for air cleaning.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling in degrees Celsius. Valid values range from 18 to 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Valid values range from 18 to 30.", "default": 22}}}}}}]}, "model_result_raw": [{"ThinQ_Connect": "{\"body\": {\"airConJobMode\": \"AIR_CLEAN\", \"windStrength\": \"HIGH\", \"monitoringEnabled\": true}}"}], "model_result_decoded": [{"ThinQ_Connect": {"body": {"airConJobMode": "AIR_CLEAN", "windStrength": "HIGH", "monitoringEnabled": true}}}], "possible_answer": [{"ThinQ_Connect": {"body": [{"airConJobMode": ["AIR_CLEAN"], "windStrength": ["HIGH"], "monitoringEnabled": [true], "airCleanOperationMode": ["POWER_ON"], "powerSaveEnabled": ["", false], "coolTargetTemperature": ["", 24], "targetTemperature": ["", 22]}]}}]}
{"id": "live_simple_42-17-2", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'airConJobMode': 'AIR_DRY'. Expected one of ['', 'cool'].", "Missing dict key parameter: 'coolTargetTemperature'."], "error_type": "value_error:dict_key", "prompt": {"id": "live_simple_42-17-2", "question": [[{"role": "user", "content": "\uc9d1\uc5d0 \uc788\ub294 LG ThinQ \uc5d0\uc5b4\ucee8\uc744 \uc81c\uc2b5 \ubaa8\ub4dc\ub85c \uc124\uc815\ud558\uace0 \uc2f6\uc5b4\uc694. \ubc14\ub78c \uc138\uae30\ub294 \uc911\uac04\uc73c\ub85c \ud558\uace0, \ubaa9\ud45c \uc628\ub3c4\ub294 22\ub3c4\ub85c \uc124\uc815\ud574 \uc8fc\uc138\uc694."}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an LG ThinQ appliance, such as an air conditioner, by setting various operation modes and target settings.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the settings and modes to control the LG ThinQ appliance.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for air cleaning.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling in degrees Celsius. Valid values range from 18 to 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Valid values range from 18 to 30.", "default": 22}}}}}}]}, "model_result_raw": [{"ThinQ_Connect": "{\"body\": {\"airConJobMode\": \"AIR_DRY\", \"windStrength\": \"MID\", \"targetTemperature\": 22}}"}], "model_result_decoded": [{"ThinQ_Connect": {"body": {"airConJobMode": "AIR_DRY", "windStrength": "MID", "targetTemperature": 22}}}], "possible_answer": [{"ThinQ_Connect": {"body": [{"airConJobMode": ["", "COOL"], "windStrength": ["", "MID"], "monitoringEnabled": ["", false], "airCleanOperationMode": ["POWER_OFF", ""], "powerSaveEnabled": ["", false], "coolTargetTemperature": [22], "targetTemperature": ["", 22]}]}}]}
{"id": "live_simple_45-18-1", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'targetTemperature': 20. Expected one of ['', 22].", "Missing dict key parameter: 'coolTargetTemperature'."], "error_type": "value_error:dict_key", "prompt": {"id": "live_simple_45-18-1", "question": [[{"role": "user", "content": "It's sooo hot. set the air conditioner to 'COOL' mode, temp to 20 degrees oC, while air cleaning on?"}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an appliance, such as setting operation modes, air flow strength, and target temperatures.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing various control parameters for the appliance.", "properties": {"currentJobMode": {"type": "string", "description": "The current mode of operation for the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength level of the airflow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality sensor monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for the air cleaning process.", "enum": ["START", "STOP"], "default": "STOP"}, "airConOperationMode": {"type": "string", "description": "The operation mode for turning the air conditioner on or off.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling mode in degrees Celsius. Value must be between 18 and 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Value must be between 18 and 30.", "default": 22}}}}}}]}, "model_result_raw": [{"ThinQ_Connect": "{\"body\": {\"currentJobMode\": \"COOL\", \"targetTemperature\": 20, \"airCleanOperationMode\": \"START\", \"airConOperationMode\": \"POWER_ON\"}}"}], "model_result_decoded": [{"ThinQ_Connect": {"body": {"currentJobMode": "COOL", "targetTemperature": 20, "airCleanOperationMode": "START", "airConOperationMode": "POWER_ON"}}}], "possible_answer": [{"ThinQ_Connect": {"body": [{"currentJobMode": ["", "COOL"], "windStrength": ["", "MID"], "monitoringEnabled": ["", false], "airCleanOperationMode": ["START"], "airConOperationMode": ["POWER_ON"], "powerSaveEnabled": ["", false], "coolTargetTemperature": [20], "targetTemperature": ["", 22]}]}}]}
{"id": "live_simple_49-21-1", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'taste': 'bitter'. Expected one of ['slightly bitter']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_49-21-1", "question": [[{"role": "user", "content": "Do you have any porters in stock? with a rich, chocolatey aroma and a slightly bitter taste. It should be dark in color, preferably from Creek brewery. All other preferences are the standard ones. Any recommendations?"}]], "function": [{"name": "find_beer", "description": "Recommend a beer based on specified attributes such as brewery, taste, aroma, color, style, and more.", "parameters": {"type": "dict", "required": ["brewery", "taste", "aroma", "color", "style"], "properties": {"brewery": {"type": "string", "description": "The brewery name to find similar beers."}, "taste": {"type": "string", "description": "The desired taste profile in the beer, such as 'bitter', 'sweet', 'sour'."}, "aroma": {"type": "string", "description": "The desired aroma profile in the beer, such as 'fruity', 'hoppy', 'malty'."}, "color": {"type": "string", "description": "The desired color of the beer, such as 'pale', 'amber', 'dark'."}, "style": {"type": "string", "description": "The style of beer being searched for, such as 'IPA', 'stout', 'lager'."}, "abv_min": {"type": "float", "description": "The minimum alcohol by volume (ABV) percentage, typically a value under 12.5%.", "default": 0.0}, "abv_max": {"type": "float", "description": "The maximum alcohol by volume (ABV) percentage, a value logically above 0%.", "default": 12.5}, "ibu_min": {"type": "integer", "description": "The minimum International Bitterness Units (IBU) score, typically a value under 120.", "default": 0}, "ibu_max": {"type": "integer", "description": "The maximum International Bitterness Units (IBU) score, logically above 0.", "default": 120}, "pairings": {"type": "array", "items": {"type": "string"}, "description": "A list of food items to pair with the beer, such as 'burger', 'cheese', 'chocolate'.", "default": []}}}}]}, "model_result_raw": [{"find_beer": "{\"brewery\": \"Creek\", \"taste\": \"bitter\", \"aroma\": \"chocolatey\", \"color\": \"dark\", \"style\": \"porter\"}"}], "model_result_decoded": [{"find_beer": {"brewery": "Creek", "taste": "bitter", "aroma": "chocolatey", "color": "dark", "style": "porter"}}], "possible_answer": [{"find_beer": {"brewery": ["Creek", "Creek brewery", "Creek Brewery", "Brewery Creek", "brewery creek"], "taste": ["slightly bitter"], "aroma": ["chocolatey"], "color": ["dark"], "style": ["porter"], "abv_min": ["", 0.0], "abv_max": ["", 12.5], "ibu_min": ["", 0], "ibu_max": ["", 120], "pairings": ["", []]}}]}
{"id": "live_simple_52-23-1", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Missing dict key parameter: 'airConJobMode'."], "error_type": "value_error:dict_key", "prompt": {"id": "live_simple_52-23-1", "question": [[{"role": "user", "content": "It's extremely hot today and my air conditioner is currently turned off. Set it to start cooling at 20 degrees Celsius mid wind strength. power off after an hour"}]], "function": [{"name": "ThinQ_Connect", "description": "Sends a command to control an appliance, allowing only a single value to be sent in one call.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the parameters for the appliance command.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Whether air quality sensor monitoring is enabled.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for the air cleaning function.", "enum": ["START", "STOP"], "default": "STOP"}, "airConOperationMode": {"type": "string", "description": "The operation mode for the air conditioner, indicating if it is turned on or off.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Whether power save mode is enabled to conserve energy.", "default": false}, "targetTemperature": {"type": "integer", "description": "The target temperature to set for the appliance, in degrees Celsius.", "default": 20}, "relativeHourToStop": {"type": "integer", "description": "The number of hours from the current time when the appliance should stop. If set to null, the appliance will not be scheduled to stop.", "default": null}, "relativeMinuteToStop": {"type": "integer", "description": "The number of minutes from the current time when the appliance should stop. If set to null, the appliance will not be scheduled to stop.", "default": null}, "relativeHourToStart": {"type": "integer", "description": "The number of hours from the current time when the appliance should start. If set to null, the appliance will not be scheduled to start.", "default": null}, "relativeMinuteToStart": {"type": "integer", "description": "The number of minutes from the current time when the appliance should start. If set to null, the appliance will not be scheduled to start.", "default": null}}}}}}]}, "model_result_raw": [{"ThinQ_Connect": "{\"body\": {\"airConOperationMode\": \"POWER_ON\", \"targetTemperature\": 20, \"windStrength\": \"MID\", \"relativeHourToStop\": 1}}"}], "model_result_decoded": [{"ThinQ_Connect": {"body": {"airConOperationMode": "POWER_ON", "targetTemperature": 20, "windStrength": "MID", "relativeHourToStop": 1}}}], "possible_answer": [{"ThinQ_Connect": {"body": [{"airConJobMode": ["COOL"], "windStrength": ["MID"], "airConOperationMode": ["POWER_ON"], "targetTemperature": [20], "relativeHourToStop": [1]}]}}]}
{"id": "live_simple_71-35-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'metrics': ['favorability', 'trust']. Expected one of [['view']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "live_simple_71-35-0", "question": [[{"role": "user", "content": "How have millennials views of Apple changed since July 2022?'"}]], "function": [{"name": "extract_parameters_v1", "description": "Extracts parameters for generating a database query to answer a user's question, identifying demographics, target entities, metrics, country, and date range.", "parameters": {"type": "dict", "required": ["targets", "metrics"], "properties": {"demographics": {"type": "array", "items": {"type": "string"}, "description": "Array of strings indicating demographics, geographic, or psychographic characteristics, e.g., 'wealthy', 'women'. If no demographics inferred, return empty array.", "default": []}, "targets": {"type": "array", "items": {"type": "string"}, "description": "Array of target entities and types. Each entry is a string combining 'target' and 'target_type', such as 'brand:Nike' or 'country:USA'."}, "metrics": {"type": "array", "items": {"type": "string"}, "description": "Array of strings for attitudinal metrics about target entities, e.g., 'trust', 'usage frequency'.", "enum": ["favorability", "admired employer", "buzz", "community impact", "purchasing consideration", "trust", "usage frequency", "value", "promoter", "view"]}, "country": {"type": "string", "description": "Country related to the demographics of interest. If unspecified, default is null.", "default": null}, "min_date": {"type": "string", "description": "Start date of the date range in YYYY-MM-DD format. Default is null, representing no specific start date.", "default": null}, "max_date": {"type": "string", "description": "End date of the date range in YYYY-MM-DD format. Default is null, representing no specific end date.", "default": null}, "interval": {"type": "string", "description": "Aggregation interval for data reporting, e.g., 'daily', 'monthly'. If unspecified, default is null.", "enum": ["day", "week", "month", "quarter", "year"], "default": null}}}}]}, "model_result_raw": [{"extract_parameters_v1": "{\"targets\": [\"brand:Apple\"], \"metrics\": [\"favorability\", \"trust\"], \"demographics\": [\"millennials\"], \"min_date\": \"2022-07-01\"}"}], "model_result_decoded": [{"extract_parameters_v1": {"targets": ["brand:Apple"], "metrics": ["favorability", "trust"], "demographics": ["millennials"], "min_date": "2022-07-01"}}], "possible_answer": [{"extract_parameters_v1": {"demographics": [["millennials"]], "targets": [["brand:Apple"]], "metrics": [["view"]], "country": ["", null], "min_date": ["2022-07-01"], "max_date": ["", null], "interval": ["", null]}}]}
{"id": "live_simple_77-38-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'country': 'UK'. Expected one of ['GB']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_77-38-0", "question": [[{"role": "user", "content": "Could you tell me the current weather conditions, including the temperature, wind, and precipitation, for London in the UK?"}]], "function": [{"name": "weather.get", "description": "Get the current weather conditions, including temperature, wind, and precipitation, for a specified city and country.", "parameters": {"type": "dict", "required": ["city", "country"], "properties": {"city": {"type": "string", "description": "The name of the city for which the weather is requested, e.g., 'Los Angeles'."}, "country": {"type": "string", "description": "The country where the city is located, e.g., 'US' for the United States. Use ISO 3166-1 alpha-2 country codes."}, "units": {"type": "string", "description": "The units for temperature measurement.", "enum": ["metric", "imperial"], "default": "metric"}, "include_forecast": {"type": "boolean", "description": "Whether to include a 5-day weather forecast in the response.", "default": false}}}}]}, "model_result_raw": [{"weather_get": "{\"city\": \"London\", \"country\": \"UK\"}"}], "model_result_decoded": [{"weather_get": {"city": "London", "country": "UK"}}], "possible_answer": [{"weather.get": {"city": ["London"], "country": ["GB"], "units": ["", "metric"], "include_forecast": ["", false]}}]}
{"id": "live_simple_80-41-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'Content': 'The latest advancements in machine learning algorithms have revolutionized the field of artificial intelligence. Researchers have developed new techniques that significantly improve model efficiency and accuracy, enabling more complex tasks to be performed with greater precision. These breakthroughs include innovations in neural network architectures, enhanced data processing methods, and improved natural language understanding. As a result, AI systems are now capable of handling real-world problems more effectively than ever before.'. Expected one of ['The recent breakthroughs in machine learning algorithms.']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_80-41-0", "question": [[{"role": "user", "content": "I need to add a news item about the latest advancements in AI. The title should be 'Breakthrough in Artificial Intelligence', and the content must cover the recent breakthroughs in machine learning algorithms. Can you set the meta title as 'AI Breakthrough Latest Developments in Machine Learning' and the meta description to 'An overview of the recent significant advancements in artificial intelligence and machine learning technology'? Also, the URL should be 'ai-breakthrough-latest-developments'."}]], "function": [{"name": "sitefinity_create_contentitem", "description": "Create a new content item in Sitefinity CMS with specified metadata for SEO optimization and a unique URL name. Ensures that content is visible and well-represented in search engine results.", "parameters": {"type": "dict", "required": ["Title", "Content", "MetaTitle", "MetaDescription", "UrlName"], "properties": {"Title": {"type": "string", "description": "The title of the content item. It is displayed prominently on the page and used as the clickable link on search engine results pages."}, "Content": {"type": "string", "description": "The body content of the content item. Contains the main text of the content and can include HTML tags."}, "MetaTitle": {"type": "string", "description": "The HTML meta title for SEO. Should be concise and relevant to the content topic. If not provided, the 'Title' will be used."}, "MetaDescription": {"type": "string", "description": "The HTML meta description for SEO. Provides a brief summary of the content and its relevance. If not provided, a summary derived from the 'Content' will be used."}, "UrlName": {"type": "string", "description": "A human-readable and SEO-friendly URL slug for the content item. It is typically constructed using hyphens to separate words and should be unique."}}}}]}, "model_result_raw": [{"sitefinity_create_contentitem": "{\"Title\": \"Breakthrough in Artificial Intelligence\", \"Content\": \"The latest advancements in machine learning algorithms have revolutionized the field of artificial intelligence. Researchers have developed new techniques that significantly improve model efficiency and accuracy, enabling more complex tasks to be performed with greater precision. These breakthroughs include innovations in neural network architectures, enhanced data processing methods, and improved natural language understanding. As a result, AI systems are now capable of handling real-world problems more effectively than ever before.\", \"MetaTitle\": \"AI Breakthrough Latest Developments in Machine Learning\", \"MetaDescription\": \"An overview of the recent significant advancements in artificial intelligence and machine learning technology\", \"UrlName\": \"ai-breakthrough-latest-developments\"}"}], "model_result_decoded": [{"sitefinity_create_contentitem": {"Title": "Breakthrough in Artificial Intelligence", "Content": "The latest advancements in machine learning algorithms have revolutionized the field of artificial intelligence. Researchers have developed new techniques that significantly improve model efficiency and accuracy, enabling more complex tasks to be performed with greater precision. These breakthroughs include innovations in neural network architectures, enhanced data processing methods, and improved natural language understanding. As a result, AI systems are now capable of handling real-world problems more effectively than ever before.", "MetaTitle": "AI Breakthrough Latest Developments in Machine Learning", "MetaDescription": "An overview of the recent significant advancements in artificial intelligence and machine learning technology", "UrlName": "ai-breakthrough-latest-developments"}}], "possible_answer": [{"sitefinity_create_contentitem": {"Title": ["Breakthrough in Artificial Intelligence"], "Content": ["The recent breakthroughs in machine learning algorithms."], "MetaTitle": ["AI Breakthrough Latest Developments in Machine Learning"], "MetaDescription": ["An overview of the recent significant advancements in artificial intelligence and machine learning technology"], "UrlName": ["ai-breakthrough-latest-developments"]}}]}
{"id": "live_simple_97-57-1", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'unit': 'celsius'. Expected one of ['', 'fahrenheit']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_97-57-1", "question": [[{"role": "user", "content": "Could you tell me the current weather conditions in El Gastor, Andalusia?"}]], "function": [{"name": "get_current_weather", "description": "Retrieve the current weather conditions for a specified location. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)' or 'City, Location', such as 'San Francisco, CA', or 'New York, NY'."}, "unit": {"type": "string", "description": "The temperature unit for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}, "model_result_raw": [{"get_current_weather": "{\"location\": \"El Gastor, Andalusia\", \"unit\": \"celsius\"}"}], "model_result_decoded": [{"get_current_weather": {"location": "El Gastor, Andalusia", "unit": "celsius"}}], "possible_answer": [{"get_current_weather": {"location": ["El Gastor, Andalusia"], "unit": ["", "fahrenheit"]}}]}
{"id": "live_simple_102-61-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Optional parameter 'county' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "live_simple_102-61-0", "question": [[{"role": "user", "content": "I've just made a purchase of $59.99 in San Francisco, California. How much the sales tax will be for this amount?"}]], "function": [{"name": "calculate_tax", "description": "Calculates the applicable sales tax for a given purchase amount and jurisdiction. The function returns the total amount with tax.", "parameters": {"type": "dict", "required": ["purchase_amount", "state"], "properties": {"purchase_amount": {"type": "float", "description": "The total purchase amount in dollars."}, "state": {"type": "string", "description": "The U.S. state abbreviation where the purchase is made, such as 'CA' for California or 'NY' for New York."}, "county": {"type": "string", "description": "The county within the state where the purchase is made. This is optional and used to calculate local tax rates.", "default": null}, "city": {"type": "string", "description": "The city within the county for local tax rates. Optional and only used if the county is provided.", "default": null}, "tax_exempt": {"type": "boolean", "description": "Indicates whether the purchase is tax-exempt.", "default": false}, "discount_rate": {"type": "float", "description": "The discount rate as a percentage if applicable. Optional.", "default": 0.0}, "apply_special_tax": {"type": "boolean", "description": "A flag to apply special tax rules based on item type or promotions. Optional.", "default": false}}}}]}, "model_result_raw": [{"calculate_tax": "{\"purchase_amount\": 59.99, \"state\": \"CA\"}"}], "model_result_decoded": [{"calculate_tax": {"purchase_amount": 59.99, "state": "CA"}}], "possible_answer": [{"calculate_tax": {"purchase_amount": [59.99], "state": ["CA"], "county": ["San Francisco"], "city": ["San Francisco"], "tax_exempt": ["", false], "discount_rate": ["", 0.0], "apply_special_tax": ["", false]}}]}
{"id": "live_simple_106-63-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Missing required parameter: 'auto_loan_payment_start'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "live_simple_106-63-0", "question": [[{"role": "user", "content": "Could you help me classify the following customer queries into the appropriate categories?\n- 'Please provide the routing number for my account.'\n- 'Which account number should I use for setting up eStatements?'\n- 'What rewards are offered for my card?'\n- 'Looking for the closest ATM for cash withdrawal, can you assist me in finding one?'\n- 'What rewards are available on my card?'\n- 'Locate ATM for immediate cash needs'\n- 'I urgently need to find an ATM for cash withdrawal, can you help me locate the nearest one?'\n- 'Activate my new card for loyalty program enrollment'\n- 'I want to cancel my card and ensure the safety of my account.'\n- 'Searching for ATM for money withdrawal'\n- 'Can you walk me through the activation process for my new bank card?'\n- 'What are the benefits of using my rewards points for entertainment?'\n- 'Find ATM for urgent cash needs'\n- 'Need to find ATM for quick cash emergency'\n- 'I'm done here. Goodbye!'\n- 'Please give me the account number for my business checking account.'\n- 'Can I purchase a cashier's check through this chat service?'\n- 'Thanks for your assistance. Goodbye!'\n- 'I need a cashier's check, how can I get one?'\n- 'You're amazing. Goodbye!'"}]], "function": [{"name": "record", "description": "Logs the types of queries users make, classifying them into predefined categories.", "parameters": {"type": "dict", "required": ["acc_routing_start", "activate_card_start", "atm_finder_start", "auto_loan_payment_start", "bank_hours_start", "cancel_card_start", "card_rewards_start", "cashier_check_start", "clean_goodbye_start"], "properties": {"acc_routing_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries requesting the bank routing numbers or account numbers."}, "activate_card_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to activating a new bank card."}, "atm_finder_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries asking for locations of ATMs nearby or in a specific area."}, "auto_loan_payment_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to making payments on auto or car loans."}, "bank_hours_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries asking for the working hours or locations of bank branches."}, "cancel_card_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to cancelling a bank card or asking for guidance on how to cancel a card."}, "card_rewards_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to rewards points or benefits associated with a bank card."}, "cashier_check_start": {"type": "array", "items": {"type": "string"}, "description": "List of requests for cashier's checks, drafts, or similar financial instruments from the bank."}, "clean_goodbye_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries saying goodbye or ending the conversation with the chatbot."}}}}]}, "model_result_raw": [{"record": "{\"acc_routing_start\": [\"Please provide the routing number for my account.\", \"Which account number should I use for setting up eStatements?\", \"Please give me the account number for my business checking account.\"], \"activate_card_start\": [\"Activate my new card for loyalty program enrollment\", \"Can you walk me through the activation process for my new bank card?\"], \"atm_finder_start\": [\"Looking for the closest ATM for cash withdrawal, can you assist me in finding one?\", \"Locate ATM for immediate cash needs\", \"I urgently need to find an ATM for cash withdrawal, can you help me locate the nearest one?\", \"Searching for ATM for money withdrawal\", \"Find ATM for urgent cash needs\", \"Need to find ATM for quick cash emergency\"], \"bank_hours_start\": [], \"cancel_card_start\": [\"I want to cancel my card and ensure the safety of my account.\"], \"card_rewards_start\": [\"What rewards are offered for my card?\", \"What rewards are available on my card?\", \"What are the benefits of using my rewards points for entertainment?\"], \"cashier_check_start\": [\"Can I purchase a cashier's check through this chat service?\", \"I need a cashier's check, how can I get one?\"], \"clean_goodbye_start\": [\"I'm done here. Goodbye!\", \"Thanks for your assistance. Goodbye!\", \"You're amazing. Goodbye!\"]}"}], "model_result_decoded": [{"record": {"acc_routing_start": ["Please provide the routing number for my account.", "Which account number should I use for setting up eStatements?", "Please give me the account number for my business checking account."], "activate_card_start": ["Activate my new card for loyalty program enrollment", "Can you walk me through the activation process for my new bank card?"], "atm_finder_start": ["Looking for the closest ATM for cash withdrawal, can you assist me in finding one?", "Locate ATM for immediate cash needs", "I urgently need to find an ATM for cash withdrawal, can you help me locate the nearest one?", "Searching for ATM for money withdrawal", "Find ATM for urgent cash needs", "Need to find ATM for quick cash emergency"], "bank_hours_start": [], "cancel_card_start": ["I want to cancel my card and ensure the safety of my account."], "card_rewards_start": ["What rewards are offered for my card?", "What rewards are available on my card?", "What are the benefits of using my rewards points for entertainment?"], "cashier_check_start": ["Can I purchase a cashier's check through this chat service?", "I need a cashier's check, how can I get one?"], "clean_goodbye_start": ["I'm done here. Goodbye!", "Thanks for your assistance. Goodbye!", "You're amazing. Goodbye!"]}}], "possible_answer": [{"record": {"acc_routing_start": [["Please provide the routing number for my account.", "Which account number should I use for setting up eStatements?", "Please give me the account number for my business checking account."]], "activate_card_start": [["Activate my new card for loyalty program enrollment", "Can you walk me through the activation process for my new bank card?"]], "atm_finder_start": [["Looking for the closest ATM for cash withdrawal, can you assist me in finding one?", "Locate ATM for immediate cash needs", "I urgently need to find an ATM for cash withdrawal, can you help me locate the nearest one?", "Searching for ATM for money withdrawal", "Find ATM for urgent cash needs", "Need to find ATM for quick cash emergency"]], "auto_loan_payment_start": [], "bank_hours_start": [], "cancel_card_start": [["I want to cancel my card and ensure the safety of my account."]], "card_rewards_start": [["What rewards are offered for my card?", "What rewards are available on my card?", "What are the benefits of using my rewards points for entertainment?"]], "cashier_check_start": [["Can I purchase a cashier's check through this chat service?", "I need a cashier's check, how can I get one?"]], "clean_goodbye_start": [["I'm done here. Goodbye!", "Thanks for your assistance. Goodbye!", "You're amazing. Goodbye!"]]}}]}
{"id": "live_simple_107-64-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'faq_describe_accounts_start': [\"I'm interested in opening a long term savings account. What options do you have?\", 'Do you offer any special accounts for children or minors?', 'Do you offer any special accounts for individuals looking to save for a home purchase or renovation?']. Expected one of [[\"I'm interested in opening a long term savings account. What options do you have?\", 'Do you offer any special accounts for individuals looking to save for a home purchase or renovation?', 'Do you offer any special accounts for children or minors?']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "live_simple_107-64-0", "question": [[{"role": "user", "content": "I have a list of customer queries and need to classify them into categories related to telephone banking services, bank working hours or locations, and descriptions of different types of bank accounts. Here are the queries:\n- 'How do I sign up for tele-banking services through the mobile app?'\n- 'Can I sign up for telephone banking services?'\n- 'Where can I find the application for telephone banking services?'\n- 'I'm interested in opening a long term savings account. What options do you have?'\n- 'Are any banks open in Sri Lanka right now?'\n- 'Do you offer any special accounts for children or minors?'\n- 'Do you offer any special accounts for individuals looking to save for a home purchase or renovation?'"}]], "function": [{"name": "record", "description": "Records the categories to which various banking-related queries are classified.", "parameters": {"type": "dict", "required": ["faq_describe_telephone_banking_start", "bank_hours_start", "faq_describe_accounts_start"], "properties": {"faq_describe_telephone_banking_start": {"type": "array", "items": {"type": "string"}, "description": "An array of strings containing queries about starting or signing up for telephone banking services."}, "bank_hours_start": {"type": "array", "items": {"type": "string"}, "description": "An array of strings containing queries regarding the working hours or locations of bank branches."}, "faq_describe_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "An array of strings containing queries about descriptions of different types of bank accounts."}}}}]}, "model_result_raw": [{"record": "{\"faq_describe_telephone_banking_start\": [\"How do I sign up for tele-banking services through the mobile app?\", \"Can I sign up for telephone banking services?\", \"Where can I find the application for telephone banking services?\"], \"bank_hours_start\": [\"Are any banks open in Sri Lanka right now?\"], \"faq_describe_accounts_start\": [\"I'm interested in opening a long term savings account. What options do you have?\", \"Do you offer any special accounts for children or minors?\", \"Do you offer any special accounts for individuals looking to save for a home purchase or renovation?\"]}"}], "model_result_decoded": [{"record": {"faq_describe_telephone_banking_start": ["How do I sign up for tele-banking services through the mobile app?", "Can I sign up for telephone banking services?", "Where can I find the application for telephone banking services?"], "bank_hours_start": ["Are any banks open in Sri Lanka right now?"], "faq_describe_accounts_start": ["I'm interested in opening a long term savings account. What options do you have?", "Do you offer any special accounts for children or minors?", "Do you offer any special accounts for individuals looking to save for a home purchase or renovation?"]}}], "possible_answer": [{"record": {"faq_describe_telephone_banking_start": [["How do I sign up for tele-banking services through the mobile app?", "Can I sign up for telephone banking services?", "Where can I find the application for telephone banking services?"]], "bank_hours_start": [["Are any banks open in Sri Lanka right now?"]], "faq_describe_accounts_start": [["I'm interested in opening a long term savings account. What options do you have?", "Do you offer any special accounts for individuals looking to save for a home purchase or renovation?", "Do you offer any special accounts for children or minors?"]]}}]}
{"id": "live_simple_108-65-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'money_movement_start': ['Can you assist me in combining my personal and savings accounts?', 'Please assist me in combining my accounts for a streamlined experience.']. Expected one of [['Can you show me all outgoing wire transfers?']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "live_simple_108-65-0", "question": [[{"role": "user", "content": "Please classify the following customer service queries:\n- 'Can you show me all outgoing wire transfers?'\n- 'Can you assist me in combining my personal and savings accounts?'\n- 'I need my credit card limit raised.'\n- 'Can you assist me in getting a higher credit card limit?'\n- 'I need to view all transactions labeled as \"checks\" on my account.'\n- 'Please assist me in combining my accounts for a streamlined experience.'\n- 'Can you show me my recent Google Pay transactions?'\n- 'How can I link my auto loan and savings accounts?'\n- 'I would like to see the details of my most recent ATM withdrawal.'\n- 'Can you give me a summary of my recent debit card transactions?'"}]], "function": [{"name": "record", "description": "Records the classification of specified customer service queries.", "parameters": {"type": "dict", "required": ["money_movement_start", "get_transactions_start", "credit_limit_increase_start", "faq_link_accounts_start"], "properties": {"money_movement_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries for transferring funds between accounts."}, "get_transactions_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries for viewing transactions, including deposits, purchases, and transaction details."}, "credit_limit_increase_start": {"type": "array", "items": {"type": "string"}, "description": "List of customer requests to increase credit card limits or complaints about the current limit."}, "faq_link_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to linking accounts within the bank's system."}}}}]}, "model_result_raw": [{"record": "{\"money_movement_start\": [\"Can you assist me in combining my personal and savings accounts?\", \"Please assist me in combining my accounts for a streamlined experience.\"], \"get_transactions_start\": [\"Can you show me all outgoing wire transfers?\", \"I need to view all transactions labeled as \\\"checks\\\" on my account.\", \"Can you show me my recent Google Pay transactions?\", \"I would like to see the details of my most recent ATM withdrawal.\", \"Can you give me a summary of my recent debit card transactions?\"], \"credit_limit_increase_start\": [\"I need my credit card limit raised.\", \"Can you assist me in getting a higher credit card limit?\"], \"faq_link_accounts_start\": [\"Can you assist me in combining my personal and savings accounts?\", \"How can I link my auto loan and savings accounts?\"]}"}], "model_result_decoded": [{"record": {"money_movement_start": ["Can you assist me in combining my personal and savings accounts?", "Please assist me in combining my accounts for a streamlined experience."], "get_transactions_start": ["Can you show me all outgoing wire transfers?", "I need to view all transactions labeled as \"checks\" on my account.", "Can you show me my recent Google Pay transactions?", "I would like to see the details of my most recent ATM withdrawal.", "Can you give me a summary of my recent debit card transactions?"], "credit_limit_increase_start": ["I need my credit card limit raised.", "Can you assist me in getting a higher credit card limit?"], "faq_link_accounts_start": ["Can you assist me in combining my personal and savings accounts?", "How can I link my auto loan and savings accounts?"]}}], "possible_answer": [{"record": {"money_movement_start": [["Can you show me all outgoing wire transfers?"]], "get_transactions_start": [["I need to view all transactions labeled as \"checks\" on my account.", "Can you show me my recent Google Pay transactions?", "I would like to see the details of my most recent ATM withdrawal.", "Can you give me a summary of my recent debit card transactions?"]], "credit_limit_increase_start": [["I need my credit card limit raised.", "Can you assist me in getting a higher credit card limit?"]], "faq_link_accounts_start": [["Can you assist me in combining my personal and savings accounts?", "Please assist me in combining my accounts for a streamlined experience.", "How can I link my auto loan and savings accounts?"]]}}]}
{"id": "live_simple_112-68-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Missing required parameter: 'acc_routing_start'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "live_simple_112-68-0", "question": [[{"role": "user", "content": "I have a query: 'what is the weather like'. Could you classify it appropriately?"}]], "function": [{"name": "record", "description": "Records classifications for a series of queries based on the intent names provided as keyword arguments. Allows for the bulk recording of query classifications in one function call.", "parameters": {"type": "dict", "required": ["acc_routing_start", "atm_finder_start", "faq_link_accounts_start", "get_balance_start", "get_transactions_start", "outofscope"], "properties": {"acc_routing_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries requesting the bank routing numbers or account numbers."}, "atm_finder_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries asking for locations of ATMs, specified in the format of 'City, State (abbr)', such as 'Berkeley, CA' or 'New York, NY'."}, "faq_link_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries related to linking accounts within the bank's system."}, "get_balance_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries asking for account balances, available funds, or other financial balances."}, "get_transactions_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries related to viewing transactions, deposits, purchases, and transaction details."}, "outofscope": {"type": "array", "items": {"type": "string"}, "description": "A list of queries that do not classify to any other available intents."}}}}]}, "model_result_raw": [{"record": "{\"outofscope\": [\"what is the weather like\"]}"}], "model_result_decoded": [{"record": {"outofscope": ["what is the weather like"]}}], "possible_answer": [{"record": {"acc_routing_start": [], "atm_finder_start": [], "faq_link_accounts_start": [], "get_balance_start": [], "get_transactions_start": [], "outofscope": [["what is the weather like"]]}}]}
{"id": "live_simple_116-72-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers_list'. Expected outer type array with inner type <class 'float'>. Parameter value: [133, 34]."], "error_type": "type_error:nested", "prompt": {"id": "live_simple_116-72-0", "question": [[{"role": "user", "content": "What is the sum of 133 and 34?"}]], "function": [{"name": "sum_numbers", "description": "Calculates the sum of all the numbers provided in the list.", "parameters": {"type": "dict", "required": ["numbers_list"], "properties": {"numbers_list": {"type": "array", "items": {"type": "float"}, "description": "A list of floating-point numbers to be summed."}}}}]}, "model_result_raw": [{"sum_numbers": "{\"numbers_list\": [133, 34]}"}], "model_result_decoded": [{"sum_numbers": {"numbers_list": [133, 34]}}], "possible_answer": [{"sum_numbers": {"numbers_list": [[133.0, 34.0]]}}]}
{"id": "live_simple_128-83-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'url': 'https://*************/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}'. Expected one of ['https://*************/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName=PEK-ACI']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_128-83-0", "question": [[{"role": "user", "content": "How to generate a RESTful API request on Cisco Nexus Dashboard?\nversion:3.1.0\nIP:*************\nAPI name: get nodes list\nParameter_fabricName:PEK-ACI"}]], "function": [{"name": "requests.get", "description": "Sends an HTTP GET request to retrieve a list of nodes associated with a specified fabric in a network.", "parameters": {"type": "dict", "required": ["fabricName"], "properties": {"url": {"type": "string", "description": "The URL to send the GET request to. The URL should include the target IP, API endpoint, and the fabricName query parameter placeholder https://<ip>/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}.", "default": "https://<ip>/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}"}, "fabricName": {"type": "string", "description": "The name of the fabric whose nodes are to be listed. This value is used to replace the {fabricName} placeholder in the URL."}}}}]}, "model_result_raw": [{"requests_get": "{\"url\": \"https://*************/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}\", \"fabricName\": \"PEK-ACI\"}"}], "model_result_decoded": [{"requests_get": {"url": "https://*************/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}", "fabricName": "PEK-ACI"}}], "possible_answer": [{"requests.get": {"url": ["https://*************/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName=PEK-ACI"], "fabricName": ["PEK-ACI"]}}]}
{"id": "live_simple_129-83-1", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'url': 'https://3.321.3232.2/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}'. Expected one of ['https://3.321.3232.2/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName=fab-ed']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_129-83-1", "question": [[{"role": "user", "content": "get list of nodes for fabric fab-ed and ip is 3.321.3232.2"}]], "function": [{"name": "requests.get", "description": "Sends an HTTP GET request to retrieve a list of nodes associated with a specified fabric in a network.", "parameters": {"type": "dict", "required": ["fabricName"], "properties": {"url": {"type": "string", "description": "The URL to send the GET request to. The URL should include the target IP, API endpoint, and the fabricName query parameter placeholder in https://<ip>/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}.", "default": "https://<ip>/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}"}, "fabricName": {"type": "string", "description": "The name of the fabric whose nodes are to be listed. This value is used to replace the {fabricName} placeholder in the URL."}}}}]}, "model_result_raw": [{"requests_get": "{\"url\": \"https://3.321.3232.2/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}\", \"fabricName\": \"fab-ed\"}"}], "model_result_decoded": [{"requests_get": {"url": "https://3.321.3232.2/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}", "fabricName": "fab-ed"}}], "possible_answer": [{"requests.get": {"url": ["https://3.321.3232.2/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName=fab-ed"], "fabricName": ["fab-ed"]}}]}
{"id": "live_simple_152-95-9", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'command': 'dir D:\\\\ && echo testing.txt'. Expected one of ['dir D:\\\\\\\\ && echo testing.txt']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_152-95-9", "question": [[{"role": "user", "content": "list file in d drive and make file called testing.txt. This can be achieved through dir D:\\\\ && echo testing.txt."}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}, "model_result_raw": [{"cmd_controller_execute": "{\"command\": \"dir D:\\\\ && echo testing.txt\"}"}], "model_result_decoded": [{"cmd_controller_execute": {"command": "dir D:\\ && echo testing.txt"}}], "possible_answer": [{"cmd_controller.execute": {"command": ["dir D:\\\\ && echo testing.txt"], "unit": ["", "N/A"]}}]}
{"id": "live_simple_168-99-2", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'command': 'docker start [container_name]'. Expected one of ['docker start']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_168-99-2", "question": [[{"role": "user", "content": "start Docker on window using docker start."}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}, "model_result_raw": [{"cmd_controller_execute": "{\"command\": \"docker start [container_name]\"}"}], "model_result_decoded": [{"cmd_controller_execute": {"command": "docker start [container_name]"}}], "possible_answer": [{"cmd_controller.execute": {"command": ["docker start"]}}]}
{"id": "live_simple_186-111-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'start_available_date': '2024-12-03 16:00:00'. Expected one of ['2024-03-12 16:00:00']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_186-111-0", "question": [[{"role": "user", "content": "Help find a housekeeper who provides ironing services in Chonburi Province. with a review score of 4.5 stars or more, available on 12/03/2024 16:00 - 18:00."}]], "function": [{"name": "get_service_providers", "description": "Retrieves a list of service providers based on the specified criteria including service type, location, and availability.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "The unique identifier of the service. For example, 1 represents cleaning service, 2 represents ironing service, 3 represents big cleaning service."}, "province_id": {"type": "integer", "description": "The unique identifier of the province. For example, 1 for Bangkok, 2 for Chiang Mai, 3 for Chonburi.", "default": null}, "district_name": {"type": "string", "description": "The name of the district. Example: 'Watthana', 'Thon Buri'.", "default": null}, "sub_district_name": {"type": "string", "description": "The name of the sub-district. Example: 'Phra Khanong', 'Khlong Toei'.", "default": null}, "rating": {"type": "float", "description": "The minimum average rating for filtering service providers. Ratings are on a scale from 1.0 to 5.0.", "default": null}, "start_available_date": {"type": "string", "description": "The start of the availability period for service providers in the format of 'YYYY-MM-DD HH:mm:ss'. Default represents the current date and time.", "default": "null (represents the current date and time)"}, "end_available_date": {"type": "string", "description": "The end of the availability period for service providers in the format of 'YYYY-MM-DD HH:mm:ss'. Default represents no specific end date.", "default": "null (represents no specific end date)"}}}}]}, "model_result_raw": [{"get_service_providers": "{\"service_id\": 2, \"province_id\": 3, \"rating\": 4.5, \"start_available_date\": \"2024-12-03 16:00:00\", \"end_available_date\": \"2024-12-03 18:00:00\"}"}], "model_result_decoded": [{"get_service_providers": {"service_id": 2, "province_id": 3, "rating": 4.5, "start_available_date": "2024-12-03 16:00:00", "end_available_date": "2024-12-03 18:00:00"}}], "possible_answer": [{"get_service_providers": {"service_id": [2], "province_id": [3], "district_name": ["", null], "sub_district_name": ["", null], "rating": [4.5], "start_available_date": ["2024-03-12 16:00:00"], "end_available_date": ["2024-03-12 18:00:00"]}}]}
{"id": "live_simple_199-116-7", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'city': 'Marshall, MN'. Expected one of ['Marshall']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_199-116-7", "question": [[{"role": "user", "content": "Could you tell me the weather forecast for Marshall, MN on March 5th 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}, "model_result_raw": [{"Weather_1_GetWeather": "{\"city\": \"Marshall, MN\", \"date\": \"2023-03-05\"}"}], "model_result_decoded": [{"Weather_1_GetWeather": {"city": "Marshall, MN", "date": "2023-03-05"}}], "possible_answer": [{"Weather_1_GetWeather": {"city": ["Marshall"], "date": ["2023-03-05"]}}]}
{"id": "live_simple_203-116-11", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'city': 'Washington, DC'. Expected one of ['Washington']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_203-116-11", "question": [[{"role": "user", "content": "Hi, could you help me check the weather in Washington, DC for March 1st, 2023 please?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}, "model_result_raw": [{"Weather_1_GetWeather": "{\"city\": \"Washington, DC\", \"date\": \"2023-03-01\"}"}], "model_result_decoded": [{"Weather_1_GetWeather": {"city": "Washington, DC", "date": "2023-03-01"}}], "possible_answer": [{"Weather_1_GetWeather": {"city": ["Washington"], "date": ["2023-03-01"]}}]}
{"id": "live_simple_204-116-12", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'city': 'Rutherford, NJ'. Expected one of ['Rutherford']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_204-116-12", "question": [[{"role": "user", "content": "Could you tell me what the weather will be like in Rutherford, NJ, on April 22nd 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}, "model_result_raw": [{"Weather_1_GetWeather": "{\"city\": \"Rutherford, NJ\", \"date\": \"2023-04-22\"}"}], "model_result_decoded": [{"Weather_1_GetWeather": {"city": "Rutherford, NJ", "date": "2023-04-22"}}], "possible_answer": [{"Weather_1_GetWeather": {"city": ["Rutherford"], "date": ["2023-04-22"]}}]}
{"id": "live_simple_205-116-13", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'date': '2023-04-29'. Expected one of ['2023-04-27']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_205-116-13", "question": [[{"role": "user", "content": "What's the weather forecast for Berkeley on the upcoming Saturday? Today is Tuesday April 25th 2023"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}, "model_result_raw": [{"Weather_1_GetWeather": "{\"city\": \"Berkeley\", \"date\": \"2023-04-29\"}"}], "model_result_decoded": [{"Weather_1_GetWeather": {"city": "Berkeley", "date": "2023-04-29"}}], "possible_answer": [{"Weather_1_GetWeather": {"city": ["Berkeley"], "date": ["2023-04-27"]}}]}
{"id": "live_simple_206-116-14", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'city': 'London, England'. Expected one of ['London']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_206-116-14", "question": [[{"role": "user", "content": "Hi, could you tell me the forecast for London, England on the 5th of march 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}, "model_result_raw": [{"Weather_1_GetWeather": "{\"city\": \"London, England\", \"date\": \"2023-03-05\"}"}], "model_result_decoded": [{"Weather_1_GetWeather": {"city": "London, England", "date": "2023-03-05"}}], "possible_answer": [{"Weather_1_GetWeather": {"city": ["London"], "date": ["2023-03-05"]}}]}
{"id": "live_simple_231-122-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'new_datetime': '2022-10-27T16:30:00Z'. Expected one of ['2022-10-30T16:30:00Z']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "live_simple_231-122-0", "question": [[{"role": "user", "content": "Can you move my next gym session, which is event number 456123, to next Thursday at 4:30 PM? Today is monday 2022-10-20"}]], "function": [{"name": "reschedule_event", "description": "Reschedule an event to a new date or time, specified in ISO-8601 format.", "parameters": {"type": "dict", "required": ["event_identifier", "new_datetime"], "properties": {"event_identifier": {"type": "string", "description": "The unique identifier of the event to be rescheduled."}, "new_datetime": {"type": "string", "description": "The new date and time for the event in ISO-8601 format, such as '2023-04-15T13:45:00Z'."}}}}]}, "model_result_raw": [{"reschedule_event": "{\"event_identifier\": \"456123\", \"new_datetime\": \"2022-10-27T16:30:00Z\"}"}], "model_result_decoded": [{"reschedule_event": {"event_identifier": "456123", "new_datetime": "2022-10-27T16:30:00Z"}}], "possible_answer": [{"reschedule_event": {"event_identifier": ["456123"], "new_datetime": ["2022-10-30T16:30:00Z"]}}]}
{"id": "live_simple_240-125-3", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'play': False. Expected one of ['', True]."], "error_type": "value_error:others", "prompt": {"id": "live_simple_240-125-3", "question": [[{"role": "user", "content": "Can you search for 'Baby Shark'?"}]], "function": [{"name": "play_spotify_song", "description": "This function searches for a song on Spotify using a specified query and plays it.", "parameters": {"type": "dict", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query for the song. It should be formatted as 'track:Song Name artist:Artist Name' for specific searches, or just 'Song Name' if the artist is unknown."}, "device_id": {"type": "string", "description": "The unique identifier for the device on which the song should be played.", "default": "null"}, "play": {"type": "boolean", "description": "A flag that indicates whether to start playing the song immediately after it is found. Defaults to true.", "default": true}}}}]}, "model_result_raw": [{"play_spotify_song": "{\"query\": \"Baby Shark\", \"play\": false}"}], "model_result_decoded": [{"play_spotify_song": {"query": "Baby Shark", "play": false}}], "possible_answer": [{"play_spotify_song": {"query": ["Baby Shark"], "device_id": ["", "null"], "play": ["", true]}}]}
{"id": "live_simple_253-135-0", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "live_simple", "valid": false, "error": ["Invalid value for parameter 'items': ['burger', 'chicken wings']. Expected one of [['burgers', 'chicken wings'], ['burger', 'chicken wing']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "live_simple_253-135-0", "question": [[{"role": "user", "content": "I want to order five burgers and six chicken wings from McDonald's."}]], "function": [{"name": "uber.eat.order", "description": "Place an order for food from selected restaurants with specified items and their quantities.", "parameters": {"type": "dict", "required": ["restaurants", "items", "quantities"], "properties": {"restaurants": {"type": "string", "description": "The name of the restaurant from which the user wants to order food. Use the exact name as registered on Uber Eats."}, "items": {"type": "array", "items": {"type": "string"}, "description": "A list of item names that the user wants to order from the restaurant."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "A list of integers, each representing the quantity of the corresponding item in the order. The quantities should align with the items list by index."}}}}]}, "model_result_raw": [{"uber_eat_order": "{\"restaurants\": \"McDonald's\", \"items\": [\"burger\", \"chicken wings\"], \"quantities\": [5, 6]}"}], "model_result_decoded": [{"uber_eat_order": {"restaurants": "McDonald's", "items": ["burger", "chicken wings"], "quantities": [5, 6]}}], "possible_answer": [{"uber.eat.order": {"restaurants": ["McDonald's", "McDonalds"], "items": [["burgers", "chicken wings"], ["burger", "chicken wing"]], "quantities": [[5, 6]]}}]}