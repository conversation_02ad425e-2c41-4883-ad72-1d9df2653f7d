{"accuracy": 0.9625, "correct_count": 385, "total_count": 400}
{"id": "simple_5", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'root_type': 'real'. Expected one of ['all']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_5", "question": [[{"role": "user", "content": "Find all the roots of a quadratic equation given coefficients a = 3, b = -11, and c = -4."}]], "function": [{"name": "solve_quadratic", "description": "Solve a quadratic equation given coefficients a, b, and c. If optional 'root_type' is 'real', the function will only return real roots. If not specified, function may return complex roots.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "The coefficient of the squared term in the quadratic equation."}, "b": {"type": "integer", "description": "The coefficient of the linear term in the quadratic equation."}, "c": {"type": "integer", "description": "The constant term in the quadratic equation."}, "root_type": {"type": "string", "description": "The type of roots to return: 'real' for real roots, 'all' for both real and complex roots. Default value is 'real'."}}, "required": ["a", "b", "c"]}}]}, "model_result_raw": [{"solve_quadratic": "{\"a\": 3, \"b\": -11, \"c\": -4, \"root_type\": \"real\"}"}], "model_result_decoded": [{"solve_quadratic": {"a": 3, "b": -11, "c": -4, "root_type": "real"}}], "possible_answer": [{"solve_quadratic": {"a": [3], "b": [-11], "c": [-4], "root_type": ["all"]}}]}
{"id": "simple_13", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": [{"calculate_area_under_curve": "{\"function\": \"x**2\", \"interval\": [1, 3]}"}], "model_result_decoded": [{"calculate_area_under_curve": {"function": "x**2", "interval": [1, 3]}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_35", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'operating_hours': 11. Expected one of [23]."], "error_type": "value_error:others", "prompt": {"id": "simple_35", "question": [[{"role": "user", "content": "Find an all vegan restaurant in New York that opens until at least 11 PM."}]], "function": [{"name": "vegan_restaurant.find_nearby", "description": "Locate nearby vegan restaurants based on specific criteria like operating hours.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York, NY, you should format it as City, State."}, "operating_hours": {"type": "integer", "description": "Preferred latest closing time of the restaurant. E.g. if 11 is given, then restaurants that close at or after 11 PM will be considered. This is in 24 hour format. Default is 24."}}, "required": ["location"]}}]}, "model_result_raw": [{"vegan_restaurant_find_nearby": "{\"location\": \"New York, NY\", \"operating_hours\": 11}"}], "model_result_decoded": [{"vegan_restaurant_find_nearby": {"location": "New York, NY", "operating_hours": 11}}], "possible_answer": [{"vegan_restaurant.find_nearby": {"location": ["New York, NY"], "operating_hours": [23]}}]}
{"id": "simple_55", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'cell_type': 'human cell'. Expected one of ['human']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_55", "question": [[{"role": "user", "content": "Find me detailed information about the structure of human cell"}]], "function": [{"name": "biology.get_cell_info", "description": "Retrieve information about the structure and functioning of a specified type of cell", "parameters": {"type": "dict", "properties": {"cell_type": {"type": "string", "description": "Type of cell you want information about"}, "detailed": {"type": "boolean", "description": "Indicate if you want a detailed description of the cell", "default": "false"}}, "required": ["cell_type"]}}]}, "model_result_raw": [{"biology_get_cell_info": "{\"cell_type\": \"human cell\", \"detailed\": true}"}], "model_result_decoded": [{"biology_get_cell_info": {"cell_type": "human cell", "detailed": true}}], "possible_answer": [{"biology.get_cell_info": {"cell_type": ["human"], "detailed": [true]}}]}
{"id": "simple_82", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers'. Expected outer type array with inner type <class 'float'>. Parameter value: [12, 15, 18, 20, 21, 26, 30]."], "error_type": "type_error:nested", "prompt": {"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}, "model_result_raw": [{"calculate_average": "{\"numbers\": [12, 15, 18, 20, 21, 26, 30]}"}], "model_result_decoded": [{"calculate_average": {"numbers": [12, 15, 18, 20, 21, 26, 30]}}], "possible_answer": [{"calculate_average": {"numbers": [[12.0, 15.0, 18.0, 20.0, 21.0, 26.0, 30.0]]}}]}
{"id": "simple_87", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'list'. Expected outer type array with inner type <class 'float'>. Parameter value: [5, 3, 4, 1, 2]."], "error_type": "type_error:nested", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": [{"array_sort": "{\"list\": [5, 3, 4, 1, 2], \"order\": \"ascending\"}"}], "model_result_decoded": [{"array_sort": {"list": [5, 3, 4, 1, 2], "order": "ascending"}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_103", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'polynomial'. Expected outer type array with inner type <class 'float'>. Parameter value: [3, 2, -4]."], "error_type": "type_error:nested", "prompt": {"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}, "model_result_raw": [{"mathematics_calculate_area_under_curve": "{\"polynomial\": [3, 2, -4], \"limits\": [-1, 2]}"}], "model_result_decoded": [{"mathematics_calculate_area_under_curve": {"polynomial": [3, 2, -4], "limits": [-1, 2]}}], "possible_answer": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3.0, 2.0, -4.0]], "limits": [[-1.0, 2.0]]}}]}
{"id": "simple_139", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'yearly_yield': 0.05. Expected one of [5.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_139", "question": [[{"role": "user", "content": "What is the estimated return on a mutual fund, given that it has a yearly yield of 5%, an investment amount of $2000 and a time period of 3 years?"}]], "function": [{"name": "estimate_mutual_fund_return", "description": "Calculate the estimated return on a mutual fund given the yearly yield, the investment amount and the time period.", "parameters": {"type": "dict", "properties": {"yearly_yield": {"type": "float", "description": "The yearly yield of the mutual fund as a percentage."}, "investment_amount": {"type": "integer", "description": "The initial investment amount in the mutual fund."}, "years": {"type": "integer", "description": "The time period for which the investment is made in years."}}, "required": ["yearly_yield", "investment_amount", "years"]}}]}, "model_result_raw": [{"estimate_mutual_fund_return": "{\"yearly_yield\": 0.05, \"investment_amount\": 2000, \"years\": 3}"}], "model_result_decoded": [{"estimate_mutual_fund_return": {"yearly_yield": 0.05, "investment_amount": 2000, "years": 3}}], "possible_answer": [{"estimate_mutual_fund_return": {"yearly_yield": [5.0], "investment_amount": [2000], "years": [3]}}]}
{"id": "simple_197", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'time': '12:00 PM'. Expected one of ['12pm', '12:00']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_197", "question": [[{"role": "user", "content": "Find the air quality index in San Diego at 12pm."}]], "function": [{"name": "get_air_quality_index", "description": "Retrieve the air quality index at a specified location and time.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location to get the air quality index for."}, "time": {"type": "string", "description": "The specific time to check the air quality. Default is the current time."}}, "required": ["location", "time"]}}]}, "model_result_raw": [{"get_air_quality_index": "{\"location\": \"San Diego\", \"time\": \"12:00 PM\"}"}], "model_result_decoded": [{"get_air_quality_index": {"location": "San Diego", "time": "12:00 PM"}}], "possible_answer": [{"get_air_quality_index": {"location": ["San Diego"], "time": ["12pm", "12:00"]}}]}
{"id": "simple_202", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_202", "question": [[{"role": "user", "content": "How many greenhouse gas emissions would I save if I switched to renewable energy sources for 3 months in California?"}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'Texas'."}}, "required": ["energy_type", "usage_duration"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"calculate_emission_savings": {"energy_type": ["renewable"], "usage_duration": [3], "region": ["California", "CA"]}}]}
{"id": "simple_234", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_234", "question": [[{"role": "user", "content": "Find important Wars in European history during the 19th century."}]], "function": [{"name": "history_eu.fetch_events", "description": "Fetches significant historical events within a specific time period in European history.", "parameters": {"type": "dict", "properties": {"century": {"type": "integer", "description": "The century you are interested in."}, "region": {"type": "string", "description": "The region of Europe you are interested in.", "enum": ["Northern", "Southern", "Eastern", "Western"]}, "category": {"type": "string", "description": "Category of the historical events. Default is 'Culture'.", "enum": ["Wars", "Culture", "Politics", "Scientific", "Others"]}}, "required": ["century", "region"]}}]}, "model_result_raw": [{"history_eu_fetch_events": "{\"century\": 19, \"region\": \"Northern\", \"category\": \"Wars\"}"}, {"history_eu_fetch_events": "{\"century\": 19, \"region\": \"Southern\", \"category\": \"Wars\"}"}, {"history_eu_fetch_events": "{\"century\": 19, \"region\": \"Eastern\", \"category\": \"Wars\"}"}, {"history_eu_fetch_events": "{\"century\": 19, \"region\": \"Western\", \"category\": \"Wars\"}"}], "model_result_decoded": [{"history_eu_fetch_events": {"century": 19, "region": "Northern", "category": "Wars"}}, {"history_eu_fetch_events": {"century": 19, "region": "Southern", "category": "Wars"}}, {"history_eu_fetch_events": {"century": 19, "region": "Eastern", "category": "Wars"}}, {"history_eu_fetch_events": {"century": 19, "region": "Western", "category": "Wars"}}], "possible_answer": [{"history_eu.fetch_events": {"century": [19], "region": ["Northern", "Southern", "Eastern", "Western"], "category": ["Wars"]}}]}
{"id": "simple_244", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'work_title': 'Philosophi\u00e6 Naturalis Principia Mathematica'. Expected one of ['Law of Universal Gravitation', 'Universal Law of Gravitation', 'The law of universal gravitation']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_244", "question": [[{"role": "user", "content": "What year was the law of universal gravitation published by Isaac Newton?"}]], "function": [{"name": "publication_year.find", "description": "Fetches the year a particular scientific work was published.", "parameters": {"type": "dict", "properties": {"author": {"type": "string", "description": "Name of the author of the work."}, "work_title": {"type": "string", "description": "Title of the scientific work."}, "location": {"type": "string", "description": "Place of the publication, if known. Default to 'all'."}}, "required": ["author", "work_title"]}}]}, "model_result_raw": [{"publication_year_find": "{\"author\": \"Isaac Newton\", \"work_title\": \"Philosophi\u00e6 Naturalis Principia Mathematica\"}"}], "model_result_decoded": [{"publication_year_find": {"author": "Isaac Newton", "work_title": "Philosophi\u00e6 Naturalis Principia Mathematica"}}], "possible_answer": [{"publication_year.find": {"author": ["Isaac Newton"], "work_title": ["Law of Universal Gravitation", "Universal Law of Gravitation", "The law of universal gravitation"], "location": ["", "all"]}}]}
{"id": "simple_370", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'quantity': [3, 5]. Expected one of [[3, 1]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": [{"safeway_order": "{\"location\": \"Palo Alto, CA\", \"items\": [\"olive oil\", \"rice\"], \"quantity\": [3, 5]}"}], "model_result_decoded": [{"safeway_order": {"location": "Palo Alto, CA", "items": ["olive oil", "rice"], "quantity": [3, 5]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_373", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'pack_size'. Expected outer type array with inner type <class 'int'>. Parameter value: [None, None, 12]."], "error_type": "type_error:nested", "prompt": {"id": "simple_373", "question": [[{"role": "user", "content": "I want to buy apples, rice, and 12 pack of bottled water from a Walmart near San Jose. Show me the product information and stock availability."}]], "function": [{"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is not use it if not specified."}}, "required": ["loc", "product_list"]}}]}, "model_result_raw": [{"walmart_purchase": "{\"loc\": \"San Jose\", \"product_list\": [\"apples\", \"rice\", \"bottled water\"], \"pack_size\": [null, null, 12]}"}], "model_result_decoded": [{"walmart_purchase": {"loc": "San Jose", "product_list": ["apples", "rice", "bottled water"], "pack_size": [null, null, 12]}}], "possible_answer": [{"walmart.purchase": {"loc": ["San Jose", "San Jose, CA"], "product_list": [["apples", "rice", "bottled water"], ["apples", "rice", "water"]], "pack_size": [[1, 1, 12]]}}]}
{"id": "simple_382", "model_name": "Qwen_Qwen3-30B-A3B-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'start_date': '10th December 2022'. Expected one of ['2022-12-10', '10/12/2022', 'Dec 10, 2022', 'December 10, 2022']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_382", "question": [[{"role": "user", "content": "Book a single room for two nights at the Hilton Hotel in Chicago, starting from 10th December 2022."}]], "function": [{"name": "book_hotel", "description": "Book a room of specified type for a particular number of nights at a specific hotel, starting from a specified date.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city in which the hotel is located."}, "room_type": {"type": "string", "description": "The type of room to be booked."}, "start_date": {"type": "string", "description": "The start date for the booking."}, "nights": {"type": "integer", "description": "The number of nights for which the booking is to be made."}}, "required": ["hotel_name", "location", "room_type", "start_date", "nights"]}}]}, "model_result_raw": [{"book_hotel": "{\"hotel_name\": \"Hilton Hotel\", \"location\": \"Chicago\", \"room_type\": \"single\", \"start_date\": \"10th December 2022\", \"nights\": 2}"}], "model_result_decoded": [{"book_hotel": {"hotel_name": "Hilton Hotel", "location": "Chicago", "room_type": "single", "start_date": "10th December 2022", "nights": 2}}], "possible_answer": [{"book_hotel": {"hotel_name": ["Hilton Hotel", "Hilton"], "location": ["Chicago"], "room_type": ["single"], "start_date": ["2022-12-10", "10/12/2022", "Dec 10, 2022", "December 10, 2022"], "nights": [2]}}]}