{"accuracy": 0.905, "correct_count": 362, "total_count": 400}
{"id": "simple_3", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_3", "question": [[{"role": "user", "content": "Find the roots of a quadratic equation with coefficients a=1, b=-3, c=2."}]], "function": [{"name": "algebra.quadratic_roots", "description": "Find the roots of a quadratic equation ax^2 + bx + c = 0.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of x^2."}, "b": {"type": "integer", "description": "Coefficient of x."}, "c": {"type": "integer", "description": "Constant term."}}, "required": ["a", "b", "c"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"algebra.quadratic_roots": {"a": [1], "b": [-3], "c": [2]}}]}
{"id": "simple_5", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_5", "question": [[{"role": "user", "content": "Find all the roots of a quadratic equation given coefficients a = 3, b = -11, and c = -4."}]], "function": [{"name": "solve_quadratic", "description": "Solve a quadratic equation given coefficients a, b, and c. If optional 'root_type' is 'real', the function will only return real roots. If not specified, function may return complex roots.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "The coefficient of the squared term in the quadratic equation."}, "b": {"type": "integer", "description": "The coefficient of the linear term in the quadratic equation."}, "c": {"type": "integer", "description": "The constant term in the quadratic equation."}, "root_type": {"type": "string", "description": "The type of roots to return: 'real' for real roots, 'all' for both real and complex roots. Default value is 'real'."}}, "required": ["a", "b", "c"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"solve_quadratic": {"a": [3], "b": [-11], "c": [-4], "root_type": ["all"]}}]}
{"id": "simple_13", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": [{"calculate_area_under_curve": "{\"function\": \"lambda x: x**2\", \"interval\": [1, 3], \"method\": \"trapezoidal\"}"}], "model_result_decoded": [{"calculate_area_under_curve": {"function": "lambda x: x**2", "interval": [1, 3], "method": "trapezoidal"}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_15", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_15", "question": [[{"role": "user", "content": "Calculate the area under the curve from x = -2 to x = 3 for the function y = x^3 using simpson method."}]], "function": [{"name": "integrate", "description": "Calculate the area under a curve for a specified function between two x values.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The function to integrate, represented as a string. For example, 'x^3'"}, "start_x": {"type": "integer", "description": "The starting x-value to integrate over."}, "end_x": {"type": "integer", "description": "The ending x-value to integrate over."}, "method": {"type": "string", "description": "The method of numerical integration to use. Choices are 'trapezoid' or 'simpson'. Default is 'trapezoid'."}}, "required": ["function", "start_x", "end_x"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"integrate": {"function": ["x**3", "lambda x: x**3"], "start_x": [-2], "end_x": [3], "method": ["simpson"]}}]}
{"id": "simple_17", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_17", "question": [[{"role": "user", "content": "Find the prime factors of 450"}]], "function": [{"name": "get_prime_factors", "description": "Function to retrieve prime factors of a number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "Number for which prime factors need to be calculated"}, "formatted": {"type": "boolean", "description": "Return formatted string if true, array if false. Default is true."}}, "required": ["number", "formatted"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"get_prime_factors": {"number": [450], "formatted": [true, ""]}}]}
{"id": "simple_29", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_29", "question": [[{"role": "user", "content": "What is the final speed of an object dropped from rest after falling for 5 seconds if we neglect air resistance?"}]], "function": [{"name": "calculate_final_speed", "description": "Calculate the final speed of an object in free fall after a certain time, neglecting air resistance. The acceleration due to gravity is considered as -9.81 m/s^2", "parameters": {"type": "dict", "properties": {"initial_speed": {"type": "integer", "description": "The initial speed of the object in m/s. Default is 0 for an object at rest."}, "time": {"type": "integer", "description": "The time in seconds for which the object is in free fall."}, "gravity": {"type": "float", "description": "The acceleration due to gravity. Default is -9.81 m/s^2."}}, "required": ["time"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"calculate_final_speed": {"initial_speed": [0, ""], "time": [5], "gravity": [-9.81, ""]}}]}
{"id": "simple_40", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'permeability': 1.257e-06. Expected one of ['', 125700000000.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_40", "question": [[{"role": "user", "content": "Calculate the magnetic field produced at the center of a circular loop carrying current of 5 Ampere with a radius of 4 meters"}]], "function": [{"name": "calculate_magnetic_field", "description": "Calculate the magnetic field produced at the center of a circular loop carrying current.", "parameters": {"type": "dict", "properties": {"current": {"type": "integer", "description": "The current through the circular loop in Amperes."}, "radius": {"type": "integer", "description": "The radius of the circular loop in meters."}, "permeability": {"type": "float", "description": "The magnetic permeability. Default is 12.57e10 (Vacuum Permeability)."}}, "required": ["current", "radius"]}}]}, "model_result_raw": [{"calculate_magnetic_field": "{\"current\": 5, \"radius\": 4, \"permeability\": 1.257e-06}"}], "model_result_decoded": [{"calculate_magnetic_field": {"current": 5, "radius": 4, "permeability": 1.257e-06}}], "possible_answer": [{"calculate_magnetic_field": {"current": [5], "radius": [4], "permeability": ["", 125700000000.0]}}]}
{"id": "simple_55", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_55", "question": [[{"role": "user", "content": "Find me detailed information about the structure of human cell"}]], "function": [{"name": "biology.get_cell_info", "description": "Retrieve information about the structure and functioning of a specified type of cell", "parameters": {"type": "dict", "properties": {"cell_type": {"type": "string", "description": "Type of cell you want information about"}, "detailed": {"type": "boolean", "description": "Indicate if you want a detailed description of the cell", "default": "false"}}, "required": ["cell_type"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"biology.get_cell_info": {"cell_type": ["human"], "detailed": [true]}}]}
{"id": "simple_82", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers'. Expected outer type array with inner type <class 'float'>. Parameter value: [12, 15, 18, 20, 21, 26, 30]."], "error_type": "type_error:nested", "prompt": {"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}, "model_result_raw": [{"calculate_average": "{\"numbers\": [12, 15, 18, 20, 21, 26, 30]}"}], "model_result_decoded": [{"calculate_average": {"numbers": [12, 15, 18, 20, 21, 26, 30]}}], "possible_answer": [{"calculate_average": {"numbers": [[12.0, 15.0, 18.0, 20.0, 21.0, 26.0, 30.0]]}}]}
{"id": "simple_83", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_83", "question": [[{"role": "user", "content": "Calculate the distance between two GPS coordinates (33.4484 N, 112.0740 W) and (34.0522 N, 118.2437 W) in miles."}]], "function": [{"name": "calculate_distance", "description": "Calculate the distance between two GPS coordinates.", "parameters": {"type": "dict", "properties": {"coord1": {"type": "tuple", "description": "The first coordinate as (latitude, longitude).", "items": {"type": "float"}}, "coord2": {"type": "tuple", "description": "The second coordinate as (latitude, longitude).", "items": {"type": "float"}}, "unit": {"type": "string", "description": "The unit of distance. Options: 'miles', 'kilometers'."}}, "required": ["coord1", "coord2", "unit"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"calculate_distance": {"coord1": [[33.4484, -112.074]], "coord2": [[34.0522, -118.2437]], "unit": ["miles"]}}]}
{"id": "simple_87", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'list'. Expected outer type array with inner type <class 'float'>. Parameter value: [5, 3, 4, 1, 2]."], "error_type": "type_error:nested", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": [{"array_sort": "{\"list\": [5, 3, 4, 1, 2], \"order\": \"ascending\"}"}], "model_result_decoded": [{"array_sort": {"list": [5, 3, 4, 1, 2], "order": "ascending"}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_103", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3.0, 2.0, -4.0]], "limits": [[-1.0, 2.0]]}}]}
{"id": "simple_109", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_109", "question": [[{"role": "user", "content": "Generate a random forest model with 100 trees and a depth of 5 on the provided data my_data."}]], "function": [{"name": "random_forest.train", "description": "Train a Random Forest Model on given data", "parameters": {"type": "dict", "properties": {"n_estimators": {"type": "integer", "description": "The number of trees in the forest."}, "max_depth": {"type": "integer", "description": "The maximum depth of the tree."}, "data": {"type": "any", "description": "The training data for the model."}}, "required": ["n_estimators", "max_depth", "data"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"random_forest.train": {"n_estimators": [100], "max_depth": [5], "data": ["my_data"]}}]}
{"id": "simple_122", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_122", "question": [[{"role": "user", "content": "Perform a Chi-Squared test for independence on a 2x2 contingency table [ [10, 20], [30, 40] ]"}]], "function": [{"name": "chi_squared_test", "description": "Performs a Chi-Squared test for independence on a 2x2 contingency table.", "parameters": {"type": "dict", "properties": {"table": {"type": "array", "items": {"type": "array", "items": {"type": "integer"}}, "description": "A 2x2 contingency table presented in array form."}, "alpha": {"type": "float", "description": "Significance level for the Chi-Squared test. Default is 0.05."}}, "required": ["table"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"chi_squared_test": {"table": [[[10, 20], [30, 40]]], "alpha": [0.05, ""]}}]}
{"id": "simple_127", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_127", "question": [[{"role": "user", "content": "Find the Net Present Value (NPV) of an investment, given cash_flows=[200,300,400,500], a discount rate of 10%, and an initial investment of $2000."}]], "function": [{"name": "calculate_NPV", "description": "Calculate the NPV (Net Present Value) of an investment, considering a series of future cash flows, discount rate, and an initial investment.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "integer"}, "description": "Series of future cash flows."}, "discount_rate": {"type": "float", "description": "The discount rate to use."}, "initial_investment": {"type": "integer", "description": "The initial investment. Default is 0 if not specified."}}, "required": ["cash_flows", "discount_rate"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"calculate_NPV": {"cash_flows": [[200, 300, 400, 500]], "discount_rate": [0.1], "initial_investment": [2000]}}]}
{"id": "simple_130", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_130", "question": [[{"role": "user", "content": "What's the NPV (Net Present Value) of a series of cash flows: [-50000, 10000, 15000, 20000, 25000, 30000] discounted at 8% annually?"}]], "function": [{"name": "finance_calculator.npv", "description": "Calculate the Net Present Value (NPV) for a series of cash flows discounted at a certain interest rate.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "integer"}, "description": "A list of cash flows."}, "discount_rate": {"type": "float", "description": "The annual interest rate used to discount the cash flows."}, "years": {"type": "array", "items": {"type": "integer"}, "description": "A list of years when the cash flow occurs. Default is empty array."}}, "required": ["cash_flows", "discount_rate"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"finance_calculator.npv": {"cash_flows": [[-50000, 10000, 15000, 20000, 25000, 30000]], "discount_rate": [0.08], "years": ["", []]}}]}
{"id": "simple_136", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'annual_rate': 0.05. Expected one of [5.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_136", "question": [[{"role": "user", "content": "Find the compound interest for an investment of $10000 with an annual interest rate of 5% compounded monthly for 5 years."}]], "function": [{"name": "compound_interest", "description": "Calculate compound interest for a certain time period.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The initial amount of money that was invested or loaned out."}, "annual_rate": {"type": "float", "description": "The interest rate for a year as a percentage."}, "compounding_freq": {"type": "string", "enum": ["monthly", "quarterly", "annually"], "description": "The number of times that interest is compounded per unit period."}, "time_in_years": {"type": "integer", "description": "The time the money is invested for in years."}}, "required": ["principal", "annual_rate", "compounding_freq", "time_in_years"]}}]}, "model_result_raw": [{"compound_interest": "{\"principal\": 10000, \"annual_rate\": 0.05, \"compounding_freq\": \"monthly\", \"time_in_years\": 5}"}], "model_result_decoded": [{"compound_interest": {"principal": 10000, "annual_rate": 0.05, "compounding_freq": "monthly", "time_in_years": 5}}], "possible_answer": [{"compound_interest": {"principal": [10000], "annual_rate": [5.0], "compounding_freq": ["monthly"], "time_in_years": [5]}}]}
{"id": "simple_144", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_144", "question": [[{"role": "user", "content": "Find the market performance of the S&P 500 and the Dow Jones over the past 5 days."}]], "function": [{"name": "market_performance.get_data", "description": "Retrieve the market performance data for specified indexes over a specified time period.", "parameters": {"type": "dict", "properties": {"indexes": {"type": "array", "items": {"type": "string"}, "description": "Array of stock market indexes. Supported indexes are 'S&P 500', 'Dow Jones', 'NASDAQ', 'FTSE 100', 'DAX' etc."}, "days": {"type": "integer", "description": "Number of days in the past for which the performance data is required."}, "detailed": {"type": "boolean", "description": "Whether to return detailed performance data. If set to true, returns high, low, opening, and closing prices. If false, returns only closing prices. Default is false."}}, "required": ["indexes", "days"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"market_performance.get_data": {"indexes": [["S&P 500", "Dow Jones"]], "days": [5], "detailed": ["", true, false]}}]}
{"id": "simple_156", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_156", "question": [[{"role": "user", "content": "Look up details of a felony crime record for case number CA123456 in San Diego County"}]], "function": [{"name": "crime_record.get_record", "description": "Retrieve detailed felony crime records using a specific case number and location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The case number related to the crime."}, "county": {"type": "string", "description": "The county in which the crime occurred."}, "details": {"type": "boolean", "description": "To get a detailed report, set as true. Defaults to false."}}, "required": ["case_number", "county"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"crime_record.get_record": {"case_number": ["CA123456"], "county": ["San Diego", "San Diego County"], "details": [true]}}]}
{"id": "simple_158", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_158", "question": [[{"role": "user", "content": "Find the information of criminal cases of Mr. X in New York between 2012 and 2015."}]], "function": [{"name": "get_criminal_records", "description": "Retrieve the criminal records of a specific person in a specific area during a certain time period.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY"}, "from_year": {"type": "integer", "description": "The start year of the time frame."}, "to_year": {"type": "integer", "description": "The end year of the time frame."}}, "required": ["name", "location", "from_year", "to_year"]}}]}, "model_result_raw": [{"get_criminal_records": "{\"name\": \"Mr. X\", \"location\": \"New York\", \"from_year\": 2012, \"to_year\": 2015}"}], "model_result_decoded": [{"get_criminal_records": {"name": "Mr. X", "location": "New York", "from_year": 2012, "to_year": 2015}}], "possible_answer": [{"get_criminal_records": {"name": ["Mr. X"], "location": ["New York, NY"], "from_year": [2012], "to_year": [2015]}}]}
{"id": "simple_177", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'status': 'open'. Expected one of ['all', '']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_177", "question": [[{"role": "user", "content": "Find all Patent lawsuit cases of Facebook in 2018."}]], "function": [{"name": "get_lawsuit_cases", "description": "Retrieve all lawsuit cases related to a specific company during a particular year.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "year": {"type": "integer", "description": "The specific year to search for lawsuit cases."}, "status": {"type": "string", "enum": ["open", "closed", "all"], "description": "The status of the lawsuit cases to retrieve. If not specified, defaults to 'all'."}}, "required": ["company_name", "year"]}}]}, "model_result_raw": [{"get_lawsuit_cases": "{\"company_name\": \"Facebook\", \"year\": 2018, \"status\": \"open\"}"}], "model_result_decoded": [{"get_lawsuit_cases": {"company_name": "Facebook", "year": 2018, "status": "open"}}], "possible_answer": [{"get_lawsuit_cases": {"company_name": ["Facebook"], "year": [2018], "status": ["all", ""]}}]}
{"id": "simple_187", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_187", "question": [[{"role": "user", "content": "What's the current temperature and humidity in Seattle, Washington?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current temperature and humidity for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city name to get the weather for."}, "include_temperature": {"type": "boolean", "description": "Whether to include the temperature in the result. Default is true."}, "include_humidity": {"type": "boolean", "description": "Whether to include the humidity in the result. Default is true."}}, "required": ["location"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"get_current_weather": {"location": ["Seattle", "Seattle, Washington"], "include_temperature": [true, ""], "include_humidity": [true, ""]}}]}
{"id": "simple_192", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_192", "question": [[{"role": "user", "content": "Calculate the slope gradient in degree between two points on a landscape with coordinates (40.7128, -74.0060) and (34.0522, -118.2437)."}]], "function": [{"name": "calculate_slope_gradient", "description": "Calculate the slope gradient between two geographical coordinates.", "parameters": {"type": "dict", "properties": {"point1": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates for the first point [Latitude, Longitude]."}, "point2": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates for the second point [Latitude, Longitude]."}, "unit": {"type": "string", "enum": ["degree", "percent", "ratio"], "description": "The unit for the slope gradient. Default is 'degree'."}}, "required": ["point1", "point2"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"calculate_slope_gradient": {"point1": [[40.7128, -74.006]], "point2": [[34.0522, -118.2437]], "unit": ["degree", ""]}}]}
{"id": "simple_196", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'date': '8-16-2022'. Expected one of ['08-16-2022']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_196", "question": [[{"role": "user", "content": "What is the air quality index in London 2022/08/16?"}]], "function": [{"name": "air_quality", "description": "Retrieve the air quality index for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality index for."}, "date": {"type": "string", "description": "The date (month-day-year) you want to get the air quality index for."}}, "required": ["location", "date"]}}]}, "model_result_raw": [{"air_quality": "{\"location\": \"London\", \"date\": \"8-16-2022\"}"}], "model_result_decoded": [{"air_quality": {"location": "London", "date": "8-16-2022"}}], "possible_answer": [{"air_quality": {"location": ["London"], "date": ["08-16-2022"]}}]}
{"id": "simple_200", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'fuel_type': 'gasoline'. Expected one of ['gas']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_200", "question": [[{"role": "user", "content": "How much CO2 is produced annually by a gas-fueled car that travels 12,000 miles per year, with fuel efficiency of 25 MPG ?"}]], "function": [{"name": "calculate_emissions", "description": "Calculates the annual carbon dioxide emissions produced by a vehicle based on the distance traveled, the fuel type and the fuel efficiency of the vehicle.", "parameters": {"type": "dict", "properties": {"distance": {"type": "integer", "description": "The distance travelled in miles."}, "fuel_type": {"type": "string", "description": "Type of fuel used by the vehicle."}, "fuel_efficiency": {"type": "float", "description": "The vehicle's fuel efficiency in miles per gallon."}, "efficiency_reduction": {"type": "integer", "description": "The percentage decrease in fuel efficiency per year (optional). Default is 0"}}, "required": ["distance", "fuel_type", "fuel_efficiency"]}}]}, "model_result_raw": [{"calculate_emissions": "{\"distance\": 12000, \"fuel_type\": \"gasoline\", \"fuel_efficiency\": 25.0}"}], "model_result_decoded": [{"calculate_emissions": {"distance": 12000, "fuel_type": "gasoline", "fuel_efficiency": 25.0}}], "possible_answer": [{"calculate_emissions": {"distance": [12000], "fuel_type": ["gas"], "fuel_efficiency": ["", 25.0], "efficiency_reduction": [0, ""]}}]}
{"id": "simple_232", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_232", "question": [[{"role": "user", "content": "What was the full name king of England in 1800?"}]], "function": [{"name": "monarch.getMonarchOfYear", "description": "Retrieve the monarch of a specific location during a specified year.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location (e.g., country) whose monarch needs to be found."}, "year": {"type": "integer", "description": "The year to search the monarch."}, "fullName": {"type": "boolean", "default": false, "description": "If true, returns the full name and title of the monarch."}}, "required": ["location", "year"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"monarch.getMonarchOfYear": {"location": ["England", "ENG"], "year": [1800], "fullName": [true]}}]}
{"id": "simple_245", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_245", "question": [[{"role": "user", "content": "Who discovered radium?"}]], "function": [{"name": "discoverer.get", "description": "Retrieve the name of the discoverer of an element based on its name.", "parameters": {"type": "dict", "properties": {"element_name": {"type": "string", "description": "The name of the element."}, "year": {"type": "integer", "description": "Optional parameter that refers to the year of discovery. It could be helpful in case an element was discovered more than once. Default to 0, which means not use it."}, "first": {"type": "boolean", "default": true, "description": "Optional parameter indicating if the first discoverer's name should be retrieved."}}, "required": ["element_name"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"discoverer.get": {"element_name": ["'radium'", "\"radium\"", "radium"], "year": ["", 0], "first": [true, ""]}}]}
{"id": "simple_248", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_248", "question": [[{"role": "user", "content": "Who invented the theory of relativity and in which year?"}]], "function": [{"name": "science_history.get_invention", "description": "Retrieve the inventor and year of invention based on the invention's name.", "parameters": {"type": "dict", "properties": {"invention_name": {"type": "string", "description": "The name of the invention."}, "want_year": {"type": "boolean", "default": false, "description": "Return the year of invention if set to true."}}, "required": ["invention_name", "want_year"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"science_history.get_invention": {"invention_name": ["theory of relativity", "Theory of Relativity"], "want_year": [true]}}]}
{"id": "simple_249", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'include_people': True. Expected one of [False, '']."], "error_type": "value_error:others", "prompt": {"id": "simple_249", "question": [[{"role": "user", "content": "Tell me more about Christianity and its history till the 14th century"}]], "function": [{"name": "religion.history_info", "description": "Provides comprehensive historical details about a specified religion till a specified century.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "The name of the religion for which historical details are needed."}, "till_century": {"type": "integer", "description": "The century till which historical details are needed."}, "include_people": {"type": "boolean", "description": "To include influential people related to the religion during that time period, default is False"}}, "required": ["religion", "till_century"]}}]}, "model_result_raw": [{"religion_history_info": "{\"religion\": \"Christianity\", \"till_century\": 14, \"include_people\": true}"}], "model_result_decoded": [{"religion_history_info": {"religion": "Christianity", "till_century": 14, "include_people": true}}], "possible_answer": [{"religion.history_info": {"religion": ["Christianity"], "till_century": [14], "include_people": [false, ""]}}]}
{"id": "simple_267", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York City, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_267", "question": [[{"role": "user", "content": "Find the top rated modern sculpture exhibition happening in New York in the upcoming month."}]], "function": [{"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the exhibition is held, e.g., New York City, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events if not specified."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'low'"}}, "required": ["location", "art_form"]}}]}, "model_result_raw": [{"find_exhibition": "{\"location\": \"New York\", \"art_form\": \"sculpture\", \"month\": \"upcoming\", \"user_ratings\": \"high\"}"}], "model_result_decoded": [{"find_exhibition": {"location": "New York", "art_form": "sculpture", "month": "upcoming", "user_ratings": "high"}}], "possible_answer": [{"find_exhibition": {"location": ["New York City, NY"], "art_form": ["sculpture", "modern sculpture"], "month": [""], "user_ratings": ["high"]}}]}
{"id": "simple_274", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_274", "question": [[{"role": "user", "content": "Find information about the opening hours of the Metropolitan Museum of Art."}]], "function": [{"name": "museum_info", "description": "Retrieve information about the opening hours of a museum based on its name.", "parameters": {"type": "dict", "properties": {"museum_name": {"type": "string", "description": "The name of the museum."}, "info_type": {"type": "string", "description": "The type of information needed about the museum.", "default": "opening_hours"}}, "required": ["museum_name"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"museum_info": {"museum_name": ["Metropolitan Museum of Art", "The Metropolitan Museum of Art", "Met Museum"], "info_type": ["opening_hours", ""]}}]}
{"id": "simple_290", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'year': 2024. Expected one of ['', 2022]."], "error_type": "value_error:others", "prompt": {"id": "simple_290", "question": [[{"role": "user", "content": "What's the timing and location for The Weeknd's concert happening in December?"}]], "function": [{"name": "concert.find_details", "description": "Finds details of a concert event.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the artist performing."}, "month": {"type": "string", "description": "Month in which the concert is happening."}, "year": {"type": "integer", "description": "Year of the concert.", "default": 2022}}, "required": ["artist", "month"]}}]}, "model_result_raw": [{"concert_find_details": "{\"artist\": \"The Weeknd\", \"month\": \"December\", \"year\": 2024}"}], "model_result_decoded": [{"concert_find_details": {"artist": "The Weeknd", "month": "December", "year": 2024}}], "possible_answer": [{"concert.find_details": {"artist": ["The Weeknd"], "month": ["December"], "year": ["", 2022]}}]}
{"id": "simple_352", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'platform'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_352", "question": [[{"role": "user", "content": "Get the average user score for the game 'The Legend of Zelda: Breath of the Wild' from GameSpot."}]], "function": [{"name": "gamespot.getAverageUserScore", "description": "Retrieve the average user score of a game from GameSpot.", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "The name of the game."}, "platform": {"type": "string", "description": "The platform the game was released on (e.g., Nintendo Switch, PS5, etc.)", "default": "all platforms"}}, "required": ["game_name", "platform"]}}]}, "model_result_raw": [{"gamespot_getAverageUserScore": "{\"game_name\": \"The Legend of Zelda: Breath of the Wild\"}"}], "model_result_decoded": [{"gamespot_getAverageUserScore": {"game_name": "The Legend of Zelda: Breath of the Wild"}}], "possible_answer": [{"gamespot.getAverageUserScore": {"game_name": ["The Legend of Zelda: Breath of the Wild"], "platform": ["Nintendo Switch", "all platforms"]}}]}
{"id": "simple_355", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'recipe': 'Beef Lasagna Recipe'. Expected one of ['Beef Lasagna']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_355", "question": [[{"role": "user", "content": "How many calories in the Beef Lasagna Recipe from Foodnetwork.com?"}]], "function": [{"name": "recipe_info.get_calories", "description": "Retrieve the amount of calories from a specific recipe in a food website.", "parameters": {"type": "dict", "properties": {"website": {"type": "string", "description": "The food website that has the recipe."}, "recipe": {"type": "string", "description": "Name of the recipe."}, "optional_meal_time": {"type": "string", "description": "Specific meal time of the day for the recipe (optional, could be 'Breakfast', 'Lunch', 'Dinner'). Default is all if not specified."}}, "required": ["website", "recipe"]}}]}, "model_result_raw": [{"recipe_info_get_calories": "{\"website\": \"Foodnetwork.com\", \"recipe\": \"Beef Lasagna Recipe\"}"}], "model_result_decoded": [{"recipe_info_get_calories": {"website": "Foodnetwork.com", "recipe": "Beef Lasagna Recipe"}}], "possible_answer": [{"recipe_info.get_calories": {"website": ["Foodnetwork.com"], "recipe": ["Beef Lasagna"], "optional_meal_time": [""]}}]}
{"id": "simple_367", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'recipe_type': 'brownies'. Expected one of ['dessert']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_367", "question": [[{"role": "user", "content": "Find me a vegan recipe for brownies which prep time is under 30 minutes."}]], "function": [{"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}]}, "model_result_raw": [{"find_recipe": "{\"dietary_restrictions\": \"vegan\", \"recipe_type\": \"brownies\", \"time\": 30}"}], "model_result_decoded": [{"find_recipe": {"dietary_restrictions": "vegan", "recipe_type": "brownies", "time": 30}}], "possible_answer": [{"find_recipe": {"dietary_restrictions": ["vegan"], "recipe_type": ["dessert"], "time": [30]}}]}
{"id": "simple_370", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'quantity': [3, 5]. Expected one of [[3, 1]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": [{"safeway_order": "{\"location\": \"Palo Alto, CA\", \"items\": [\"olive oil\", \"rice\"], \"quantity\": [3, 5]}"}], "model_result_decoded": [{"safeway_order": {"location": "Palo Alto, CA", "items": ["olive oil", "rice"], "quantity": [3, 5]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_375", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'store_location': 'Walmart'. Expected one of ['']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_375", "question": [[{"role": "user", "content": "Check the total price for three pumpkins and two dozen eggs at Walmart."}]], "function": [{"name": "walmart.check_price", "description": "Calculate total price for given items and their quantities at Walmart.", "parameters": {"type": "dict", "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "List of items to be priced."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item corresponding to the items list."}, "store_location": {"type": "string", "description": "The store location for specific pricing (optional). Default to all if not specified."}}, "required": ["items", "quantities"]}}]}, "model_result_raw": [{"walmart_check_price": "{\"items\": [\"pumpkins\", \"eggs\"], \"quantities\": [3, 2], \"store_location\": \"Walmart\"}"}], "model_result_decoded": [{"walmart_check_price": {"items": ["pumpkins", "eggs"], "quantities": [3, 2], "store_location": "Walmart"}}], "possible_answer": [{"walmart.check_price": {"items": [["pumpkins", "eggs"], ["pumpkin", "dozen eggs"]], "quantities": [[3, 24], [3, 2]], "store_location": [""]}}]}
{"id": "simple_394", "model_name": "meta-llama_Llama-4-Scout-17B-16E-Instruct-FC", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_394", "question": [[{"role": "user", "content": "Get me the travel distance and duration from the Eiffel Tower to the Louvre Museum"}]], "function": [{"name": "maps.get_distance_duration", "description": "Retrieve the travel distance and estimated travel time from one location to another via car", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "Starting point of the journey"}, "end_location": {"type": "string", "description": "Ending point of the journey"}, "traffic": {"type": "boolean", "description": "If true, considers current traffic. Default is false."}}, "required": ["start_location", "end_location"]}}]}, "model_result_raw": [], "model_result_decoded": [], "possible_answer": [{"maps.get_distance_duration": {"start_location": ["Eiffel Tower"], "end_location": ["Louvre Museum"], "traffic": ["", false]}}]}