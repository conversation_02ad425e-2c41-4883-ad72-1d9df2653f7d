{"name": "add_to_watchlist", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Add a stock to the watchlist.", "parameters": {"type": "dict", "properties": {"stock": {"type": "string", "description": "the stock symbol to add to the watchlist. "}}, "required": ["stock"]}, "response": {"type": "dict", "properties": {"symbol": {"type": "string", "description": "the symbol that were successfully added to the watchlist."}}}}
{"name": "cancel_order", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Cancel an order.", "parameters": {"type": "dict", "properties": {"order_id": {"type": "integer", "description": "ID of the order to cancel. "}}, "required": ["order_id"]}, "response": {"type": "dict", "properties": {"order_id": {"type": "integer", "description": "ID of the cancelled order."}, "status": {"type": "string", "description": "New status of the order after cancellation attempt."}}}}
{"name": "filter_stocks_by_price", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Filter stocks based on a price range.", "parameters": {"type": "dict", "properties": {"stocks": {"type": "array", "items": {"type": "string"}, "description": "List of stock symbols to filter."}, "min_price": {"type": "float", "description": "Minimum stock price."}, "max_price": {"type": "float", "description": "Maximum stock price. "}}, "required": ["stocks", "min_price", "max_price"]}, "response": {"type": "dict", "properties": {"filtered_stocks": {"type": "array", "description": "Filtered list of stock symbols within the price range.", "items": {"type": "string"}}}}}
{"name": "fund_account", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Fund the account with the specified amount.", "parameters": {"type": "dict", "properties": {"amount": {"type": "float", "description": "Amount to fund the account with. "}}, "required": ["amount"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Status of the funding operation."}, "new_balance": {"type": "float", "description": "Updated account balance after funding."}}}}
{"name": "get_account_info", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get account information.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"account_id": {"type": "integer", "description": "ID of the account."}, "balance": {"type": "float", "description": "Current balance of the account."}, "binding_card": {"type": "integer", "description": "Card number associated with the account."}}}}
{"name": "get_available_stocks", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get a list of stock symbols in the given sector.", "parameters": {"type": "dict", "properties": {"sector": {"type": "string", "description": "The sector to retrieve stocks from (e.g., 'Technology'). "}}, "required": ["sector"]}, "response": {"type": "dict", "properties": {"stock_list": {"type": "array", "description": "List of stock symbols in the specified sector.", "items": {"type": "string"}}}}}
{"name": "get_current_time", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get the current time.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"current_time": {"type": "string", "description": "Current time in HH:MM AM/PM format."}}}}
{"name": "get_order_details", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get the details of an order.", "parameters": {"type": "dict", "properties": {"order_id": {"type": "integer", "description": "ID of the order. "}}, "required": ["order_id"]}, "response": {"type": "dict", "properties": {"id": {"type": "integer", "description": "ID of the order."}, "order_type": {"type": "string", "description": "Type of the order."}, "symbol": {"type": "string", "description": "Symbol of the stock in the order."}, "price": {"type": "float", "description": "Price at which the order was placed."}, "amount": {"type": "integer", "description": "Number of shares in the order."}, "status": {"type": "string", "description": "Current status of the order. [Enum]: [\"Open\", \"Pending\", \"Completed\", \"Cancelled\"]"}}}}
{"name": "get_order_history", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get the stock order ID history.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"order_history": {"type": "array", "description": "List of orders ID in the order history.", "items": {"type": "integer"}}}}}
{"name": "get_stock_info", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get the details of a stock.", "parameters": {"type": "dict", "properties": {"symbol": {"type": "string", "description": "Symbol that uniquely identifies the stock. "}}, "required": ["symbol"]}, "response": {"type": "dict", "properties": {"price": {"type": "float", "description": "Current price of the stock."}, "percent_change": {"type": "float", "description": "Percentage change in stock price."}, "volume": {"type": "float", "description": "Trading volume of the stock. MA(5) (float): 5-day Moving Average of the stock. MA(20) (float): 20-day Moving Average of the stock."}}}}
{"name": "get_symbol_by_name", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get the symbol of a stock by company name.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "Name of the company. "}}, "required": ["name"]}, "response": {"type": "dict", "properties": {"symbol": {"type": "string", "description": "Symbol of the stock or \"Stock not found\" if not available."}}}}
{"name": "get_transaction_history", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get the transaction history within a specified date range.", "parameters": {"type": "dict", "properties": {"start_date": {"type": "string", "description": "Start date for the history (format: 'YYYY-MM-DD').", "default": "None"}, "end_date": {"type": "string", "description": "End date for the history (format: 'YYYY-MM-DD'). ", "default": "None"}}, "required": []}, "response": {"type": "dict", "properties": {"transaction_history": {"type": "array", "description": "List of transactions within the specified date range.", "items": {"type": "dict", "properties": {"type": {"type": "string", "description": "Type of transaction. [Enum]: [\"deposit\", \"withdrawal\"]"}, "amount": {"type": "float", "description": "Amount involved in the transaction."}, "timestamp": {"type": "string", "description": "Timestamp of the transaction, formatted as 'YYYY-MM-DD HH:MM:SS'."}}}}}}}
{"name": "get_watchlist", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get the watchlist.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"watchlist": {"type": "array", "description": "List of stock symbols in the watchlist.", "items": {"type": "string"}}}}}
{"name": "make_transaction", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Make a deposit or withdrawal based on specified amount.", "parameters": {"type": "dict", "properties": {"account_id": {"type": "integer", "description": "ID of the account."}, "xact_type": {"type": "string", "description": "Transaction type (deposit or withdrawal)."}, "amount": {"type": "float", "description": "Amount to deposit or withdraw. "}}, "required": ["account_id", "xact_type", "amount"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Status of the transaction."}, "new_balance": {"type": "float", "description": "Updated account balance after the transaction."}}}}
{"name": "notify_price_change", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Notify if there is a significant price change in the stocks.", "parameters": {"type": "dict", "properties": {"stocks": {"type": "array", "items": {"type": "string"}, "description": "List of stock symbols to check."}, "threshold": {"type": "float", "description": "Percentage change threshold to trigger a notification. "}}, "required": ["stocks", "threshold"]}, "response": {"type": "dict", "properties": {"notification": {"type": "string", "description": "Notification message about the price changes."}}}}
{"name": "place_order", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Place an order.", "parameters": {"type": "dict", "properties": {"order_type": {"type": "string", "description": "Type of the order (Buy/Sell)."}, "symbol": {"type": "string", "description": "Symbol of the stock to trade."}, "price": {"type": "float", "description": "Price at which to place the order."}, "amount": {"type": "integer", "description": "Number of shares to trade. "}}, "required": ["order_type", "symbol", "price", "amount"]}, "response": {"type": "dict", "properties": {"order_id": {"type": "integer", "description": "ID of the newly placed order."}, "order_type": {"type": "string", "description": "Type of the order (Buy/Sell)."}, "status": {"type": "string", "description": "Initial status of the order."}, "price": {"type": "float", "description": "Price at which the order was placed."}, "amount": {"type": "integer", "description": "Number of shares in the order."}}}}
{"name": "remove_stock_from_watchlist", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Remove a stock from the watchlist.", "parameters": {"type": "dict", "properties": {"symbol": {"type": "string", "description": "Symbol of the stock to remove. "}}, "required": ["symbol"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Status of the removal operation."}}}}
{"name": "trading_get_login_status", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Get the login status.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"status": {"type": "boolean", "description": "Login status."}}}}
{"name": "trading_login", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Handle user login.", "parameters": {"type": "dict", "properties": {"username": {"type": "string", "description": "Username for authentication."}, "password": {"type": "string", "description": "Password for authentication. "}}, "required": ["username", "password"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Login status message."}}}}
{"name": "trading_logout", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Handle user logout for trading system.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Logout status message."}}}}
{"name": "update_market_status", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Update the market status based on the current time.", "parameters": {"type": "dict", "properties": {"current_time_str": {"type": "string", "description": "Current time in HH:MM AM/PM format. "}}, "required": ["current_time_str"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Status of the market. [Enum]: [\"Open\", \"Closed\"]"}}}}
{"name": "update_stock_price", "description": "This tool belongs to the trading system, which allows users to trade stocks, manage their account, and view stock information. Tool description: Update the price of a stock.", "parameters": {"type": "dict", "properties": {"symbol": {"type": "string", "description": "Symbol of the stock to update."}, "new_price": {"type": "float", "description": "New price of the stock. "}}, "required": ["symbol", "new_price"]}, "response": {"type": "dict", "properties": {"symbol": {"type": "string", "description": "Symbol of the updated stock."}, "old_price": {"type": "float", "description": "Previous price of the stock."}, "new_price": {"type": "float", "description": "Updated price of the stock."}}}}
