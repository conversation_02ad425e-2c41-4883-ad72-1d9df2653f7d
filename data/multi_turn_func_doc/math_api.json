{"name": "absolute_value", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Calculate the absolute value of a number.", "parameters": {"type": "dict", "properties": {"number": {"type": "float", "description": "The number to calculate the absolute value of. "}}, "required": ["number"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The absolute value of the number."}}}}
{"name": "add", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Add two numbers.", "parameters": {"type": "dict", "properties": {"a": {"type": "float", "description": "First number."}, "b": {"type": "float", "description": "Second number. "}}, "required": ["a", "b"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "Sum of the two numbers."}}}}
{"name": "divide", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Divide one number by another.", "parameters": {"type": "dict", "properties": {"a": {"type": "float", "description": "Numerator."}, "b": {"type": "float", "description": "Denominator. "}}, "required": ["a", "b"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "Quotient of the division."}}}}
{"name": "imperial_si_conversion", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Convert a value between imperial and SI units.", "parameters": {"type": "dict", "properties": {"value": {"type": "float", "description": "Value to be converted."}, "unit_in": {"type": "string", "description": "Unit of the input value."}, "unit_out": {"type": "string", "description": "Unit to convert the value to. "}}, "required": ["value", "unit_in", "unit_out"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "Converted value in the new unit."}}}}
{"name": "logarithm", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Compute the logarithm of a number with adjustable precision using mpmath.", "parameters": {"type": "dict", "properties": {"value": {"type": "float", "description": "The number to compute the logarithm of."}, "base": {"type": "float", "description": "The base of the logarithm."}, "precision": {"type": "integer", "description": "Desired precision for the result. "}}, "required": ["value", "base", "precision"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The logarithm of the number with respect to the given base."}}}}
{"name": "max_value", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Find the maximum value in a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "List of numbers to find the maximum from. "}}, "required": ["numbers"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The maximum value in the list."}}}}
{"name": "mean", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Calculate the mean of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "List of numbers to calculate the mean of. "}}, "required": ["numbers"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "Mean of the numbers."}}}}
{"name": "min_value", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Find the minimum value in a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "List of numbers to find the minimum from. "}}, "required": ["numbers"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The minimum value in the list."}}}}
{"name": "multiply", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Multiply two numbers.", "parameters": {"type": "dict", "properties": {"a": {"type": "float", "description": "First number."}, "b": {"type": "float", "description": "Second number. "}}, "required": ["a", "b"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "Product of the two numbers."}}}}
{"name": "percentage", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Calculate the percentage of a part relative to a whole.", "parameters": {"type": "dict", "properties": {"part": {"type": "float", "description": "The part value."}, "whole": {"type": "float", "description": "The whole value. "}}, "required": ["part", "whole"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The percentage of the part relative to the whole."}}}}
{"name": "power", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Raise a number to a power.", "parameters": {"type": "dict", "properties": {"base": {"type": "float", "description": "The base number."}, "exponent": {"type": "float", "description": "The exponent. "}}, "required": ["base", "exponent"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The base raised to the power of the exponent."}}}}
{"name": "round_number", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Round a number to a specified number of decimal places.", "parameters": {"type": "dict", "properties": {"number": {"type": "float", "description": "The number to round."}, "decimal_places": {"type": "integer", "description": "The number of decimal places to round to. Defaults to 0. ", "default": 0}}, "required": ["number"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The rounded number."}}}}
{"name": "si_unit_conversion", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Convert a value from one SI unit to another.", "parameters": {"type": "dict", "properties": {"value": {"type": "float", "description": "Value to be converted."}, "unit_in": {"type": "string", "description": "Unit of the input value."}, "unit_out": {"type": "string", "description": "Unit to convert the value to. "}}, "required": ["value", "unit_in", "unit_out"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "Converted value in the new unit."}}}}
{"name": "square_root", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Calculate the square root of a number with adjustable precision using the decimal module.", "parameters": {"type": "dict", "properties": {"number": {"type": "float", "description": "The number to calculate the square root of."}, "precision": {"type": "integer", "description": "Desired precision for the result. "}}, "required": ["number", "precision"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The square root of the number, or an error message."}}}}
{"name": "standard_deviation", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Calculate the standard deviation of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "List of numbers to calculate the standard deviation of. "}}, "required": ["numbers"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "Standard deviation of the numbers."}}}}
{"name": "subtract", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Subtract one number from another.", "parameters": {"type": "dict", "properties": {"a": {"type": "float", "description": "Number to subtract from."}, "b": {"type": "float", "description": "Number to subtract. "}}, "required": ["a", "b"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "Difference between the two numbers."}}}}
{"name": "sum_values", "description": "This tool belongs to the Math API, which provides various mathematical operations. Tool description: Calculate the sum of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "List of numbers to sum. "}}, "required": ["numbers"]}, "response": {"type": "dict", "properties": {"result": {"type": "float", "description": "The sum of all numbers in the list."}}}}
