{"id": "multiple_0", "question": [[{"role": "user", "content": "Can I find the dimensions and properties of a triangle, if I know its three sides are 5 units, 4 units and 3 units long?"}]], "function": [{"name": "triangle_properties.get", "description": "Retrieve the dimensions, such as area and perimeter, of a triangle if lengths of three sides are given.", "parameters": {"type": "dict", "properties": {"side1": {"type": "integer", "description": "The length of first side of the triangle."}, "side2": {"type": "integer", "description": "The length of second side of the triangle."}, "side3": {"type": "integer", "description": "The length of third side of the triangle."}, "get_area": {"type": "boolean", "description": "A flag to determine whether to calculate the area of triangle. Default is true.", "default": true, "optional": true}, "get_perimeter": {"type": "boolean", "description": "A flag to determine whether to calculate the perimeter of triangle. Default is true.", "default": true, "optional": true}, "get_angles": {"type": "boolean", "description": "A flag to determine whether to calculate the internal angles of triangle. Default is true.", "default": true, "optional": true}}, "required": ["side1", "side2", "side3"]}}, {"name": "circle_properties.get", "description": "Retrieve the dimensions, such as area and circumference, of a circle if radius is given.", "parameters": {"type": "dict", "properties": {"radius": {"type": "float", "description": "The length of radius of the circle."}, "get_area": {"type": "boolean", "description": "A flag to determine whether to calculate the area of circle. Default is true.", "default": true, "optional": true}, "get_circumference": {"type": "boolean", "description": "A flag to determine whether to calculate the circumference of circle. Default is true.", "default": true, "optional": true}}, "required": ["radius"]}}]}
{"id": "multiple_1", "question": [[{"role": "user", "content": "Calculate the area of a triangle, given the lengths of its three sides: 3, 4, and 5."}]], "function": [{"name": "math.triangle_area_heron", "description": "Calculates the area of a triangle using Heron's formula, given the lengths of its three sides.", "parameters": {"type": "dict", "properties": {"side1": {"type": "integer", "description": "Length of the first side of the triangle."}, "side2": {"type": "integer", "description": "Length of the second side of the triangle."}, "side3": {"type": "integer", "description": "Length of the third side of the triangle."}}, "required": ["side1", "side2", "side3"]}}, {"name": "math.circle_area", "description": "Calculates the area of a circle given its radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "float", "description": "The radius of the circle."}}, "required": ["radius"]}}, {"name": "math.triangle_area_base_height", "description": "Calculates the area of a triangle using the formula (1/2)base*height.", "parameters": {"type": "dict", "properties": {"base": {"type": "float", "description": "The base length of the triangle."}, "height": {"type": "float", "description": "The height of the triangle."}}, "required": ["base", "height"]}}]}
{"id": "multiple_2", "question": [[{"role": "user", "content": "What is the capital of Brazil?"}]], "function": [{"name": "country_info.largest_city", "description": "Fetch the largest city of a specified country.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "Name of the country."}}, "required": ["country"]}}, {"name": "country_info.capital", "description": "Fetch the capital city of a specified country.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "Name of the country."}}, "required": ["country"]}}, {"name": "country_info.population", "description": "Fetch the current population of a specified country.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "Name of the country."}}, "required": ["country"]}}]}
{"id": "multiple_3", "question": [[{"role": "user", "content": "Compute the Euclidean distance between two points A(3,4) and B(1,2)."}]], "function": [{"name": "EuclideanDistance.calculate", "description": "Calculate the Euclidean distance between two points.", "parameters": {"type": "dict", "properties": {"pointA": {"type": "array", "items": {"type": "integer"}, "description": "Coordinates for Point A."}, "pointB": {"type": "array", "items": {"type": "integer"}, "description": "Coordinates for Point B."}, "rounding": {"type": "integer", "description": "Optional: The number of decimals to round off the result. Default 0"}}, "required": ["pointA", "pointB"]}}, {"name": "angleToXAxis.calculate", "description": "Calculate the angle between two points with respect to x-axis.", "parameters": {"type": "dict", "properties": {"pointA": {"type": "array", "items": {"type": "integer"}, "description": "Coordinates for Point A."}, "pointB": {"type": "array", "items": {"type": "integer"}, "description": "Coordinates for Point B."}, "rounding": {"type": "integer", "description": "Optional: The number of decimals to round off the result. Default 0"}}, "required": ["pointA", "pointB"]}}]}
{"id": "multiple_4", "question": [[{"role": "user", "content": "Can you calculate the displacement of a car moving at an initial speed of 20 m/s and then accelerates at 10 m/s^2 for 5 seconds? (assuming a straight line motion)"}]], "function": [{"name": "kinematics.calculate_displacement", "description": "Calculate displacement based on initial speed, acceleration, and time interval for a motion along a straight line.", "parameters": {"type": "dict", "properties": {"initial_speed": {"type": "integer", "description": "The initial speed of the moving object in m/s."}, "acceleration": {"type": "integer", "description": "The rate of change of speed, m/s^2."}, "time": {"type": "integer", "description": "The time interval during which the acceleration is applied, in seconds."}, "rounding": {"type": "integer", "description": "The number of decimals to round off the result (optional).", "default": 2}}, "required": ["initial_speed", "acceleration", "time"]}}, {"name": "kinematics.calculate_final_speed", "description": "Calculate the final speed of an object that starts from an initial speed and then accelerates for a certain duration.", "parameters": {"type": "dict", "properties": {"initial_speed": {"type": "float", "description": "The initial speed of the moving object in m/s."}, "acceleration": {"type": "float", "description": "The rate of change of speed, m/s^2."}, "time": {"type": "float", "description": "The time interval during which the acceleration is applied, in seconds."}, "rounding": {"type": "integer", "description": "The number of decimals to round off the result (optional).", "default": 2}}, "required": ["initial_speed", "acceleration", "time"]}}]}
{"id": "multiple_5", "question": [[{"role": "user", "content": "What is the wind speed and temperature in location given by coordinates 46.603354,1.8883340 on December 13, 2019?"}]], "function": [{"name": "weather.get_by_city_date", "description": "Retrieves the historical weather data based on city and date.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city for which to retrieve the weather."}, "date": {"type": "string", "format": "date", "description": "The date for which to retrieve the historical weather data in the format YYYY-MM-DD."}}, "required": ["city", "date"]}}, {"name": "weather.get_forecast_by_coordinates", "description": "Get the weather forecast for a specific geographical coordinates.", "parameters": {"type": "dict", "properties": {"coordinates": {"type": "tuple", "items": {"type": "float"}, "description": "The geographical coordinates for which to retrieve the weather. The first element of the tuple is the latitude and the second is the longitude."}, "days_ahead": {"type": "integer", "description": "Number of days to forecast from current date (optional, default is 7)."}}, "required": ["coordinates"]}}, {"name": "weather.get_by_coordinates_date", "description": "Retrieves the historical weather data based on coordinates and date.", "parameters": {"type": "dict", "properties": {"coordinates": {"type": "tuple", "items": {"type": "float"}, "description": "The geographical coordinates for which to retrieve the weather. The first element of the tuple is the latitude and the second is the longitude."}, "date": {"type": "string", "format": "date", "description": "The date for which to retrieve the historical weather data in the format YYYY-MM-DD."}}, "required": ["coordinates", "date"]}}]}
{"id": "multiple_6", "question": [[{"role": "user", "content": "Calculate the capacitance of a parallel plate capacitor where the area of the plate is 10 square meters, the distance between plates is 0.01 meters and the dielectric constant K is 1.0."}]], "function": [{"name": "resistance_calculator.calculate", "description": "Calculate the resistance of an electrical circuit based on current and voltage.", "parameters": {"type": "dict", "properties": {"I": {"type": "float", "description": "The electric current flowing in Amperes."}, "V": {"type": "float", "description": "The voltage difference in Volts."}}, "required": ["I", "V"]}}, {"name": "capacitance_calculator.calculate", "description": "Calculate the capacitance of a parallel plate capacitor based on the area, distance and dielectric constant using the equation C = \u03b5\u2080KA/d.", "parameters": {"type": "dict", "properties": {"A": {"type": "integer", "description": "The area of one plate of the capacitor in square meters."}, "d": {"type": "float", "description": "The distance between the two plates in meters."}, "K": {"type": "float", "description": "The dielectric constant (default is 1.0 for free space, optional)."}}, "required": ["A", "d"]}}, {"name": "magnetic_field.calculate", "description": "Calculate the magnetic field based on the current flowing and the radial distance.", "parameters": {"type": "dict", "properties": {"I": {"type": "float", "description": "The electric current flowing in Amperes."}, "r": {"type": "float", "description": "The radial distance from the line of current in meters."}}, "required": ["I", "r"]}}]}
{"id": "multiple_7", "question": [[{"role": "user", "content": "How to assess the population growth in deer and their impact on woodland in Washington state over the past decade?"}]], "function": [{"name": "wildlife_population.assess_growth", "description": "Assesses the population growth of a specific species in a specified location over a period.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species for which the growth is to be calculated."}, "location": {"type": "string", "description": "The area where the species is present."}, "duration": {"type": "integer", "description": "The time period for which the population growth should be calculated in years."}}, "required": ["species", "location", "duration"]}}, {"name": "ecological_impact.analyze", "description": "Analyzes the impact of a species on a particular ecosystem.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species whose impact is to be calculated."}, "ecosystem": {"type": "string", "description": "The ecosystem being affected."}, "location": {"type": "string", "description": "The area where the impact is analyzed."}, "timeframe": {"type": "integer", "description": "The time period for which the impact analysis should be carried out in years.", "default": 5}}, "required": ["species", "ecosystem", "location"]}}]}
{"id": "multiple_8", "question": [[{"role": "user", "content": "Find a 3 bedroom villa for sale within $300,000 to $400,000 budget in San Diego."}]], "function": [{"name": "property_valuation.get", "description": "Get estimated value of a property based on location, specifications and age", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "City and state where the property is located, e.g. San Diego, CA."}, "propertyType": {"type": "string", "description": "Type of property such as villa, condo, apartment, etc."}, "bedrooms": {"type": "integer", "description": "Number of bedrooms required in the property."}, "age": {"type": "integer", "description": "Age of the property in years."}}, "required": ["location", "propertyType", "bedrooms", "age"]}}, {"name": "realestate.find_properties", "description": "Find properties based on location, budget, and specifications", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "City and state where the property is located, e.g. San Diego, CA."}, "propertyType": {"type": "string", "description": "Type of property such as villa, condo, apartment, etc."}, "bedrooms": {"type": "integer", "description": "Number of bedrooms required in the property."}, "budget": {"type": "dict", "properties": {"min": {"type": "float", "description": "Minimum budget limit."}, "max": {"type": "float", "description": "Maximum budget limit."}}, "description": "Budget range for the property."}}, "required": ["location", "propertyType", "bedrooms", "budget"]}}]}
{"id": "multiple_9", "question": [[{"role": "user", "content": "Calculate the average grade for student John who has these scores {'math':90, 'science':75, 'history':82, 'music':89} across different subjects."}]], "function": [{"name": "calculate_standard_deviation", "description": "This function calculates the standard deviation across different scores for a specific student.", "parameters": {"type": "dict", "properties": {"gradeDict": {"type": "dict", "description": "A dictionary where keys represent subjects and values represent scores"}}, "required": ["gradeDict"]}}, {"name": "calculate_average", "description": "This function calculates the average grade across different subjects for a specific student.", "parameters": {"type": "dict", "properties": {"gradeDict": {"type": "dict", "description": "A dictionary where keys represent subjects and values represent scores"}}, "required": ["gradeDict"]}}, {"name": "highest_grade", "description": "This function finds the subject where the student got the highest score.", "parameters": {"type": "dict", "properties": {"gradeDict": {"type": "dict", "description": "A dictionary where keys represent subjects and values represent scores"}}, "required": ["gradeDict"]}}]}
{"id": "multiple_10", "question": [[{"role": "user", "content": "I need to delete some columns from my employees database on personal_data table. I want to remove their email addresses and social security numbers to respect privacy."}]], "function": [{"name": "database.modify_columns", "description": "This function allows deletion or addition of columns in a database", "parameters": {"type": "dict", "properties": {"db_name": {"type": "string", "description": "The name of the database to modify."}, "table": {"type": "string", "description": "The name of the table to modify."}, "operation": {"type": "string", "description": "The operation to carry out on the table. Can be 'delete' or 'add'."}, "columns": {"type": "array", "description": "List of the columns to add or delete from the table.", "items": {"type": "string"}}}, "required": ["db_name", "table", "operation", "columns"]}}, {"name": "database.create_backup", "description": "This function creates a backup of the database before modification", "parameters": {"type": "dict", "properties": {"db_name": {"type": "string", "description": "The name of the database to create a backup of."}, "backup_location": {"type": "string", "description": "The file path where the backup should be stored."}, "timestamp": {"type": "boolean", "description": "Option to append a timestamp to the backup file name.", "default": "False"}}, "required": ["db_name", "backup_location"]}}]}
{"id": "multiple_11", "question": [[{"role": "user", "content": "Calculate the roots of a quadratic equation with coefficients 5, 20, and -25"}]], "function": [{"name": "math_roots.quadratic", "description": "Calculate the roots of a quadratic equation.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of the second-degree term."}, "b": {"type": "integer", "description": "Coefficient of the first-degree term."}, "c": {"type": "integer", "description": "Constant term."}}, "required": ["a", "b", "c"]}}, {"name": "math.roots.cubic", "description": "Calculate the roots of a cubic equation.", "parameters": {"type": "dict", "properties": {"a": {"type": "float", "description": "Coefficient of the third-degree term."}, "b": {"type": "float", "description": "Coefficient of the second-degree term."}, "c": {"type": "float", "description": "Coefficient of the first-degree term."}, "d": {"type": "float", "description": "Constant term."}}, "required": ["a", "b", "c", "d"]}}, {"name": "math.roots.polynomial", "description": "Calculate the roots of a polynomial equation.", "parameters": {"type": "dict", "properties": {"coefficients": {"type": "array", "items": {"type": "float"}, "description": "Array of coefficients of the polynomial equation starting from highest degree term."}, "degree": {"type": "integer", "description": "Degree of the polynomial equation. Default 0"}}, "required": ["coefficients"]}}]}
{"id": "multiple_12", "question": [[{"role": "user", "content": "What is the year over year growth rate for company 'Tech Inc' with revenues of $1M in 2019 and $1.2M in 2020?"}]], "function": [{"name": "corporate_finance.calculate_YOY_growth_rate", "description": "Calculate the year over year (YOY) growth rate for a company.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company for which to calculate the YOY growth rate."}, "year1": {"type": "integer", "description": "The initial year."}, "year1_revenue": {"type": "integer", "description": "The revenue for the initial year."}, "year2": {"type": "integer", "description": "The subsequent year."}, "year2_revenue": {"type": "integer", "description": "The revenue for the subsequent year."}}, "required": ["company_name", "year1", "year1_revenue", "year2", "year2_revenue"]}}, {"name": "financial_ratios.calculate_ROE", "description": "Calculate the return on equity (ROE) for a company.", "parameters": {"type": "dict", "properties": {"net_income": {"type": "float", "description": "Net income for the period."}, "shareholder_equity": {"type": "float", "description": "Average shareholder equity for the period."}}, "required": ["net_income", "shareholder_equity"]}}, {"name": "financial_ratios.calculate_ROA", "description": "Calculate the return on assets (ROA) for a company.", "parameters": {"type": "dict", "properties": {"net_income": {"type": "float", "description": "Net income for the period."}, "total_assets": {"type": "float", "description": "Total average assets for the period."}}, "required": ["net_income", "total_assets"]}}]}
{"id": "multiple_13", "question": [[{"role": "user", "content": "How much revenue would company XYZ generate if we increase the sales units of product A by 10% while keeping the price the same?"}]], "function": [{"name": "corporate_finance.product_price", "description": "Fetch the current selling price of the product.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company that sells the product."}, "product": {"type": "string", "description": "The product whose price we want to fetch."}}, "required": ["company", "product"]}}, {"name": "corporate_finance.revenue_forecast", "description": "Estimate the revenue of a company by multiplying the sales units of the product with its selling price.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company that you want to calculate the revenue for."}, "product": {"type": "string", "description": "The product sold by the company."}, "sales_units_increase_percentage": {"type": "integer", "description": "Percentage increase in the sales units. This value is optional and defaults to zero if not provided."}}, "required": ["company", "product"]}}]}
{"id": "multiple_14", "question": [[{"role": "user", "content": "Calculate the depreciated value of a property costing $200,000 with an annual depreciation rate of 3% for 5 years."}]], "function": [{"name": "finance.property_depreciation", "description": "Calculates the depreciated value of a property given its initial cost, depreciation rate, and the number of years.", "parameters": {"type": "dict", "properties": {"initial_cost": {"type": "integer", "description": "The initial cost of the property."}, "depreciation_rate": {"type": "integer", "description": "The annual depreciation rate in percentage."}, "years": {"type": "integer", "description": "The number of years for which to calculate the depreciation."}, "monthly": {"type": "boolean", "description": "If set to true, it will calculate monthly depreciation instead of annually. (optional)", "default": false}}, "required": ["initial_cost", "depreciation_rate", "years"]}}, {"name": "finance.loan_repayment", "description": "Calculates the monthly repayment for a loan.", "parameters": {"type": "dict", "properties": {"loan_amount": {"type": "float", "description": "The amount borrowed or loaned."}, "interest_rate": {"type": "float", "description": "The annual interest rate."}, "loan_term": {"type": "integer", "description": "The term of the loan in years."}}, "required": ["loan_amount", "interest_rate", "loan_term"]}}, {"name": "finance.inflation_adjustment", "description": "Adjusts a sum of money for inflation based on the consumer price index (CPI).", "parameters": {"type": "dict", "properties": {"initial_sum": {"type": "float", "description": "The initial sum of money."}, "years": {"type": "integer", "description": "The number of years over which inflation is calculated."}, "inflation_rate": {"type": "float", "description": "The annual rate of inflation. Default 0.0"}}, "required": ["initial_sum", "years"]}}]}
{"id": "multiple_15", "question": [[{"role": "user", "content": "How much is the potential of the Solar farm at location with coordinates [43.653225, -79.383186] in December, given that it has a total solar panel area of 80000 sq ft?"}]], "function": [{"name": "solarFarm.potential", "description": "Estimate the energy output of a solar farm given its location and panel area for a particular month.", "parameters": {"type": "dict", "properties": {"coordinates": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates of the location of the solar farm."}, "panelArea": {"type": "integer", "description": "The total solar panel area in square feet at the location."}, "month": {"type": "string", "description": "The month for which to calculate the potential energy output. Default to January", "optional": true}}, "required": ["coordinates", "panelArea"]}}, {"name": "windFarm.potential", "description": "Estimate the energy output of a wind farm given its location and turbine count for a particular month.", "parameters": {"type": "dict", "properties": {"coordinates": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates of the location of the wind farm."}, "turbineCount": {"type": "integer", "description": "The total number of wind turbines at the location."}, "month": {"type": "string", "description": "The month for which to calculate the potential energy output. Default to January", "optional": true}}, "required": ["coordinates", "turbineCount"]}}]}
{"id": "multiple_16", "question": [[{"role": "user", "content": "What's the required minimum population size (Ne) for maintaining the genetic diversity of a wild tiger population for the next 100 generations with a probability of 0.95?"}]], "function": [{"name": "species_distribution_modeling.project_range_shift", "description": "Predict the potential future geographic distribution of a species under a specified climate change scenario.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species of animal."}, "climate_scenario": {"type": "string", "description": "The name of the climate change scenario."}, "future_time": {"type": "integer", "description": "The future time in years for the prediction.", "default": 100}}, "required": ["species", "climate_scenario"]}}, {"name": "population_genetics.calculate_ne", "description": "Calculate the effective population size necessary to maintain genetic diversity in a wild animal population for a specified number of generations with a given probability.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species of wild animal."}, "generations": {"type": "integer", "description": "The number of generations for which to maintain the genetic diversity."}, "probability": {"type": "float", "description": "The probability of maintaining genetic diversity."}}, "required": ["species", "generations", "probability"]}}, {"name": "ecology.calculate_carrying_capacity", "description": "Calculate the maximum population size of the species that the environment can sustain indefinitely.", "parameters": {"type": "dict", "properties": {"habitat_area": {"type": "float", "description": "The area of the habitat in square kilometers."}, "species": {"type": "string", "description": "The species of animal."}, "productivity": {"type": "float", "description": "The biological productivity of the habitat in animals per square kilometer per year."}}, "required": ["habitat_area", "species", "productivity"]}}]}
{"id": "multiple_17", "question": [[{"role": "user", "content": "Find the conversion rate from Euro to Dollar at January 1, 2022"}]], "function": [{"name": "currency_conversion.convert", "description": "Converts a specified amount of money from one currency to another at the latest rate.", "parameters": {"type": "dict", "properties": {"from_currency": {"type": "string", "description": "The currency that you want to convert from."}, "to_currency": {"type": "string", "description": "The currency that you want to convert to."}, "amount": {"type": "float", "description": "The amount of money that you want to convert."}}, "required": ["from_currency", "to_currency", "amount"]}}, {"name": "currency_conversion.get_latest_rate", "description": "Get the latest currency conversion rate from one currency to another.", "parameters": {"type": "dict", "properties": {"from_currency": {"type": "string", "description": "The currency that you want to convert from."}, "to_currency": {"type": "string", "description": "The currency that you want to convert to."}}, "required": ["from_currency", "to_currency"]}}, {"name": "currency_conversion.get_rate", "description": "Get the currency conversion rate from one currency to another at a specified date.", "parameters": {"type": "dict", "properties": {"from_currency": {"type": "string", "description": "The currency that you want to convert from."}, "to_currency": {"type": "string", "description": "The currency that you want to convert to."}, "date": {"type": "string", "description": "The date at which the conversion rate applies. Default is the current date.", "default": "today"}}, "required": ["from_currency", "to_currency"]}}]}
{"id": "multiple_18", "question": [[{"role": "user", "content": "Who were the main participants and what was the location of the Battle of Stalingrad?"}]], "function": [{"name": "european_history.war_details", "description": "Get details of a specific historical European war.", "parameters": {"type": "dict", "properties": {"war": {"type": "string", "description": "Name of the war"}}, "required": ["war"]}}, {"name": "european_history.leader_info", "description": "Get information about a specific historical leader in European history.", "parameters": {"type": "dict", "properties": {"leader": {"type": "string", "description": "Name of the leader"}}, "required": ["leader"]}}, {"name": "european_history.battle_details", "description": "Get details of a specific historical European battle.", "parameters": {"type": "dict", "properties": {"battle": {"type": "string", "description": "Name of the battle"}}, "required": ["battle"]}}]}
{"id": "multiple_19", "question": [[{"role": "user", "content": "What are the three great Schism in Christianity history?"}]], "function": [{"name": "religion_history.get_councils", "description": "Retrieves a list of major councils in a specified religion.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "Name of the religion for which to retrieve the councils."}, "count": {"type": "integer", "description": "Number of top councils to retrieve.", "default": 3}}, "required": ["religion", "count"]}}, {"name": "religion_history.get_reformations", "description": "Retrieves a list of major reformations in a specified religion.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "Name of the religion for which to retrieve the reformations."}, "count": {"type": "integer", "description": "Number of top reformations to retrieve.", "default": 3}}, "required": ["religion", "count"]}}, {"name": "religion_history.get_schisms", "description": "Retrieves a list of major schisms in a specified religion.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "Name of the religion for which to retrieve the schisms."}, "count": {"type": "integer", "description": "Number of top schisms to retrieve."}}, "required": ["religion", "count"]}}]}
{"id": "multiple_20", "question": [[{"role": "user", "content": "What is the price to commission a sculpture made of marble with a size of 3 feet?"}]], "function": [{"name": "sculptor_info.get", "description": "Get information about a specific sculptor.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the sculptor."}}, "required": ["name"]}}, {"name": "sculpture_price.calculate", "description": "Calculate the estimated price to commission a sculpture based on the material and size.", "parameters": {"type": "dict", "properties": {"material": {"type": "string", "description": "The material used for the sculpture."}, "size": {"type": "integer", "description": "The size of the sculpture in feet."}, "complexity": {"type": "string", "enum": ["low", "medium", "high"], "description": "The complexity level of the sculpture. Default is 'medium'.", "default": "medium"}}, "required": ["material", "size"]}}, {"name": "sculpture_availability.check", "description": "Check the availability of a specific sculpture in the inventory.", "parameters": {"type": "dict", "properties": {"sculpture_name": {"type": "string", "description": "The name of the sculpture."}, "material": {"type": "string", "description": "The material of the sculpture."}}, "required": ["sculpture_name", "material"]}}]}
{"id": "multiple_21", "question": [[{"role": "user", "content": "I want to generate a sound of 440Hz frequency for 5 seconds. What is the function and how can I use it?"}]], "function": [{"name": "play_sound_wave", "description": "This function is for playing a sound wave file.", "parameters": {"type": "dict", "properties": {"wave_file": {"type": "string", "description": "The filename of the sound wave file to be played."}, "volume": {"type": "float", "description": "The volume level at which the sound is to be played (1 is 100%).", "default": 1}}, "required": ["wave_file"]}}, {"name": "generate_sound_wave", "description": "This function is for generating a sinusoidal sound wave file of a certain frequency for a specific duration and save it to a WAV file.", "parameters": {"type": "dict", "properties": {"frequency": {"type": "integer", "description": "The frequency of the sound wave in Hz."}, "duration": {"type": "integer", "description": "The duration of the sound in seconds."}, "wave_type": {"type": "string", "enum": ["sine", "square", "sawtooth"], "description": "The waveform to be used to generate the sound.", "default": "sine"}}, "required": ["frequency", "duration"]}}]}
{"id": "multiple_22", "question": [[{"role": "user", "content": "What is the record for the most points scored by a single player in an NBA game?"}]], "function": [{"name": "sports_data.basketball.most_points_single_season", "description": "Returns the record for the most points scored by a single player in one season of NBA, including the player name, points scored, and season.", "parameters": {"type": "dict", "properties": {"league": {"type": "string", "description": "The specific basketball league for which to fetch the record. In this case, 'NBA'."}}, "required": ["league"]}}, {"name": "sports_data.basketball.most_points_career", "description": "Returns the record for the most points scored by a player in his career in NBA, including the player name, total points scored, and career span.", "parameters": {"type": "dict", "properties": {"league": {"type": "string", "description": "The specific basketball league for which to fetch the record. In this case, 'NBA'."}}, "required": ["league"]}}, {"name": "sports_data.basketball.most_points_single_game", "description": "Returns the record for the most points scored by a single player in one game of NBA, including the player name, points scored, and game date.", "parameters": {"type": "dict", "properties": {"league": {"type": "string", "description": "The specific basketball league for which to fetch the record. In this case, 'NBA'."}}, "required": ["league"]}}]}
{"id": "multiple_23", "question": [[{"role": "user", "content": "What are the current stats for basketball player LeBron James including points per game, assists, and minutes per game."}]], "function": [{"name": "basketball.player_stats.get", "description": "Get current statistics for a specified basketball player", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the player."}, "stats_fields": {"type": "array", "description": "List of statistical categories to be fetched, including points, assists, rebounds, minutes.", "items": {"type": "string"}}}, "required": ["player_name", "stats_fields"]}}, {"name": "basketball.game_stats.get", "description": "Get the detailed statistical data from a specific basketball game", "parameters": {"type": "dict", "properties": {"team1": {"type": "string", "description": "One of the competing teams in the game."}, "team2": {"type": "string", "description": "One of the competing teams in the game."}, "date": {"type": "string", "description": "The date when the game occurred."}, "stats_fields": {"type": "array", "description": "List of statistical categories to be fetched, including total points, total assists, total rebounds, turnovers. Default to empty list", "items": {"type": "string"}}}, "required": ["team1", "team2", "date"]}}, {"name": "basketball.team_stats.get", "description": "Get current statistics for a specific basketball team", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the team."}, "stats_fields": {"type": "array", "description": "List of statistical categories to be fetched, including total points, total assists, total rebounds, win rate.", "items": {"type": "string"}}}, "required": ["team_name", "stats_fields"]}}]}
{"id": "multiple_24", "question": [[{"role": "user", "content": "What is the fastest route from London to Edinburgh for playing a chess championship? Also provide an estimate of the distance."}]], "function": [{"name": "route_planner.calculate_route", "description": "Determines the best route between two points.", "parameters": {"type": "dict", "properties": {"start": {"type": "string", "description": "The starting point of the journey."}, "destination": {"type": "string", "description": "The destination of the journey."}, "method": {"type": "string", "enum": ["fastest", "shortest", "balanced"], "description": "The method to use when calculating the route (default is 'fastest').", "default": "fastest"}}, "required": ["start", "destination"]}}, {"name": "chess_club_details.find", "description": "Provides details about a chess club, including location.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the chess club."}, "city": {"type": "string", "description": "The city in which the chess club is located."}, "event": {"type": "string", "description": "The event hosted by the club.", "default": "null"}}, "required": ["name", "city"]}}]}
{"id": "multiple_25", "question": [[{"role": "user", "content": "What is the cheapest selling price for the game 'Assassins Creed Valhalla' in the PlayStation Store in the United States?"}]], "function": [{"name": "video_games.store_currency", "description": "Fetches the currency used in a specific region in a gaming platform store.", "parameters": {"type": "dict", "properties": {"platform": {"type": "string", "description": "The gaming platform e.g. PlayStation, Xbox, Nintendo Switch"}, "region": {"type": "string", "description": "The region e.g. United States, United Kingdom, Japan", "default": "True"}}, "required": ["platform"]}}, {"name": "video_games.on_sale", "description": "Checks if a particular game is currently on sale in a specific gaming platform store and in a specific region.", "parameters": {"type": "dict", "properties": {"game_title": {"type": "string", "description": "The title of the video game"}, "platform": {"type": "string", "description": "The gaming platform e.g. PlayStation, Xbox, Nintendo Switch"}, "region": {"type": "string", "description": "The region e.g. United States, United Kingdom, Japan. Default United States", "optional": "True"}}, "required": ["game_title", "platform"]}}, {"name": "video_games.store_price", "description": "Fetches the selling price of a specified game in a particular gaming platform store and in a specific region.", "parameters": {"type": "dict", "properties": {"game_title": {"type": "string", "description": "The title of the video game"}, "platform": {"type": "string", "description": "The gaming platform e.g. PlayStation, Xbox, Nintendo Switch"}, "region": {"type": "string", "description": "The region e.g. United States, United Kingdom, Japan. Default to United States"}}, "required": ["game_title", "platform"]}}]}
{"id": "multiple_26", "question": [[{"role": "user", "content": "Find out the rewards for playing Fortnite on Playstation platform with different missions and trophies"}]], "function": [{"name": "game_scores.get", "description": "Retrieve scores and rankings based on player\u2019s performance in a certain game.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the game."}, "platform": {"type": "string", "description": "The gaming platform e.g. Xbox, Playstation, PC"}, "level": {"type": "integer", "description": "The level of the game for which you want to retrieve the scores."}, "player": {"type": "string", "description": "The name of the player for whom you want to retrieve scores. Default ''", "optional": true}}, "required": ["game", "platform", "level"]}}, {"name": "game_rewards.get", "description": "Retrieve information about different types of rewards that you can receive when playing a certain game.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the game."}, "platform": {"type": "string", "description": "The gaming platform e.g. Xbox, Playstation, PC"}, "mission": {"type": "string", "description": "The mission for which you want to know the rewards. Default to ''", "optional": true}, "trophy": {"type": "string", "description": "The trophy level for which you want to know the rewards. Default to ''", "optional": true}}, "required": ["game", "platform"]}}, {"name": "game_missions.list", "description": "List all missions for a certain game.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the game."}}, "required": ["game"]}}]}
{"id": "multiple_27", "question": [[{"role": "user", "content": "What is the shortest path from Paris, France to Rome, Italy by using a public transportation?"}]], "function": [{"name": "maps.route_times", "description": "Estimates the time it will take to travel from one location to another by a specific mode of transportation.", "parameters": {"type": "dict", "properties": {"route": {"type": "string", "description": "The string representation of the route."}, "mode": {"type": "string", "description": "The mode of transportation (walk, bike, transit, drive).", "default": "walk"}}, "required": ["route"]}}, {"name": "maps.shortest_path", "description": "Find the shortest path from one location to another by using a specific mode of transportation.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The name or coordinates of the start location."}, "end_location": {"type": "string", "description": "The name or coordinates of the end location."}, "mode": {"type": "string", "description": "The mode of transportation (walk, bike, transit, drive).", "default": "walk"}}, "required": ["start_location", "end_location"]}}]}
{"id": "multiple_28", "question": [[{"role": "user", "content": "What's the root of quadratic equation with coefficients 2, 3 and -4?"}]], "function": [{"name": "solve.quadratic_equation", "description": "Solve a quadratic equation with given coefficients a, b, and c.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of x^2."}, "b": {"type": "integer", "description": "Coefficient of x."}, "c": {"type": "integer", "description": "Constant term."}}, "required": ["a", "b", "c"]}}, {"name": "convert.rgb_to_hex", "description": "Converts RGB values to Hexadecimal color code.", "parameters": {"type": "dict", "properties": {"r": {"type": "integer", "description": "The Red component."}, "g": {"type": "integer", "description": "The Green component."}, "b": {"type": "integer", "description": "The Blue component."}}, "required": ["r", "g", "b"]}}, {"name": "perform.string_reverse", "description": "Reverses a given string.", "parameters": {"type": "dict", "properties": {"input_string": {"type": "string", "description": "The string to be reversed."}}, "required": ["input_string"]}}]}
{"id": "multiple_29", "question": [[{"role": "user", "content": "Find the intersection points of the functions y=3x+2 and y=2x+3."}]], "function": [{"name": "functions.zero", "description": "Find the zero points of a function.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "Function given as a string with x as the variable, e.g. 3x+2"}}, "required": ["function"]}}, {"name": "functions.intersect", "description": "Locate the intersection points of two functions.", "parameters": {"type": "dict", "properties": {"function1": {"type": "string", "description": "First function given as a string with x as the variable, e.g. 3x+2"}, "function2": {"type": "string", "description": "Second function given as a string with x as the variable, e.g. 2x+3"}}, "required": ["function1", "function2"]}}]}
{"id": "multiple_30", "question": [[{"role": "user", "content": "What is the area of a rectangle with length 12 meters and width 5 meters?"}]], "function": [{"name": "rectangle.area", "description": "Calculate the area of a rectangle with given length and width", "parameters": {"type": "dict", "properties": {"length": {"type": "integer", "description": "Length of the rectangle"}, "width": {"type": "integer", "description": "Width of the rectangle"}}, "required": ["length", "width"]}}, {"name": "circle.area", "description": "Calculate the area of a circle with given radius", "parameters": {"type": "dict", "properties": {"radius": {"type": "float", "description": "Radius of the circle"}, "isDiameter": {"type": "boolean", "description": "Whether the given length is the diameter of the circle, default is false", "default": false}}, "required": ["radius"]}}, {"name": "triangle.area", "description": "Calculate the area of a triangle with given base and height", "parameters": {"type": "dict", "properties": {"base": {"type": "float", "description": "Base of the triangle"}, "height": {"type": "float", "description": "Height of the triangle"}}, "required": ["base", "height"]}}]}
{"id": "multiple_31", "question": [[{"role": "user", "content": "What is the area and perimeter of a rectangle with width of 7 units and length of 10 units?"}]], "function": [{"name": "geometry_square.calculate", "description": "Calculates the area and perimeter of a square given the side length.", "parameters": {"type": "dict", "properties": {"side": {"type": "integer", "description": "The length of a side of the square."}}, "required": ["side"]}}, {"name": "geometry_circle.calculate", "description": "Calculates the area and circumference of a circle given the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}}, "required": ["radius"]}}, {"name": "geometry_rectangle.calculate", "description": "Calculates the area and perimeter of a rectangle given the width and length.", "parameters": {"type": "dict", "properties": {"width": {"type": "integer", "description": "The width of the rectangle."}, "length": {"type": "integer", "description": "The length of the rectangle."}}, "required": ["width", "length"]}}]}
{"id": "multiple_32", "question": [[{"role": "user", "content": "Calculate the volume of a cone with radius 4 and height 7."}]], "function": [{"name": "geometry.calculate_cone_volume", "description": "Calculate the volume of a cone given the radius and height.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "Radius of the cone base."}, "height": {"type": "integer", "description": "Height of the cone."}, "round_off": {"type": "integer", "description": "Number of decimal places to round off the answer. Default 0"}}, "required": ["radius", "height"]}}, {"name": "physics.calculate_cone_mass", "description": "Calculate the mass of a cone given the radius, height, and density.", "parameters": {"type": "dict", "properties": {"radius": {"type": "float", "description": "Radius of the cone base."}, "height": {"type": "float", "description": "Height of the cone."}, "density": {"type": "float", "description": "Density of the material the cone is made of."}}, "required": ["radius", "height", "density"]}}]}
{"id": "multiple_33", "question": [[{"role": "user", "content": "Find the integral of the function f(x) = 3x^2 from 1 to 2."}]], "function": [{"name": "calculate_derivative", "description": "Calculate the derivative of a single-variable function.", "parameters": {"type": "dict", "properties": {"func": {"type": "string", "description": "The function to be differentiated."}, "x_value": {"type": "integer", "description": "The x-value at which the derivative should be calculated."}, "order": {"type": "integer", "description": "The order of the derivative (optional). Default is 1st order.", "default": 1}}, "required": ["func", "x_value"]}}, {"name": "calculate_integral", "description": "Calculate the definite integral of a single-variable function.", "parameters": {"type": "dict", "properties": {"func": {"type": "string", "description": "The function to be integrated."}, "a": {"type": "integer", "description": "The lower bound of the integration."}, "b": {"type": "integer", "description": "The upper bound of the integration."}}, "required": ["func", "a", "b"]}}]}
{"id": "multiple_34", "question": [[{"role": "user", "content": "Calculate the Least Common Multiple (LCM) of 18 and 12."}]], "function": [{"name": "math.gcd", "description": "Calculates the greatest common divisor of two numbers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "The first number."}, "num2": {"type": "integer", "description": "The second number."}}, "required": ["num1", "num2"]}}, {"name": "math.sqrt", "description": "Calculates the square root of a number.", "parameters": {"type": "dict", "properties": {"num": {"type": "float", "description": "The number."}, "accuracy": {"type": "integer", "description": "The number of decimal places in the result. Default to 0", "optional": true}}, "required": ["num"]}}, {"name": "math.lcm", "description": "Calculates the least common multiple of two numbers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "The first number."}, "num2": {"type": "integer", "description": "The second number."}}, "required": ["num1", "num2"]}}]}
{"id": "multiple_35", "question": [[{"role": "user", "content": "Calculate the greatest common divisor between 128 and 256."}]], "function": [{"name": "calculate_lcm", "description": "Calculate the least common multiple (lcm) between two integers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "First number to calculate lcm for."}, "num2": {"type": "integer", "description": "Second number to calculate lcm for."}, "method": {"type": "string", "description": "The specific method to use in the calculation. Supported values: 'standard', 'reduced'. Default 'standard'"}}, "required": ["num1", "num2"]}}, {"name": "calculate_gcd", "description": "Calculate the greatest common divisor (gcd) between two integers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "First number to calculate gcd for."}, "num2": {"type": "integer", "description": "Second number to calculate gcd for."}, "algorithm": {"type": "string", "description": "The specific algorithm to use in the calculation. Supported values: 'euclidean', 'binary'. Default 'euclidean'", "enum": ["euclidean", "binary"]}}, "required": ["num1", "num2"]}}]}
{"id": "multiple_36", "question": [[{"role": "user", "content": "Find out how fast an object was going if it started from rest and traveled a distance of 20 meters over 4 seconds due to a constant acceleration?"}]], "function": [{"name": "kinematics.calculate_acceleration", "description": "Calculates the acceleration of an object under given conditions.", "parameters": {"type": "dict", "properties": {"initial_speed": {"type": "float", "description": "The initial speed of the object."}, "final_speed": {"type": "float", "description": "The final speed of the object."}, "time": {"type": "float", "description": "The time in seconds it took the object to reach the final speed."}, "distance": {"type": "float", "description": "The distance in meters the object has traveled.", "default": 0}}, "required": ["initial_speed", "final_speed", "time"]}}, {"name": "kinematics.calculate_speed_from_rest", "description": "Calculates the speed of an object that starts from rest under a constant acceleration over a specified distance.", "parameters": {"type": "dict", "properties": {"distance": {"type": "integer", "description": "The distance in meters the object has traveled."}, "time": {"type": "integer", "description": "The time in seconds it took the object to travel."}, "initial_speed": {"type": "integer", "description": "The initial speed of the object.", "default": 0}}, "required": ["distance", "time"]}}]}
{"id": "multiple_37", "question": [[{"role": "user", "content": "Find the final velocity of an object thrown up at 40 m/s after 6 seconds."}]], "function": [{"name": "physics.wave_velocity", "description": "Calculate the velocity of a wave based on its frequency and wavelength.", "parameters": {"type": "dict", "properties": {"frequency": {"type": "float", "description": "The frequency of the wave in Hz."}, "wavelength": {"type": "float", "description": "The wavelength of the wave in m."}}, "required": ["frequency", "wavelength"]}}, {"name": "kinematics.final_velocity", "description": "Find the final velocity of an object moving under constant acceleration.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "integer", "description": "The initial velocity of the object in m/s."}, "time": {"type": "integer", "description": "The time in seconds the object has been moving."}, "acceleration": {"type": "float", "description": "The acceleration of the object in m/s^2. Default value is -9.81 (Earth's gravity)"}}, "required": ["initial_velocity", "time"]}}, {"name": "kinematics.distance", "description": "Find the distance traveled by an object moving under constant acceleration.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "float", "description": "The initial velocity of the object in m/s."}, "time": {"type": "integer", "description": "The time in seconds the object has been moving."}, "acceleration": {"type": "float", "description": "The acceleration of the object in m/s^2. Default value is -9.81 (Earth's gravity)"}}, "required": ["initial_velocity", "time"]}}]}
{"id": "multiple_38", "question": [[{"role": "user", "content": "Find a book 'The Alchemist' in the library branches within New York city."}]], "function": [{"name": "library.reserve_book", "description": "Reserves a book in the library if available.", "parameters": {"type": "dict", "properties": {"book_id": {"type": "string", "description": "The id of the book to reserve."}, "branch_id": {"type": "string", "description": "The id of the library branch to reserve from."}, "return_date": {"type": "string", "description": "The date the book is to be returned (optional). Default is ''"}}, "required": ["book_id", "branch_id"]}}, {"name": "library.search_book", "description": "Searches for a book in the library within the specified city.", "parameters": {"type": "dict", "properties": {"book_name": {"type": "string", "description": "The name of the book to search for."}, "city": {"type": "string", "description": "The city to search within."}, "availability": {"type": "boolean", "description": "If true, search for available copies. If false or omitted, search for any copy regardless of availability. Default is false"}, "genre": {"type": "string", "description": "The genre of the book to filter search (optional). Default is ''"}}, "required": ["book_name", "city"]}}]}
{"id": "multiple_39", "question": [[{"role": "user", "content": "Find a ride from New York to Philadelphia with maximum cost of $50"}]], "function": [{"name": "grocery_delivery.order", "description": "Order grocery items from a specific location with optional delivery price limit", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the grocery store"}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order"}, "max_delivery_cost": {"type": "float", "description": "The maximum delivery cost. It is optional. Default 1000000"}}, "required": ["location", "items"]}}, {"name": "ride_hailing.get_rides", "description": "Find ride from source to destination with an optional cost limit", "parameters": {"type": "dict", "properties": {"source": {"type": "string", "description": "The starting point of the journey"}, "destination": {"type": "string", "description": "The endpoint of the journey"}, "max_cost": {"type": "integer", "description": "The maximum cost of the ride. It is optional. Default is 1000000"}}, "required": ["source", "destination"]}}]}
{"id": "multiple_40", "question": [[{"role": "user", "content": "Calculate the strength of magnetic field given distance is 8 meters and current is 12 Amperes?"}]], "function": [{"name": "electromagnetism.ampere_law", "description": "Calculate magnetic field strength using Ampere's Circuital Law. Input the current enclosed by a circular path and the distance from the center of the circle. Can be applied to a cylindrical or spherical symmetry of consistent magnetic field. ", "parameters": {"type": "dict", "properties": {"enclosed_current": {"type": "float", "description": "The total current enclosed by the loop. In Amperes."}, "radius": {"type": "float", "description": "The radius of the circle or the distance from the center of the circular path. In meters."}, "mu0": {"type": "float", "description": "Permeability of free space. Its value is default approximated to be 0.000001256 H/m. Optional"}}, "required": ["enclosed_current", "radius"]}}, {"name": "electromagnetism.biot_savart_law", "description": "Calculate magnetic field strength using Biot-Savart law. Input the current in Ampere and the distance in meters.", "parameters": {"type": "dict", "properties": {"current": {"type": "integer", "description": "The current in the conductor, in Amperes."}, "distance": {"type": "integer", "description": "Distance from the current carrying conductor, in meters."}, "mu0": {"type": "float", "description": "Permeability of free space. Its value is default approximated to be 0.000001256 H/m. Optional"}}, "required": ["current", "distance"]}}]}
{"id": "multiple_41", "question": [[{"role": "user", "content": "Calculate the magnetic field at point P using Ampere\u2019s law where current I is 10 Amperes and r is 0.01 meter."}]], "function": [{"name": "electric_field.calculate", "description": "Calculate the electric field based on the amount of charge and distance from the charge", "parameters": {"type": "dict", "properties": {"Q": {"type": "float", "description": "The amount of charge in coulombs."}, "r": {"type": "float", "description": "The distance from the charge in meters."}}, "required": ["Q", "r"]}}, {"name": "magnetic_field.calculate", "description": "Calculate the magnetic field based on the current flowing and the radial distance using Ampere\u2019s law", "parameters": {"type": "dict", "properties": {"I": {"type": "integer", "description": "The electric current flowing in Amperes."}, "r": {"type": "float", "description": "The radial distance from the line of current in meters."}}, "required": ["I", "r"]}}, {"name": "electric_force.calculate", "description": "Calculate the electric force between two charges at a distance", "parameters": {"type": "dict", "properties": {"Q1": {"type": "float", "description": "The amount of the first charge in coulombs."}, "Q2": {"type": "float", "description": "The amount of the second charge in coulombs."}, "r": {"type": "float", "description": "The distance between the two charges in meters."}}, "required": ["Q1", "Q2", "r"]}}]}
{"id": "multiple_42", "question": [[{"role": "user", "content": "Calculate the final temperature when 2 moles of gas at 300 K are mixed with 3 moles of the same gas at 400 K."}]], "function": [{"name": "calculate_final_temperature", "description": "Calculate the final temperature when different quantities of the same gas at different temperatures are mixed.", "parameters": {"type": "dict", "properties": {"quantity1": {"type": "integer", "description": "The quantity of the first sample of gas."}, "temperature1": {"type": "integer", "description": "The temperature of the first sample of gas."}, "quantity2": {"type": "integer", "description": "The quantity of the second sample of gas."}, "temperature2": {"type": "integer", "description": "The temperature of the second sample of gas."}}, "required": ["quantity1", "temperature1", "quantity2", "temperature2"]}}, {"name": "calculate_mass", "description": "Calculate the mass of a gas given its quantity and molar mass.", "parameters": {"type": "dict", "properties": {"quantity": {"type": "integer", "description": "The quantity of the gas."}, "molar_mass": {"type": "integer", "description": "The molar mass of the gas."}}, "required": ["quantity", "molar_mass"]}}]}
{"id": "multiple_43", "question": [[{"role": "user", "content": "What is the energy produced by 5 mol of glucose (C6H12O6)?"}]], "function": [{"name": "biological.calc_biomass", "description": "Calculate the biomass from the energy given the energy conversion efficiency.", "parameters": {"type": "dict", "properties": {"energy": {"type": "float", "description": "The total energy produced."}, "efficiency": {"type": "float", "description": "The conversion efficiency, default value is 10%.", "default": 0.1}}, "required": ["energy"]}}, {"name": "biological.calc_energy", "description": "Calculate energy from amount of substance based on its molecular composition.", "parameters": {"type": "dict", "properties": {"mols": {"type": "integer", "description": "Amount of substance in moles."}, "substance": {"type": "string", "description": "The chemical formula of the substance."}, "joules_per_mol": {"type": "integer", "description": "The energy produced or required for the reaction, default value for glucose is 2800 kJ/mol", "default": 2800}}, "required": ["mols", "substance"]}}, {"name": "physical.calc_work", "description": "Calculate the work from energy.", "parameters": {"type": "dict", "properties": {"energy": {"type": "float", "description": "The total energy produced."}, "distance": {"type": "float", "description": "The distance over which the work is done."}}, "required": ["energy", "distance"]}}]}
{"id": "multiple_44", "question": [[{"role": "user", "content": "How much will I weigh on Mars if my weight on Earth is 70 kg?"}]], "function": [{"name": "unit_conversion.convert", "description": "Convert a value from one unit to another.", "parameters": {"type": "dict", "properties": {"value": {"type": "float", "description": "The value to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from."}, "to_unit": {"type": "string", "description": "The unit to convert to."}}, "required": ["value", "from_unit", "to_unit"]}}, {"name": "currency_conversion", "description": "Convert a value from one currency to another.", "parameters": {"type": "dict", "properties": {"amount": {"type": "float", "description": "The amount to be converted."}, "from_currency": {"type": "string", "description": "The currency to convert from."}, "to_currency": {"type": "string", "description": "The currency to convert to."}}, "required": ["amount", "from_currency", "to_currency"]}}, {"name": "calculate.weight_in_space", "description": "Calculate your weight on different planets given your weight on earth", "parameters": {"type": "dict", "properties": {"weight_earth_kg": {"type": "integer", "description": "Your weight on Earth in Kilograms."}, "planet": {"type": "string", "description": "The planet you want to know your weight on."}}, "required": ["weight_earth_kg", "planet"]}}]}
{"id": "multiple_45", "question": [[{"role": "user", "content": "Calculate how many years ago was the Ice age?"}]], "function": [{"name": "geology.get_era", "description": "Get the estimated date of a geological era.", "parameters": {"type": "dict", "properties": {"era_name": {"type": "string", "description": "The name of the geological era. e.g Ice age"}, "calculate_years_ago": {"type": "boolean", "description": "True if years ago is to be calculated. False by default"}}, "required": ["era_name"]}}, {"name": "history.get_event_date", "description": "Get the date of an historical event.", "parameters": {"type": "dict", "properties": {"event_name": {"type": "string", "description": "The name of the event."}, "calculate_years_ago": {"type": "boolean", "description": "True if years ago is to be calculated. False by default"}}, "required": ["event_name"]}}]}
{"id": "multiple_46", "question": [[{"role": "user", "content": "Sort this list of names in ascending order: ['Sam', 'Alice', 'Jack']"}]], "function": [{"name": "filter_list", "description": "Filters elements of a list based on a given condition", "parameters": {"type": "dict", "properties": {"elements": {"type": "array", "items": {"type": "string"}, "description": "The list of elements to filter."}, "condition": {"type": "string", "description": "The condition to filter the elements on."}}, "required": ["elements", "condition"]}}, {"name": "sum_elements", "description": "Add all elements of a numeric list", "parameters": {"type": "dict", "properties": {"elements": {"type": "array", "items": {"type": "integer"}, "description": "The list of numeric elements to add."}}, "required": ["elements"]}}, {"name": "sort_list", "description": "Sort the elements of a list in ascending or descending order", "parameters": {"type": "dict", "properties": {"elements": {"type": "array", "items": {"type": "string"}, "description": "The list of elements to sort."}, "order": {"type": "string", "description": "The order in which to sort the elements. This can be 'asc' for ascending order, or 'desc' for descending order.", "default": "asc"}}, "required": ["elements"]}}]}
{"id": "multiple_47", "question": [[{"role": "user", "content": "Calculate the cosine similarity between vector A [3, 2, 1] and vector B [1, 2, 3]."}]], "function": [{"name": "cosine_similarity.calculate", "description": "Calculate the cosine similarity between two vectors.", "parameters": {"type": "dict", "properties": {"vector1": {"type": "array", "items": {"type": "integer"}, "description": "The first vector for calculating cosine similarity."}, "vector2": {"type": "array", "items": {"type": "integer"}, "description": "The second vector for calculating cosine similarity."}, "rounding": {"type": "integer", "description": "Optional: The number of decimals to round off the result. Default 0"}}, "required": ["vector1", "vector2"]}}, {"name": "correlation.calculate", "description": "Calculate the correlation coefficient between two arrays of numbers.", "parameters": {"type": "dict", "properties": {"array1": {"type": "array", "items": {"type": "integer"}, "description": "The first array of numbers."}, "array2": {"type": "array", "items": {"type": "integer"}, "description": "The second array of numbers."}, "type": {"type": "string", "enum": ["pearson", "spearman"], "description": "Optional: The type of correlation coefficient to calculate. Default is 'pearson'."}}, "required": ["array1", "array2"]}}]}
{"id": "multiple_48", "question": [[{"role": "user", "content": "Find me a pet-friendly library with facilities for disabled people in New York City."}]], "function": [{"name": "store.find_nearby", "description": "Locate nearby stores based on specific preferences such as being pet-friendly and having disabled access facilities.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city, for example, New York City, NY"}, "preferences": {"type": "array", "items": {"type": "string", "enum": ["Pet-friendly", "Disabled Access", "24 hours", "Cafe Inside"]}, "description": "Your preferences for the store."}}, "required": ["location", "preferences"]}}, {"name": "library.find_nearby", "description": "Locate nearby libraries based on specific preferences such as being pet-friendly and having disabled access facilities.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city, for example, New York City, NY"}, "preferences": {"type": "array", "items": {"type": "string", "enum": ["Pet-friendly", "Disabled Access", "24 hours", "Cafe Inside"]}, "description": "Your preferences for the library."}}, "required": ["location", "preferences"]}}]}
{"id": "multiple_49", "question": [[{"role": "user", "content": "Calculate the compound interest for an amount of 1500 for a duration of 2 years with an annual interest rate of 2.5%."}]], "function": [{"name": "calc_Compound_Interest", "description": "Compute compound interest.", "parameters": {"type": "dict", "properties": {"principle_amount": {"type": "integer", "description": "The principle amount that is invested."}, "duration": {"type": "integer", "description": "Duration of time period in years."}, "annual_rate": {"type": "float", "description": "Interest rate in percentage."}, "compound_freq": {"type": "integer", "default": 1, "description": "The number of times that interest is compounded per unit time."}}, "required": ["principle_amount", "duration", "annual_rate"]}}, {"name": "future_value", "description": "Calculates the future value of an investment given an interest rate and time period.", "parameters": {"type": "dict", "properties": {"initial_investment": {"type": "float", "description": "The initial investment amount."}, "interest_rate": {"type": "float", "description": "The annual interest rate (as a decimal)."}, "time": {"type": "integer", "description": "The number of time periods the money is invested for."}, "num_compoundings": {"type": "integer", "default": 1, "description": "The number of times that interest is compounded per time period."}}, "required": ["initial_investment", "interest_rate", "time"]}}, {"name": "calc_Simple_Interest", "description": "Compute simple interest.", "parameters": {"type": "dict", "properties": {"principle_amount": {"type": "float", "description": "The principle amount that is invested."}, "duration": {"type": "float", "description": "Duration of time period in years."}, "annual_rate": {"type": "float", "description": "Interest rate in percentage."}}, "required": ["principle_amount", "duration", "annual_rate"]}}]}
{"id": "multiple_50", "question": [[{"role": "user", "content": "Predict the house prices for the next month in New York."}]], "function": [{"name": "house_price_forecast", "description": "Predict the house prices for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the house price prediction for."}, "months": {"type": "integer", "description": "Number of future months for the prediction."}, "features": {"type": "array", "items": {"type": "string", "enum": ["SqFt", "Bedrooms", "Bathrooms", "Location"]}, "description": "Additional features considered for prediction. Not required. Default empty array", "optional": true}}, "required": ["location", "months"]}}, {"name": "weather_forecast", "description": "Retrieve a weather forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the weather for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}}, "required": ["location", "days"]}}, {"name": "stock_market_forecast", "description": "Predict the stock prices for a specific company and time frame.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company that you want to get the stock price prediction for."}, "days": {"type": "integer", "description": "Number of future days for the prediction."}}, "required": ["company", "days"]}}]}
{"id": "multiple_51", "question": [[{"role": "user", "content": "Calculate the probability of rolling a sum of 7 on a roll of two dice."}]], "function": [{"name": "dice_roll_probability", "description": "Calculate the probability of a specific sum appearing from rolling two dice.", "parameters": {"type": "dict", "properties": {"desired_sum": {"type": "integer", "description": "The sum for which to calculate the probability."}, "n_rolls": {"type": "integer", "description": "Number of dice to be rolled. Default is 1", "optional": true}, "sides_per_die": {"type": "integer", "description": "Number of sides on each die."}}, "required": ["desired_sum", "sides_per_die"]}}, {"name": "flip_coin_probability", "description": "Calculate the probability of a specific outcome appearing from flipping a coin.", "parameters": {"type": "dict", "properties": {"desired_outcome": {"type": "string", "description": "The outcome for which to calculate the probability."}, "n_flips": {"type": "integer", "description": "Number of coins to be flipped. Default 1", "optional": true}}, "required": ["desired_outcome"]}}, {"name": "shuffle_card_probability", "description": "Calculate the probability of a specific card appearing from a shuffled deck.", "parameters": {"type": "dict", "properties": {"desired_card": {"type": "string", "description": "The card for which to calculate the probability."}, "n_decks": {"type": "integer", "description": "Number of decks to shuffle. Default 1", "optional": true}}, "required": ["desired_card"]}}]}
{"id": "multiple_52", "question": [[{"role": "user", "content": "I have 100 euro. How much is it in USD?"}]], "function": [{"name": "unit_conversion", "description": "Convert a value from one unit to another.", "parameters": {"type": "dict", "properties": {"value": {"type": "float", "description": "The value to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from."}, "to_unit": {"type": "string", "description": "The unit to convert to."}}, "required": ["value", "from_unit", "to_unit"]}}, {"name": "currency_conversion", "description": "Convert a value from one currency to another.", "parameters": {"type": "dict", "properties": {"amount": {"type": "integer", "description": "The amount to be converted."}, "from_currency": {"type": "string", "description": "The currency to convert from."}, "to_currency": {"type": "string", "description": "The currency to convert to."}}, "required": ["amount", "from_currency", "to_currency"]}}]}
{"id": "multiple_53", "question": [[{"role": "user", "content": "Predict the house prices for next 5 years based on interest rates and unemployment rates."}]], "function": [{"name": "linear_regression", "description": "Applies linear regression to a given set of independent variables to make a prediction.", "parameters": {"type": "dict", "properties": {"independent_var": {"type": "array", "items": {"type": "string"}, "description": "The independent variables."}, "dependent_var": {"type": "string", "description": "The dependent variable."}, "forecast_period": {"type": "integer", "description": "The number of years to forecast the prices. Default 1", "optional": true}}, "required": ["independent_var", "dependent_var"]}}, {"name": "random_forest_regression", "description": "Applies Random Forest Regression to a given set of independent variables to make a prediction.", "parameters": {"type": "dict", "properties": {"independent_var": {"type": "array", "items": {"type": "string"}, "description": "The independent variables."}, "dependent_var": {"type": "string", "description": "The dependent variable."}, "n_estimators": {"type": "integer", "description": "The number of trees in the forest. Default 1", "optional": true}, "forecast_period": {"type": "integer", "description": "The number of years to forecast the prices. Default 1", "optional": true}}, "required": ["independent_var", "dependent_var"]}}]}
{"id": "multiple_54", "question": [[{"role": "user", "content": "Find out the historical dividend payments of Apple Inc for last five years."}]], "function": [{"name": "corporate_finance.dividend_data", "description": "Get historical dividend data of a specific company within a particular duration.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company that you want to get the dividend data for."}, "years": {"type": "integer", "description": "Number of past years for which to retrieve the data."}, "frequency": {"type": "string", "enum": ["quarterly", "annually"], "description": "The frequency of the dividend payment. Default annually"}}, "required": ["company", "years"]}}, {"name": "stock_market_data", "description": "Retrieve stock market data for a specific company and time frame.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company that you want to get the stock market data for."}, "days": {"type": "integer", "description": "Number of past days for which to retrieve the data."}}, "required": ["company", "days"]}}]}
{"id": "multiple_55", "question": [[{"role": "user", "content": "Predict the stock price for Google for the next 3 days."}]], "function": [{"name": "stock_forecast", "description": "Predict the future stock price for a specific company and time frame.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company that you want to get the stock price prediction for."}, "days": {"type": "integer", "description": "Number of future days for which to predict the stock price."}, "model": {"type": "string", "description": "The model to use for prediction. Default 'regression'"}}, "required": ["company", "days"]}}, {"name": "weather_forecast", "description": "Retrieve a weather forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the weather for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}}, "required": ["location", "days"]}}]}
{"id": "multiple_56", "question": [[{"role": "user", "content": "Find the average closing price of Apple stock in the past 60 days"}]], "function": [{"name": "volume_traded", "description": "Calculate the total volume of stocks traded over a certain period of time", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "Name of the company to get data for"}, "days": {"type": "integer", "description": "Number of past days to calculate volume traded for"}, "data_source": {"type": "string", "description": "Source to fetch the financial data. default is 'yahoo finance'"}}, "required": ["company", "days"]}}, {"name": "total_revenue", "description": "Calculate the total revenue of a company over a specific period of time", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "Name of the company to get data for"}, "days": {"type": "integer", "description": "Number of past days to calculate total revenue for"}, "data_source": {"type": "string", "description": "Source to fetch the financial data. default is 'google finance'"}}, "required": ["company", "days"]}}, {"name": "avg_closing_price", "description": "Calculate the average closing price of a specific company over a given period of time", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "Name of the company to get data for"}, "days": {"type": "integer", "description": "Number of past days to calculate average closing price for"}, "data_source": {"type": "string", "description": "Source to fetch the stock data. default is 'yahoo finance'"}}, "required": ["company", "days"]}}]}
{"id": "multiple_57", "question": [[{"role": "user", "content": "Can you please calculate the compound interest for a principle of $1000, annual rate of 5% over 10 years with 4 compound per year."}]], "function": [{"name": "financial.compound_interest", "description": "Calculates compound interest.", "parameters": {"type": "dict", "properties": {"principle": {"type": "integer", "description": "The initial amount of money that is being compounded."}, "rate": {"type": "float", "description": "The annual interest rate, as a decimal. E.g., an annual interest rate of 5% would be represented as 0.05."}, "time": {"type": "integer", "description": "The amount of time, in years, that the money is to be compounded for."}, "n": {"type": "integer", "description": "The number of times interest applied per time period."}}, "required": ["principle", "rate", "time", "n"]}}, {"name": "financial.simple_interest", "description": "Calculates simple interest.", "parameters": {"type": "dict", "properties": {"principle": {"type": "float", "description": "The initial amount of money that interest is being calculated for."}, "rate": {"type": "float", "description": "The annual interest rate, as a decimal. E.g., an annual interest rate of 5% would be represented as 0.05."}, "time": {"type": "integer", "description": "The amount of time, in years, that the money is to be compounded for."}}, "required": ["principle", "rate", "time"]}}]}
{"id": "multiple_58", "question": [[{"role": "user", "content": "Search for divorce law specialists in Los Angeles"}]], "function": [{"name": "doctor.search", "description": "Search for a doctor based on area of expertise and location", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Los Angeles, CA"}, "specialization": {"type": "string", "description": "Medical specialization. For example, 'Cardiology', 'Orthopedics', 'Gynecology'."}}, "required": ["location", "specialization"]}}, {"name": "lawyer.search", "description": "Search for a lawyer based on area of expertise and location", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Los Angeles, CA"}, "expertise": {"type": "string", "description": "Area of legal expertise. For example, 'Marriage', 'Criminal', 'Business'."}}, "required": ["location", "expertise"]}}]}
{"id": "multiple_59", "question": [[{"role": "user", "content": "Find lawyers specializing in criminal law near me in New York."}]], "function": [{"name": "car_rental", "description": "Rent a car near you based on your preference.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Your location"}, "car_type": {"type": "array", "items": {"type": "string"}, "description": "Type of cars that you want to rent."}, "fuel_type": {"type": "string", "description": "Preferred fuel type of car. Gas, diesel, electric, hybrid etc. Default 'gas'"}}, "required": ["location", "car_type"]}}, {"name": "lawyer_finder", "description": "Locate lawyers near you based on their specialization.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Your location"}, "specialization": {"type": "array", "items": {"type": "string"}, "description": "Specializations of lawyer that you are looking for."}, "experience": {"type": "integer", "description": "Experience in years that lawyer has. Default 1"}}, "required": ["location", "specialization"]}}]}
{"id": "multiple_60", "question": [[{"role": "user", "content": "What will be the humidity and temperature for New York City after 7 days?"}]], "function": [{"name": "event_search", "description": "Search for events happening in a specific location for a future date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the event information for."}, "days": {"type": "integer", "description": "Number of future days for which to retrieve the event information."}}, "required": ["location", "days"]}}, {"name": "movie_showtimes", "description": "Retrieve movie showtimes for a specific location and for a future date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the movie showtimes for."}, "days": {"type": "integer", "description": "Number of future days for which to retrieve the showtimes."}}, "required": ["location", "days"]}}, {"name": "humidity_temperature_forecast", "description": "Retrieve forecast of humidity and temperature for a specific location and for a future date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the humidity and temperature forecast for."}, "days": {"type": "integer", "description": "Number of future days for which to retrieve the forecast."}}, "required": ["location", "days"]}}]}
{"id": "multiple_61", "question": [[{"role": "user", "content": "Find a Landscape Architect who is experienced 5 years in small space garden design in Portland"}]], "function": [{"name": "home_renovation_expert.find_specialty", "description": "Search for a home renovation expert based on the location and specialization", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "City and state where the professional is based, e.g. Portland, OR."}, "specialization": {"type": "string", "description": "A specific area of expertise, such as kitchen or bathroom renovation."}, "years_experience": {"type": "integer", "description": "Number of years the professional has been practicing in their field. (optional)", "default": 0}}, "required": ["location", "specialization"]}}, {"name": "landscape_architect.find_specialty", "description": "Search for a landscape architect based on the location and specialization", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "City and state where the professional is based, e.g. Portland, OR."}, "specialization": {"type": "string", "description": "A specific area of expertise. Common areas include residential design, commercial design, urban design, and park design."}, "years_experience": {"type": "integer", "description": "Number of years the professional has been practicing in their field. (optional)", "default": 0}}, "required": ["location", "specialization"]}}]}
{"id": "multiple_62", "question": [[{"role": "user", "content": "Find me the closest nature park that allows camping and has scenic views in Boston, MA."}]], "function": [{"name": "nature_park.find_nearby", "description": "Locate nearby nature parks based on specific criteria like camping availability and scenic views.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Boston, MA."}, "features": {"type": "array", "items": {"type": "string", "enum": ["Camping", "Scenic View", "Trails", "Picnic Areas"]}, "description": "Preferred features in nature park."}}, "required": ["location", "features"]}}, {"name": "restaurant.find_nearby", "description": "Locate nearby restaurants based on specific criteria.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Boston, MA."}, "amenities": {"type": "array", "items": {"type": "string", "enum": ["Delivery", "Outdoor Seating", "Vegetarian Options"]}, "description": "Preferred amenities in restaurant. Default empty array []"}}, "required": ["location"]}}]}
{"id": "multiple_63", "question": [[{"role": "user", "content": "What will be the air quality index of New York for the next week?"}]], "function": [{"name": "air_quality_forecast", "description": "Retrieve an air quality forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality forecast for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}}, "required": ["location", "days"]}}, {"name": "weather_forecast", "description": "Retrieve a weather forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the weather for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}}, "required": ["location", "days"]}}, {"name": "news", "description": "Retrieve news articles for a specific topic.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The topic that you want to get the news for."}, "days": {"type": "integer", "description": "Number of past days for which to retrieve the news."}}, "required": ["topic", "days"]}}]}
{"id": "multiple_64", "question": [[{"role": "user", "content": "Give me the UV index for Tokyo for tomorrow, June01 2023."}]], "function": [{"name": "uv_index.get_future", "description": "Retrieve UV index data for a specified location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location to retrieve the UV index for."}, "date": {"type": "string", "description": "The date for the UV index, in the format mm-dd-yyyy."}}, "required": ["location", "date"]}}, {"name": "rainfall_prediction", "description": "Retrieve rainfall data for a specified location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location to retrieve the rainfall prediction for."}, "date": {"type": "string", "description": "The date for the rainfall prediction, in the format mm/dd/yyyy.'"}}, "required": ["location", "date"]}}, {"name": "snowfall_prediction", "description": "Retrieve snowfall data for a specified location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location to retrieve the snowfall prediction for."}, "date": {"type": "string", "description": "The date for the snowfall prediction, in the format mm-dd-yyyy."}}, "required": ["location", "date"]}}]}
{"id": "multiple_65", "question": [[{"role": "user", "content": "Find the distance between New York City and Los Angeles."}]], "function": [{"name": "timezones.get_difference", "description": "Find the time difference between two cities.", "parameters": {"type": "dict", "properties": {"city1": {"type": "string", "description": "The first city."}, "city2": {"type": "string", "description": "The second city."}}, "required": ["city1", "city2"]}}, {"name": "geodistance.find", "description": "Find the distance between two cities on the globe.", "parameters": {"type": "dict", "properties": {"origin": {"type": "string", "description": "The originating city for the distance calculation."}, "destination": {"type": "string", "description": "The destination city for the distance calculation."}, "unit": {"type": "string", "default": "miles", "description": "The unit of measure for the distance calculation."}}, "required": ["origin", "destination"]}}, {"name": "flights.search", "description": "Find flights between two cities.", "parameters": {"type": "dict", "properties": {"from_city": {"type": "string", "description": "The city to depart from."}, "to_city": {"type": "string", "description": "The city to arrive at."}, "date": {"type": "string", "default": "next monday", "description": "The date to fly."}}, "required": ["from_city", "to_city"]}}]}
{"id": "multiple_66", "question": [[{"role": "user", "content": "How much traffic should I expect from Las Vegas to Los Angeles this weekend?"}]], "function": [{"name": "calculate_distance", "description": "Calculate distance between two locations.", "parameters": {"type": "dict", "properties": {"start_point": {"type": "string", "description": "Starting point of the journey."}, "end_point": {"type": "string", "description": "Ending point of the journey."}}, "required": ["start_point", "end_point"]}}, {"name": "traffic_estimate", "description": "Estimate traffic from one location to another for a specific time period.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "Starting location for the journey."}, "end_location": {"type": "string", "description": "Ending location for the journey."}, "time_period": {"type": "string", "description": "Specify a time frame to estimate the traffic, 'now' for current, 'weekend' for the coming weekend. Default 'now'"}}, "required": ["start_location", "end_location"]}}, {"name": "weather_forecast", "description": "Retrieve a weather forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the weather for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}}, "required": ["location", "days"]}}]}
{"id": "multiple_67", "question": [[{"role": "user", "content": "Translate Hello, how are you? from English to French."}]], "function": [{"name": "translate", "description": "Translate text from a specified source language to a specified target language.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text to be translated."}, "source_language": {"type": "string", "description": "The language the text is currently in."}, "target_language": {"type": "string", "description": "The language the text will be translated to."}}, "required": ["text", "source_language", "target_language"]}}, {"name": "sentiment_analysis", "description": "Analyze the sentiment of a specified text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text whose sentiment is to be analyzed."}}, "required": ["text"]}}, {"name": "word_count", "description": "Count the number of words in the given text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text that the number of words is to be calculated."}}, "required": ["text"]}}]}
{"id": "multiple_68", "question": [[{"role": "user", "content": "Can I find a historical fiction book at the New York public library?"}]], "function": [{"name": "library.search_books", "description": "Search for a book in a given library with optional parameters", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Name or city of library"}, "genre": {"type": "string", "description": "Genre of the book"}, "title": {"type": "string", "description": "Title of the book. Default ''"}}, "required": ["location", "genre"]}}, {"name": "google.books_search", "description": "Search for a book in the Google Books library with optional parameters", "parameters": {"type": "dict", "properties": {"genre": {"type": "string", "description": "Genre of the book"}, "title": {"type": "string", "description": "Title of the book. Default ''"}}, "required": ["genre"]}}, {"name": "openlibrary.books_search", "description": "Search for a book in the Open Library with optional parameters", "parameters": {"type": "dict", "properties": {"genre": {"type": "string", "description": "Genre of the book"}, "title": {"type": "string", "description": "Title of the book. Default ''"}}, "required": ["genre"]}}]}
{"id": "multiple_69", "question": [[{"role": "user", "content": "Determine my personality type based on the five factor model with given information: I'm talkative, gets nervous easily, has few artistic interests, tend to be lazy and has a forgiving nature."}]], "function": [{"name": "MBTI.analyse", "description": "Analyse personality based on the Myers-Briggs Type Indicator (MBTI) which sorts for preferences and generates a 4-letter personality type.", "parameters": {"type": "dict", "properties": {"thinking_vs_feeling": {"type": "string", "description": "Preference of user between thinking and feeling."}, "introverted_vs_extroverted": {"type": "string", "description": "Preference of user between introverted and extroverted."}, "judging_vs_perceiving": {"type": "string", "description": "Preference of user between judging and perceiving."}, "sensing_vs_intuition": {"type": "string", "description": "Preference of user between sensing and intuition."}}, "required": ["thinking_vs_feeling", "introverted_vs_extroverted", "judging_vs_perceiving", "sensing_vs_intuition"]}}, {"name": "five_factor_model.analyse", "description": "Analyse personality based on the five-factor model, also known as the Big Five, which measures openness, conscientiousness, extraversion, agreeableness, and neuroticism.", "parameters": {"type": "dict", "properties": {"talkative": {"type": "boolean", "description": "Indicates if the user is talkative."}, "nervous": {"type": "boolean", "description": "Indicates if the user gets nervous easily."}, "artistic_interests": {"type": "boolean", "description": "Indicates if the user has many artistic interests."}, "lazy": {"type": "boolean", "description": "Indicates if the user tends to be lazy."}, "forgiving": {"type": "boolean", "description": "Indicates if the user is forgiving."}}, "required": ["talkative", "nervous", "artistic_interests", "lazy", "forgiving"]}}]}
{"id": "multiple_70", "question": [[{"role": "user", "content": "Who were the kings of France during the 18th century?"}]], "function": [{"name": "european_history.get_events", "description": "Provides a list of major historical events based on the specified country and century.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "Country name."}, "century": {"type": "integer", "description": "Century as an integer. For example, for the 1700s, input '18'."}, "event_type": {"type": "string", "description": "Type of the event such as 'war', 'invention', 'revolution' etc. This field is optional. Default is 'all'"}}, "required": ["country", "century"]}}, {"name": "european_history.get_culture", "description": "Provides information on cultural trends, art movements, philosophical ideas based on the specified country and century.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "Country name."}, "century": {"type": "integer", "description": "Century as an integer. For example, for the 1700s, input '18'."}, "aspect": {"type": "string", "description": "Aspect of culture such as 'literature', 'art', 'philosophy' etc. This field is optional. Default 'any'"}}, "required": ["country", "century"]}}, {"name": "european_history.get_monarchs", "description": "Provides a list of monarchs based on the specified country and century.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "Country name."}, "century": {"type": "integer", "description": "Century as an integer. For example, for the 1700s, input '18'."}}, "required": ["country", "century"]}}]}
{"id": "multiple_71", "question": [[{"role": "user", "content": "How many veterans were there in the United States in the year 1954?"}]], "function": [{"name": "get_bureau_statistics", "description": "Retrieve statistical data for a specific year and statistical category", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "The year for which to retrieve the statistical data"}, "category": {"type": "string", "description": "The statistical category (e.g., employment, crime, health)"}}, "required": ["year", "category"]}}, {"name": "get_population", "description": "Retrieve population data for a specific year and population category", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "The year for which to retrieve the population data"}, "category": {"type": "string", "description": "The population category (e.g., total, veterans, women)"}}, "required": ["year", "category"]}}, {"name": "get_demographics", "description": "Retrieve demographic data for a specific year and demographic category", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "The year for which to retrieve the demographic data"}, "category": {"type": "string", "description": "The demographic category (e.g., gender, race, age)"}}, "required": ["year", "category"]}}]}
{"id": "multiple_72", "question": [[{"role": "user", "content": "What was the population of California in 1970?"}]], "function": [{"name": "us_history.population_by_state_year", "description": "Retrieve historical population data for a specific U.S. state and year.", "parameters": {"type": "dict", "properties": {"state": {"type": "string", "description": "The U.S. state for which to retrieve the population."}, "year": {"type": "integer", "description": "The year for which to retrieve the population."}}, "required": ["state", "year"]}}, {"name": "us_economy.gdp_by_state_year", "description": "Retrieve historical GDP data for a specific U.S. state and year.", "parameters": {"type": "dict", "properties": {"state": {"type": "string", "description": "The U.S. state for which to retrieve the GDP."}, "year": {"type": "integer", "description": "The year for which to retrieve the GDP."}, "adjustment": {"type": "string", "description": "The type of adjustment for inflation, 'Real' or 'Nominal'. Optional, 'Nominal' by default.", "enum": ["Real", "Nominal"]}}, "required": ["state", "year"]}}]}
{"id": "multiple_73", "question": [[{"role": "user", "content": "Who was the founder of Buddhism and where was it originated?"}]], "function": [{"name": "religion.get_core_beliefs", "description": "Retrieves the core beliefs and practices of a specified religion.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "Name of the religion for which to retrieve the core beliefs and practices."}}, "required": ["religion"]}}, {"name": "religion.get_origin", "description": "Retrieves the origin and founder information of a specified religion.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "Name of the religion for which to retrieve the founder and origin."}}, "required": ["religion"]}}]}
{"id": "multiple_74", "question": [[{"role": "user", "content": "Find the price of Van Gogh's painting 'Starry Night' on all auction platforms."}]], "function": [{"name": "art_auction.fetch_artwork_price", "description": "Fetch the price of a specific artwork on the auction platform.", "parameters": {"type": "dict", "properties": {"artwork_name": {"type": "string", "description": "The name of the artwork to be searched."}, "artist": {"type": "string", "description": "The artist's name to ensure the precise artwork is fetched."}, "platform": {"type": "string", "description": "The platform where the artwork's price should be fetched from.", "default": "all"}}, "required": ["artwork_name", "artist"]}}, {"name": "library.search_book", "description": "Search for a specific book in the library.", "parameters": {"type": "dict", "properties": {"title": {"type": "string", "description": "The title of the book to be searched."}, "author": {"type": "string", "description": "The author of the book to ensure the precise book is fetched."}, "platform": {"type": "string", "description": "The library where the book should be fetched from.", "default": "all"}}, "required": ["title", "author"]}}]}
{"id": "multiple_75", "question": [[{"role": "user", "content": "Which paint color is currently most popular for living rooms?"}]], "function": [{"name": "weather_forecast", "description": "Retrieve a weather forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the weather for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}}, "required": ["location", "days"]}}, {"name": "paint_color.trends", "description": "Find the most popular paint color for a specific area in the home.", "parameters": {"type": "dict", "properties": {"room": {"type": "string", "description": "Type of the room e.g. Living room, Bathroom etc."}, "period": {"type": "string", "enum": ["Daily", "Weekly", "Monthly", "Yearly"], "description": "The period over which to check the paint color trend. Default 'Daily'"}}, "required": ["room"]}}, {"name": "house_price_trends", "description": "Find the average house price in a specific area.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "City and state, e.g. New York, NY."}, "period": {"type": "string", "enum": ["Daily", "Weekly", "Monthly", "Yearly"], "description": "The period over which to check the price trend. Default 'Yearly'"}}, "required": ["location"]}}]}
{"id": "multiple_76", "question": [[{"role": "user", "content": "I want to order a custom bronze sculpture of a horse. What material options are available?"}]], "function": [{"name": "painting.create_custom", "description": "Order a custom painting with your preferred color.", "parameters": {"type": "dict", "properties": {"subject": {"type": "string", "description": "The subject of the painting, e.g. horse"}, "color": {"type": "string", "enum": ["Red", "Blue", "Green", "Yellow", "Black"], "description": "Preferred main color for the painting."}, "size": {"type": "integer", "description": "The desired size for the painting in inches. This parameter is optional. Default 12"}}, "required": ["subject", "color"]}}, {"name": "sculpture.create_custom", "description": "Order a custom sculpture with your preferred material.", "parameters": {"type": "dict", "properties": {"item": {"type": "string", "description": "The subject of the sculpture, e.g. horse"}, "material": {"type": "string", "enum": ["Bronze", "Marble", "Terracotta", "Wood", "Stone"], "description": "Preferred material for the sculpture."}, "size": {"type": "integer", "description": "The desired size for the sculpture in inches. This parameter is optional. Default 12"}}, "required": ["item", "material"]}}]}
{"id": "multiple_77", "question": [[{"role": "user", "content": "Search for famous contemporary sculptures in New York."}]], "function": [{"name": "tourist_attraction.find", "description": "Search for tourist attractions based on type and location.", "parameters": {"type": "dict", "properties": {"attractionType": {"type": "string", "description": "Type of the attraction. E.g., monument, museum, park."}, "location": {"type": "string", "description": "Location or city where the attraction is."}}, "required": ["attractionType", "location"]}}, {"name": "artwork_search.find", "description": "Search for artworks based on type and location.", "parameters": {"type": "dict", "properties": {"type": {"type": "string", "description": "Type of the artwork. E.g., painting, sculpture, installation."}, "location": {"type": "string", "description": "Location or city where the artwork is."}, "era": {"type": "string", "description": "Time period of the artwork, can be 'contemporary', 'modern', 'renaissance', etc. Default 'contemporary'", "optional": "True"}}, "required": ["type", "location"]}}, {"name": "park_search.find", "description": "Search for parks based on facilities and location.", "parameters": {"type": "dict", "properties": {"facilities": {"type": "array", "items": {"type": "string"}, "description": "List of facilities in the park."}, "location": {"type": "string", "description": "Location or city where the park is."}}, "required": ["facilities", "location"]}}]}
{"id": "multiple_78", "question": [[{"role": "user", "content": "Get me information about Natural History Museum in London including timings, exhibitions, and accessibility."}]], "function": [{"name": "tourist_spot_info", "description": "Retrieve information about a specific tourist spot.", "parameters": {"type": "dict", "properties": {"spot": {"type": "string", "description": "The name of the tourist spot you want to get information for."}, "city": {"type": "string", "description": "The city where the tourist spot is located."}, "details": {"type": "array", "items": {"type": "string", "enum": ["timing", "attractions", "tickets", "accessibility", "history"]}, "description": "Details of the tourist spot to get information on. For multiple details, separate them by comma.", "default": "timing, attractions"}}, "required": ["spot", "city"]}}, {"name": "museum_info", "description": "Retrieve information about a specific museum.", "parameters": {"type": "dict", "properties": {"museum": {"type": "string", "description": "The name of the museum you want to get information for."}, "city": {"type": "string", "description": "The city where the museum is located."}, "features": {"type": "array", "items": {"type": "string", "enum": ["timings", "exhibitions", "accessibility", "events", "history"]}, "description": "Features of the museum to get information on. For multiple features, separate them by comma.", "default": "timings, exhibitions"}}, "required": ["museum", "city"]}}]}
{"id": "multiple_79", "question": [[{"role": "user", "content": "Find art exhibitions for the upcoming month in the Museum of Modern Art, New York."}]], "function": [{"name": "restaurant_info", "description": "Get restaurant information for a specific area.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Location for which to find restaurants."}, "food_type": {"type": "string", "description": "Type of cuisine for which to find restaurants. Default 'any'", "enum": ["Italian", "Chinese", "Mexican", "American"]}}, "required": ["location"]}}, {"name": "exhibition_info", "description": "Get exhibition information for a specific museum.", "parameters": {"type": "dict", "properties": {"museum_name": {"type": "string", "description": "Name of the museum for which to find exhibitions."}, "month": {"type": "integer", "description": "Number of upcoming months for which to retrieve exhibition details. Default 1"}}, "required": ["museum_name"]}}]}
{"id": "multiple_80", "question": [[{"role": "user", "content": "Find a local guitar shop that also offers violin lessons in Nashville."}]], "function": [{"name": "music_shop.find_nearby", "description": "Locate nearby music shops based on specific criteria like instrument lessons availability.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Nashville, TN"}, "services": {"type": "array", "items": {"type": "string", "enum": ["Guitar Lessons", "Violin Lessons", "Piano Lessons", "Ukulele Lessons"]}, "description": "Types of instrument lessons offered in the shop. Default empty array"}, "instruments": {"type": "array", "items": {"type": "string", "enum": ["Guitars", "Violins", "Pianos", "Drums"]}, "description": "Types of instruments sold in the shop. Default empty array"}}, "required": ["location"]}}, {"name": "gym.find_nearby", "description": "Locate nearby gyms based on specific criteria like types of fitness classes availability.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Nashville, TN"}, "classes": {"type": "array", "items": {"type": "string", "enum": ["Yoga", "Spin", "Zumba", "CrossFit"]}, "description": "Types of fitness classes offered in the gym. Default empty array"}, "equipment": {"type": "array", "items": {"type": "string", "enum": ["Treadmills", "Ellipticals", "Weight Machines", "Free Weights"]}, "description": "Types of gym equipment available. Default empty array"}}, "required": ["location"]}}]}
{"id": "multiple_81", "question": [[{"role": "user", "content": "Book a ticket for the upcoming Eminem concert in New York City, I would like to get the one with backstage access."}]], "function": [{"name": "concert.book_ticket", "description": "Book a ticket for a concert at a specific location with various add-ons like backstage pass.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the artist for the concert."}, "location": {"type": "string", "description": "City where the concert will take place."}, "add_ons": {"type": "array", "items": {"type": "string", "enum": ["Backstage Pass", "VIP Seating", "Parking Pass"]}, "description": "Add-ons for the concert. Default empty array"}}, "required": ["artist", "location"]}}, {"name": "festival.book_ticket", "description": "Book a ticket for a festival at a specific location with various add-ons like camping access.", "parameters": {"type": "dict", "properties": {"festival": {"type": "string", "description": "Name of the festival."}, "location": {"type": "string", "description": "City where the festival will take place."}, "add_ons": {"type": "array", "items": {"type": "string", "enum": ["Camping Pass", "VIP Seating", "Parking Pass"]}, "description": "Add-ons for the festival. Default empty array"}}, "required": ["festival", "location"]}}]}
{"id": "multiple_82", "question": [[{"role": "user", "content": "Play a song in C Major key at tempo 120 bpm."}]], "function": [{"name": "music.generate", "description": "Generate a piece of music given a key, tempo, and time signature.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key of the piece, e.g., C Major."}, "tempo": {"type": "integer", "description": "Tempo of the piece in beats per minute."}, "time_signature": {"type": "string", "description": "Time signature of the piece, e.g., 4/4. Default '4/4'", "optional": true}}, "required": ["key", "tempo"]}}, {"name": "audio.generate", "description": "Generate an audio signal given a frequency, amplitude, and duration.", "parameters": {"type": "dict", "properties": {"frequency": {"type": "float", "description": "Frequency of the audio signal in Hz."}, "amplitude": {"type": "float", "description": "Amplitude of the audio signal."}, "duration": {"type": "integer", "description": "Duration of the audio signal in seconds. Default 1", "optional": true}}, "required": ["frequency", "amplitude"]}}]}
{"id": "multiple_83", "question": [[{"role": "user", "content": "How many goals has Lionel Messi scored for Barcelona till date?"}]], "function": [{"name": "team_stats.get_top_scorer", "description": "Fetch the top scorer of a specified football team.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the football team."}, "competition": {"type": "string", "description": "Competition for which to fetch stats (optional). Default ''"}}, "required": ["team_name"]}}, {"name": "league_stats.get_top_scorer", "description": "Fetch the top scorer of a specified football league.", "parameters": {"type": "dict", "properties": {"league_name": {"type": "string", "description": "The name of the football league."}, "season": {"type": "string", "description": "Season for which to fetch stats (optional). Default ''"}}, "required": ["league_name"]}}, {"name": "player_stats.get_all_time_goals", "description": "Fetch all-time goals scored by a particular football player for a specified team.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the football player."}, "team_name": {"type": "string", "description": "The name of the team for which player has played."}, "competition": {"type": "string", "description": "Competition for which to fetch stats (optional). Default ''"}}, "required": ["player_name", "team_name"]}}]}
{"id": "multiple_84", "question": [[{"role": "user", "content": "Give me the top 10 goal scorers in the UEFA Champions League from Barcelona team."}]], "function": [{"name": "getTopGoalScorers", "description": "Returns the top goal scorers for a specific competition and team", "parameters": {"type": "dict", "properties": {"competition": {"type": "string", "description": "The name of the competition (for example, 'UEFA Champions League')."}, "team": {"type": "string", "description": "The name of the team (for example, 'Barcelona')."}, "number": {"type": "integer", "description": "The number of top goal scorers to retrieve."}}, "required": ["competition", "team", "number"]}}, {"name": "getTopAssists", "description": "Returns the top assist makers for a specific competition and team", "parameters": {"type": "dict", "properties": {"competition": {"type": "string", "description": "The name of the competition (for example, 'UEFA Champions League')."}, "team": {"type": "string", "description": "The name of the team (for example, 'Barcelona')."}, "number": {"type": "integer", "description": "The number of top assist makers to retrieve."}}, "required": ["competition", "team", "number"]}}]}
{"id": "multiple_85", "question": [[{"role": "user", "content": "Get the soccer scores for Real Madrid games in La Liga for the last 5 rounds."}]], "function": [{"name": "basketball_scores.get_scores", "description": "Retrieve basketball scores for a specific team and league within a certain range of rounds.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The basketball team whose scores are to be retrieved."}, "league": {"type": "string", "description": "The league in which the team competes."}, "rounds": {"type": "integer", "description": "Number of past rounds for which to retrieve the scores."}}, "required": ["team", "league", "rounds"]}}, {"name": "soccer_scores.get_scores", "description": "Retrieve soccer scores for a specific team and league within a certain range of rounds.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The soccer team whose scores are to be retrieved."}, "league": {"type": "string", "description": "The league in which the team competes."}, "rounds": {"type": "integer", "description": "Number of past rounds for which to retrieve the scores."}}, "required": ["team", "league", "rounds"]}}]}
{"id": "multiple_86", "question": [[{"role": "user", "content": "What are some recommended board games for 2 players and strategy based from store BoardGameGeek?"}]], "function": [{"name": "BoardGameGeek.recommend", "description": "Generate game recommendation from BoardGameGeek store based on number of players and category.", "parameters": {"type": "dict", "properties": {"numPlayers": {"type": "integer", "description": "The number of players who will play the game."}, "category": {"type": "string", "description": "The preferred category of board game. E.g. strategy, family, party etc."}, "difficulty": {"type": "string", "description": "Preferred difficulty level. E.g. beginner, intermediate, advanced etc. This is an optional parameter. Default 'beginner'"}}, "required": ["numPlayers", "category"]}}, {"name": "AmazonGameStore.recommend", "description": "Generate game recommendation from Amazon Game Store based on number of players and category.", "parameters": {"type": "dict", "properties": {"numOfPlayers": {"type": "integer", "description": "The number of players who will play the game."}, "category": {"type": "string", "description": "The preferred category of board game. E.g. strategy, family, party etc."}, "priceRange": {"type": "string", "description": "The price range you are willing to pay for the board game. E.g. $10-$20, $20-$30 etc. This is an optional parameter. Default ''"}}, "required": ["numOfPlayers", "category"]}}]}
{"id": "multiple_87", "question": [[{"role": "user", "content": "Find the latest update or patch for the game 'Cyberpunk 2077' on Xbox platform."}]], "function": [{"name": "games.reviews.find", "description": "Find reviews for a specific game.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the game."}, "region": {"type": "string", "description": "The region where the reviews are coming from (optional, default is 'global')"}}, "required": ["game"]}}, {"name": "games.update.find", "description": "Find the latest updates or patches for a specific game on a specified gaming platform.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the game."}, "platform": {"type": "string", "description": "The gaming platform, e.g. Xbox, Playstation, PC."}, "region": {"type": "string", "description": "The region of the update (optional, default is 'global')"}}, "required": ["game", "platform"]}}, {"name": "games.price.find", "description": "Find the current price for a specific game on a specified gaming platform.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the game."}, "platform": {"type": "string", "description": "The gaming platform, e.g. Xbox, Playstation, PC."}}, "required": ["game", "platform"]}}]}
{"id": "multiple_88", "question": [[{"role": "user", "content": "Find me the number of active players in the game 'World of Warcraft' in 2020."}]], "function": [{"name": "video_games.get_player_count", "description": "Retrieves the number of active players for a specified video game and year.", "parameters": {"type": "dict", "properties": {"game_title": {"type": "string", "description": "The title of the video game."}, "year": {"type": "integer", "description": "The year in question."}, "platform": {"type": "string", "optional": true, "description": "The gaming platform (e.g. 'PC', 'Xbox', 'Playstation'). Default ''"}}, "required": ["game_title", "year"]}}, {"name": "video_games.get_sales", "description": "Retrieves the sales figures for a specified video game and year.", "parameters": {"type": "dict", "properties": {"game_title": {"type": "string", "description": "The title of the video game."}, "year": {"type": "integer", "description": "The year in question."}, "platform": {"type": "string", "optional": true, "description": "The gaming platform (e.g. 'PC', 'Xbox', 'Playstation'). Default ''"}}, "required": ["game_title", "year"]}}]}
{"id": "multiple_89", "question": [[{"role": "user", "content": "Find a healthy lunch recipe under 500 calories that uses chicken and mushrooms."}]], "function": [{"name": "restaurant_search", "description": "Searches for restaurants based on a list of preferred ingredients and maximum calorie count.", "parameters": {"type": "dict", "properties": {"ingredients": {"type": "array", "items": {"type": "string"}, "description": "A list of ingredients you prefer in the restaurant's dishes."}, "calories": {"type": "integer", "description": "The maximum calorie count you prefer for the restaurant's dishes."}, "meal": {"type": "string", "description": "Type of the meal for the restaurant's dishes, it's optional and could be breakfast, lunch or dinner. Default 'lunch'"}}, "required": ["ingredients", "calories"]}}, {"name": "ingredient_replace", "description": "Replaces an ingredient in a recipe with a substitute, keeping the calories below a certain number.", "parameters": {"type": "dict", "properties": {"original_ingredient": {"type": "string", "description": "The ingredient in the recipe to replace."}, "replacement_ingredient": {"type": "string", "description": "The substitute ingredient to replace the original one."}, "calories": {"type": "integer", "description": "The maximum number of calories for the recipe after replacement."}}, "required": ["original_ingredient", "replacement_ingredient", "calories"]}}, {"name": "recipe_search", "description": "Searches for recipes based on a list of ingredients and a maximum caloric value.", "parameters": {"type": "dict", "properties": {"ingredients": {"type": "array", "items": {"type": "string"}, "description": "A list of ingredients you want to use in the recipe."}, "calories": {"type": "integer", "description": "The maximum number of calories for the recipe."}, "meal": {"type": "string", "description": "Type of the meal for the recipe, it's optional and could be breakfast, lunch or dinner. Default 'lunch'"}}, "required": ["ingredients", "calories"]}}]}
{"id": "multiple_90", "question": [[{"role": "user", "content": "I want a seafood restaurant in Seattle that can accommodate a group of 5."}]], "function": [{"name": "events.find_event", "description": "Find events suitable for groups based on specified criteria such as location and event type.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "event_type": {"type": "array", "items": {"type": "string", "enum": ["Concert", "Sports", "Exhibition", "Festival"]}, "description": "Type of event. Default empty array"}, "group_size": {"type": "integer", "description": "Size of the group that the event should accommodate."}}, "required": ["location", "group_size"]}}, {"name": "restaurant.find_group", "description": "Find restaurants suitable for groups based on specified criteria such as location and cuisine.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "cuisine": {"type": "array", "items": {"type": "string", "enum": ["Seafood", "Italian", "Indian", "Chinese"]}, "description": "Preferred cuisine at the restaurant. Default empty array"}, "group_size": {"type": "integer", "description": "Size of the group that the restaurant should accommodate."}}, "required": ["location", "group_size"]}}]}
{"id": "multiple_91", "question": [[{"role": "user", "content": "Can I find a good cooking recipe for apple pie using less than 5 ingredients?"}]], "function": [{"name": "restaurant.find", "description": "Locate restaurants based on specific criteria such as cuisine and price range", "parameters": {"type": "dict", "properties": {"cuisine": {"type": "string", "description": "The type of cuisine preferred."}, "price": {"type": "array", "items": {"type": "string"}, "description": "Price range of the restaurant in format ['low', 'mid', 'high']. Default ['low', 'mid', 'high']"}}, "required": ["cuisine"]}}, {"name": "recipe.find", "description": "Locate cooking recipes based on specific criteria such as main ingredient and number of ingredients", "parameters": {"type": "dict", "properties": {"mainIngredient": {"type": "string", "description": "Main ingredient for the recipe."}, "ingredientLimit": {"type": "integer", "description": "Max number of ingredients the recipe should use."}}, "required": ["mainIngredient", "ingredientLimit"]}}]}
{"id": "multiple_92", "question": [[{"role": "user", "content": "Get me a list of available vegetarian and gluten-free foods at the Walmart near Denver."}]], "function": [{"name": "safeway.vegan_products", "description": "Get available vegan products at specified Safeway store", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "City and state where the Safeway store is located, e.g. Denver, CO"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["vegan", "gluten-free"]}, "description": "Product categories to search within. Default empty array"}}, "required": ["location"]}}, {"name": "wholefoods.vegan_products", "description": "Get available vegan products at specified Whole Foods store", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "City and state where the Whole Foods store is located, e.g. Denver, CO"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["vegan", "gluten-free"]}, "description": "Product categories to search within. Default empty array"}}, "required": ["location"]}}, {"name": "walmart.vegan_products", "description": "Get available vegan products at specified Walmart store", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "City and state where the Walmart store is located, e.g. Denver, CO"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["vegan", "gluten-free"]}, "description": "Product categories to search within. Default empty array"}}, "required": ["location"]}}]}
{"id": "multiple_93", "question": [[{"role": "user", "content": "Book a deluxe room for 2 nights at the Marriott hotel in New York and add breakfast as an extra service"}]], "function": [{"name": "car.rental", "description": "Rent a car at the specified location for a specific number of days", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Location of the car rental."}, "days": {"type": "integer", "description": "Number of days for which to rent the car."}, "car_type": {"type": "string", "description": "Type of the car to rent."}, "pick_up": {"type": "string", "description": "Location of where to pick up the car. Default ''"}}, "required": ["location", "days", "car_type"]}}, {"name": "hotel.book", "description": "Book a hotel room given the location, room type, and number of nights and additional services", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Location of the hotel."}, "roomType": {"type": "string", "description": "Type of the room to be booked."}, "nights": {"type": "integer", "description": "Number of nights to book the room for."}, "additional_services": {"type": "array", "items": {"type": "string", "description": "Additonal services that can be booked.", "enum": ["breakfast", "parking", "spa"]}, "description": "Additional services to be added. Default empty array"}}, "required": ["location", "roomType", "nights"]}}]}
{"id": "multiple_94", "question": [[{"role": "user", "content": "I want to book a suite with queen size bed for 3 nights in Hilton New York. Can you find the pricing for me?"}]], "function": [{"name": "hotel_room_pricing.get", "description": "Get pricing for a specific type of hotel room for specified number of nights.", "parameters": {"type": "dict", "properties": {"hotelName": {"type": "string", "description": "The name of the hotel e.g. Hilton New York"}, "roomType": {"type": "string", "description": "Type of the room to be booked."}, "nights": {"type": "integer", "description": "Number of nights to book the room for."}}, "required": ["hotelName", "roomType", "nights"]}}, {"name": "car_rental_pricing.get", "description": "Get pricing for a specific type of rental car for a specified number of days.", "parameters": {"type": "dict", "properties": {"rentalCompany": {"type": "string", "description": "The name of the rental company."}, "carType": {"type": "string", "description": "Type of the car to be rented."}, "days": {"type": "integer", "description": "Number of days to rent the car."}}, "required": ["rentalCompany", "carType", "days"]}}, {"name": "flight_ticket_pricing.get", "description": "Get pricing for a specific type of flight ticket for specified number of passengers.", "parameters": {"type": "dict", "properties": {"airline": {"type": "string", "description": "The name of the airline."}, "flightClass": {"type": "string", "description": "Class of the flight."}, "passengers": {"type": "integer", "description": "Number of passengers."}}, "required": ["airline", "flightClass", "passengers"]}}]}
{"id": "multiple_95", "question": [[{"role": "user", "content": "Convert 200 euros to US dollars using current exchange rate."}]], "function": [{"name": "currency_exchange.convert", "description": "Converts a value from one currency to another using the latest exchange rate.", "parameters": {"type": "dict", "properties": {"amount": {"type": "integer", "description": "The amount of money to be converted."}, "from_currency": {"type": "string", "description": "The currency to convert from."}, "to_currency": {"type": "string", "description": "The currency to convert to."}, "live_conversion": {"type": "boolean", "description": "If true, use the latest exchange rate for conversion, else use the last known rate. Default false"}}, "required": ["amount", "from_currency", "to_currency"]}}, {"name": "unit_conversion.convert", "description": "Converts a value from one unit to another.", "parameters": {"type": "dict", "properties": {"value": {"type": "integer", "description": "The value to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from."}, "to_unit": {"type": "string", "description": "The unit to convert to."}}, "required": ["value", "from_unit", "to_unit"]}}]}
{"id": "multiple_96", "question": [[{"role": "user", "content": "Solve a quadratic equation where a=2, b=6, and c=5"}]], "function": [{"name": "get_stock_info", "description": "Retrieves information about a specific stock based on company's name.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "detail_level": {"type": "string", "description": "Level of detail for stock information. Can be 'summary' or 'detailed'."}, "market": {"type": "string", "description": "The stock market of interest. Default is 'NASDAQ'"}}, "required": ["company_name", "detail_level"]}}, {"name": "portfolio_future_value", "description": "Calculate the future value of an investment in a specific stock based on the invested amount, expected annual return and number of years.", "parameters": {"type": "dict", "properties": {"stock": {"type": "string", "description": "The ticker symbol of the stock."}, "invested_amount": {"type": "float", "description": "The invested amount in USD."}, "expected_annual_return": {"type": "float", "description": "The expected annual return on investment as a decimal. E.g. 5% = 0.05"}, "years": {"type": "integer", "description": "The number of years for which the investment is made."}}, "required": ["stock", "invested_amount", "expected_annual_return", "years"]}}, {"name": "solve_quadratic_equation", "description": "Function solves the quadratic equation and returns its roots.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of x squared"}, "b": {"type": "integer", "description": "Coefficient of x"}, "c": {"type": "integer", "description": "Constant term in the quadratic equation"}}, "required": ["a", "b", "c"]}}]}
{"id": "multiple_97", "question": [[{"role": "user", "content": "What's the area of a circle with a radius of 10?"}]], "function": [{"name": "geometry.area_circle", "description": "Calculate the area of a circle given the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}, "units": {"type": "string", "description": "The units in which the radius is measured (defaults to meters).", "default": "meters"}}, "required": ["radius"]}}, {"name": "plot_sine_wave", "description": "Plot a sine wave for a given frequency in a given range.", "parameters": {"type": "dict", "properties": {"start_range": {"type": "float", "description": "Start of the range in radians."}, "end_range": {"type": "float", "description": "End of the range in radians."}, "frequency": {"type": "float", "description": "Frequency of the sine wave in Hz."}, "amplitude": {"type": "float", "description": "Amplitude of the sine wave. Default is 1."}, "phase_shift": {"type": "float", "description": "Phase shift of the sine wave in radians. Default is 0."}}, "required": ["start_range", "end_range", "frequency"]}}]}
{"id": "multiple_98", "question": [[{"role": "user", "content": "Calculate the circumference of a circle with radius 3"}]], "function": [{"name": "get_earliest_reference", "description": "Retrieve the earliest historical reference of a person.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "source": {"type": "string", "enum": ["scriptures", "historical records"], "description": "Source to fetch the reference. Default is 'scriptures'"}}, "required": ["name"]}}, {"name": "get_current_time", "description": "Retrieve the current time for a specified city and country.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city for which the current time is to be retrieved."}, "country": {"type": "string", "description": "The country where the city is located."}, "format": {"type": "string", "description": "The format in which the time is to be displayed, optional (defaults to 'HH:MM:SS')."}}, "required": ["city", "country"]}}, {"name": "music_generator.generate_melody", "description": "Generate a melody based on certain musical parameters.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key of the melody. E.g., 'C' for C major."}, "start_note": {"type": "string", "description": "The first note of the melody, specified in scientific pitch notation. E.g., 'C4'."}, "length": {"type": "integer", "description": "The number of measures in the melody."}, "tempo": {"type": "integer", "description": "The tempo of the melody, in beats per minute. Optional parameter. If not specified, defaults to 120."}}, "required": ["key", "start_note", "length"]}}, {"name": "geometry.circumference", "description": "Calculate the circumference of a circle given the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}, "units": {"type": "string", "description": "Units for the output circumference measurement. Default is 'cm'."}}, "required": ["radius"]}}]}
{"id": "multiple_99", "question": [[{"role": "user", "content": "Calculate the derivative of the function 2x^2 at x = 1."}]], "function": [{"name": "calculus.derivative", "description": "Compute the derivative of a function at a specific value.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The function to calculate the derivative of."}, "value": {"type": "integer", "description": "The value where the derivative needs to be calculated at."}, "function_variable": {"type": "string", "description": "The variable present in the function, for instance x or y, etc. Default is 'x'"}}, "required": ["function", "value"]}}, {"name": "get_personality_traits", "description": "Retrieve the personality traits for a specific personality type, including their strengths and weaknesses.", "parameters": {"type": "dict", "properties": {"type": {"type": "string", "description": "The personality type."}, "traits": {"type": "array", "items": {"type": "string", "enum": ["strengths", "weaknesses"]}, "description": "List of traits to be retrieved, default is ['strengths', 'weaknesses']."}}, "required": ["type"]}}]}
{"id": "multiple_100", "question": [[{"role": "user", "content": "Find the highest common factor of 36 and 24."}]], "function": [{"name": "music_generator.generate_scale_progression", "description": "Generate a music scale progression in a specific key with a given tempo and duration.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key in which to generate the scale progression."}, "tempo": {"type": "integer", "description": "The tempo of the scale progression in BPM."}, "duration": {"type": "integer", "description": "The duration of each note in beats."}, "scale_type": {"type": "string", "default": "major", "description": "The type of scale to generate. Defaults to 'major'."}}, "required": ["key", "tempo", "duration"]}}, {"name": "math.hcf", "description": "Calculate the highest common factor of two numbers.", "parameters": {"type": "dict", "properties": {"number1": {"type": "integer", "description": "First number."}, "number2": {"type": "integer", "description": "Second number."}}, "required": ["number1", "number2"]}}]}
{"id": "multiple_101", "question": [[{"role": "user", "content": "Find the greatest common divisor (GCD) of 12 and 18"}]], "function": [{"name": "math.gcd", "description": "Calculate the greatest common divisor of two integers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "First number."}, "num2": {"type": "integer", "description": "Second number."}}, "required": ["num1", "num2"]}}, {"name": "get_top_cases", "description": "Retrieve a list of the most influential or landmark cases in a specific field of law.", "parameters": {"type": "dict", "properties": {"field_of_law": {"type": "string", "description": "The specific field of law e.g., constitutional law, criminal law, etc."}, "top_number": {"type": "integer", "description": "The number of top cases to retrieve."}, "country": {"type": "string", "description": "The country where the law cases should be retrieved from. Default is US."}}, "required": ["field_of_law", "top_number"]}}]}
{"id": "multiple_102", "question": [[{"role": "user", "content": "Calculate the displacement of a car given the initial velocity of 10 and acceleeration of 9.8 within 5 seconds."}]], "function": [{"name": "calculate_displacement", "description": "Calculates the displacement of an object in motion given initial velocity, time, and acceleration.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "integer", "description": "The initial velocity of the object in m/s."}, "time": {"type": "integer", "description": "The time in seconds that the object has been in motion."}, "acceleration": {"type": "float", "description": "The acceleration of the object in m/s^2.", "default": 0}}, "required": ["initial_velocity", "time"]}}, {"name": "poker_game_winner", "description": "Identify the winner in a poker game based on the cards.", "parameters": {"type": "dict", "properties": {"players": {"type": "array", "items": {"type": "string"}, "description": "Names of the players in a list."}, "cards": {"type": "dict", "description": "An object containing the player name as key and the cards as values in a list."}, "type": {"type": "string", "description": "Type of poker game. Defaults to 'Texas Holdem'"}}, "required": ["players", "cards"]}}, {"name": "musical_scale", "description": "Get the musical scale of a specific key in music theory.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The musical key for which the scale will be found."}, "scale_type": {"type": "string", "default": "major", "description": "The type of musical scale."}}, "required": ["key"]}}]}
{"id": "multiple_103", "question": [[{"role": "user", "content": "Calculate the final speed of an object dropped from 100 m without air resistance."}]], "function": [{"name": "chess.rating", "description": "Fetches the current chess rating of a given player", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The full name of the chess player."}, "variant": {"type": "string", "description": "The variant of chess for which rating is requested (e.g., 'classical', 'blitz', 'bullet'). Default is 'classical'."}}, "required": ["player_name"]}}, {"name": "court_case.search", "description": "Retrieves details about a court case using its docket number and location.", "parameters": {"type": "dict", "properties": {"docket_number": {"type": "string", "description": "The docket number for the case."}, "location": {"type": "string", "description": "The location where the case is registered, in the format: city, state, e.g., Dallas, TX."}, "full_text": {"type": "boolean", "default": "false", "description": "Option to return the full text of the case ruling."}}, "required": ["docket_number", "location"]}}, {"name": "calculate_final_speed", "description": "Calculate the final speed of an object dropped from a certain height without air resistance.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "integer", "description": "The initial velocity of the object."}, "height": {"type": "integer", "description": "The height from which the object is dropped."}, "gravity": {"type": "float", "description": "The gravitational acceleration. Default is 9.8 m/s^2."}}, "required": ["initial_velocity", "height"]}}, {"name": "get_event_date", "description": "Retrieve the date of a historical event.", "parameters": {"type": "dict", "properties": {"event": {"type": "string", "description": "The name of the historical event."}, "location": {"type": "string", "description": "Location where the event took place. Defaults to global if not specified"}}, "required": ["event"]}}]}
{"id": "multiple_104", "question": [[{"role": "user", "content": "Find the shortest driving distance between New York City and Washington D.C."}]], "function": [{"name": "instrument_price.get", "description": "Retrieve the current retail price of a specific musical instrument.", "parameters": {"type": "dict", "properties": {"brand": {"type": "string", "description": "The brand of the instrument."}, "model": {"type": "string", "description": "The specific model of the instrument."}, "finish": {"type": "string", "description": "The color or type of finish on the instrument."}}, "required": ["brand", "model", "finish"]}}, {"name": "get_shortest_driving_distance", "description": "Calculate the shortest driving distance between two locations.", "parameters": {"type": "dict", "properties": {"origin": {"type": "string", "description": "Starting point of the journey."}, "destination": {"type": "string", "description": "End point of the journey."}, "unit": {"type": "string", "description": "Preferred unit of distance (optional, default is kilometers)."}}, "required": ["origin", "destination"]}}, {"name": "cell_biology.function_lookup", "description": "Look up the function of a given molecule in a specified organelle.", "parameters": {"type": "dict", "properties": {"molecule": {"type": "string", "description": "The molecule of interest."}, "organelle": {"type": "string", "description": "The organelle of interest."}, "specific_function": {"type": "boolean", "description": "If set to true, a specific function of the molecule within the organelle will be provided, if such information exists."}}, "required": ["molecule", "organelle", "specific_function"]}}, {"name": "get_scientist_for_discovery", "description": "Retrieve the scientist's name who is credited for a specific scientific discovery or theory.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The scientific discovery or theory."}}, "required": ["discovery"]}}]}
{"id": "multiple_105", "question": [[{"role": "user", "content": "Calculate the magnetic field produced at the center of a circular loop carrying current of 5 Ampere with a radius of 4 meters"}]], "function": [{"name": "concert_booking.book_ticket", "description": "Book concert tickets for a specific artist in a specified city.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "The artist you want to book tickets for."}, "city": {"type": "string", "description": "The city where the concert is."}, "num_tickets": {"type": "integer", "description": "Number of tickets required. Default is 1."}}, "required": ["artist", "city"]}}, {"name": "calculate_magnetic_field", "description": "Calculate the magnetic field produced at the center of a circular loop carrying current.", "parameters": {"type": "dict", "properties": {"current": {"type": "integer", "description": "The current through the circular loop in Amperes."}, "radius": {"type": "integer", "description": "The radius of the circular loop in meters."}, "permeability": {"type": "float", "description": "The magnetic permeability. Default is permeability in free space, 0.01"}}, "required": ["current", "radius"]}}, {"name": "lawsuit_details.find", "description": "Find details of lawsuits involving a specific company from a given year.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "Name of the company."}, "year": {"type": "integer", "description": "Year of the lawsuit."}, "case_type": {"type": "string", "description": "Type of the lawsuit, e.g., 'IPR', 'Patent', 'Commercial', etc. This is an optional parameter. Default is 'all'"}}, "required": ["company_name", "year"]}}]}
{"id": "multiple_106", "question": [[{"role": "user", "content": "Calculate the electric field strength 4 meters away from a charge of 0.01 Coulombs."}]], "function": [{"name": "mix_paint_color", "description": "Combine two primary paint colors and adjust the resulting color's lightness level.", "parameters": {"type": "dict", "properties": {"color1": {"type": "string", "description": "The first primary color to be mixed."}, "color2": {"type": "string", "description": "The second primary color to be mixed."}, "lightness": {"type": "integer", "description": "The desired lightness level of the resulting color in percentage. The default level is set to 50%."}}, "required": ["color1", "color2"]}}, {"name": "cooking_conversion.convert", "description": "Convert cooking measurements from one unit to another.", "parameters": {"type": "dict", "properties": {"quantity": {"type": "integer", "description": "The quantity to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from."}, "to_unit": {"type": "string", "description": "The unit to convert to."}, "item": {"type": "string", "description": "The item to be converted."}}, "required": ["quantity", "from_unit", "to_unit", "item"]}}, {"name": "group_dynamics.pattern", "description": "Examine the social dynamics and interactions within a group based on the personality traits and group size.", "parameters": {"type": "dict", "properties": {"total": {"type": "integer", "description": "The total group size."}, "extroverts": {"type": "integer", "description": "The number of extroverted members in the group."}, "introverts": {"type": "integer", "description": "The number of introverted members in the group."}}, "required": ["total", "extroverts", "introverts"]}}, {"name": "calculate_electric_field_strength", "description": "Calculate the electric field strength at a certain distance from a point charge.", "parameters": {"type": "dict", "properties": {"charge": {"type": "float", "description": "The charge in Coulombs."}, "distance": {"type": "integer", "description": "The distance from the charge in meters."}, "medium": {"type": "string", "description": "The medium in which the charge and the point of calculation is located. Default is 'vacuum'."}}, "required": ["charge", "distance"]}}]}
{"id": "multiple_107", "question": [[{"role": "user", "content": "What is the density of a substance with a mass of 45 kg and a volume of 15 m\u00b3?"}]], "function": [{"name": "mix_paint_color", "description": "Combine two primary paint colors and adjust the resulting color's lightness level.", "parameters": {"type": "dict", "properties": {"color1": {"type": "string", "description": "The first primary color to be mixed."}, "color2": {"type": "string", "description": "The second primary color to be mixed."}, "lightness": {"type": "integer", "description": "The desired lightness level of the resulting color in percentage. The default level is set to 50%."}}, "required": ["color1", "color2"]}}, {"name": "calculate_density", "description": "Calculate the density of a substance based on its mass and volume.", "parameters": {"type": "dict", "properties": {"mass": {"type": "integer", "description": "The mass of the substance in kilograms."}, "volume": {"type": "integer", "description": "The volume of the substance in cubic meters."}, "unit": {"type": "string", "description": "The unit of density. Default is kg/m\u00b3"}}, "required": ["mass", "volume"]}}]}
{"id": "multiple_108", "question": [[{"role": "user", "content": "Calculate the heat capacity at constant pressure for air, given its temperature is 298K and volume is 10 m^3."}]], "function": [{"name": "calc_heat_capacity", "description": "Calculate the heat capacity at constant pressure of air using its temperature and volume.", "parameters": {"type": "dict", "properties": {"temp": {"type": "integer", "description": "The temperature of the gas in Kelvin."}, "volume": {"type": "integer", "description": "The volume of the gas in m^3."}, "gas": {"type": "string", "description": "Type of gas, with air as default."}}, "required": ["temp", "volume"]}}, {"name": "calculate_discounted_cash_flow", "description": "Calculate the discounted cash flow of a bond for a given annual coupon payment, time frame and discount rate.", "parameters": {"type": "dict", "properties": {"coupon_payment": {"type": "float", "description": "The annual coupon payment."}, "period": {"type": "integer", "description": "The time frame in years for which coupon payment is made."}, "discount_rate": {"type": "float", "description": "The discount rate."}, "face_value": {"type": "integer", "description": "The face value of the bond, default is $1000."}}, "required": ["coupon_payment", "period", "discount_rate"]}}]}
{"id": "multiple_109", "question": [[{"role": "user", "content": "What are the names of proteins found in the plasma membrane?"}]], "function": [{"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the exhibition is held, e.g., New York, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'high'"}}, "required": ["location", "art_form"]}}, {"name": "mutation_type.find", "description": "Finds the type of a genetic mutation based on its SNP (Single Nucleotide Polymorphism) ID.", "parameters": {"type": "dict", "properties": {"snp_id": {"type": "string", "description": "The ID of the Single Nucleotide Polymorphism (SNP) mutation."}, "species": {"type": "string", "description": "Species in which the SNP occurs, default is 'Homo sapiens' (Humans)."}}, "required": ["snp_id"]}}, {"name": "cellbio.get_proteins", "description": "Get the list of proteins in a specific cell compartment.", "parameters": {"type": "dict", "properties": {"cell_compartment": {"type": "string", "description": "The specific cell compartment."}, "include_description": {"type": "boolean", "description": "Set true if you want a brief description of each protein.", "default": "false"}}, "required": ["cell_compartment"]}}]}
{"id": "multiple_110", "question": [[{"role": "user", "content": "Find the type of gene mutation based on SNP (Single Nucleotide Polymorphism) ID rs6034464."}]], "function": [{"name": "get_collectables_in_season", "description": "Retrieve a list of collectable items in a specific game during a specified season.", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "Name of the game."}, "season": {"type": "string", "description": "The season for which to retrieve the collectable items."}, "item_type": {"type": "string", "description": "The type of item to search for. Default is 'all'. Possible values: 'all', 'bug', 'fish', 'sea creatures', etc."}}, "required": ["game_name", "season"]}}, {"name": "mutation_type.find", "description": "Finds the type of a genetic mutation based on its SNP (Single Nucleotide Polymorphism) ID.", "parameters": {"type": "dict", "properties": {"snp_id": {"type": "string", "description": "The ID of the Single Nucleotide Polymorphism (SNP) mutation."}, "species": {"type": "string", "description": "Species in which the SNP occurs, default is 'Homo sapiens' (Humans)."}}, "required": ["snp_id"]}}]}
{"id": "multiple_111", "question": [[{"role": "user", "content": "What is the genotype frequency of AA genotype in a population, given that allele frequency of A is 0.3?"}]], "function": [{"name": "calculate_genotype_frequency", "description": "Calculate the frequency of homozygous dominant genotype based on the allele frequency using Hardy Weinberg Principle.", "parameters": {"type": "dict", "properties": {"allele_frequency": {"type": "float", "description": "The frequency of the dominant allele in the population."}, "genotype": {"type": "string", "description": "The genotype which frequency is needed, default is homozygous dominant. ", "enum": ["AA", "Aa", "aa"]}}, "required": ["allele_frequency", "genotype"]}}, {"name": "math.factorial", "description": "Calculate the factorial of a given number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number for which factorial needs to be calculated."}}, "required": ["number"]}}, {"name": "find_flute", "description": "Locate a flute for sale based on specific requirements.", "parameters": {"type": "dict", "properties": {"brand": {"type": "string", "description": "The brand of the flute. Example, 'Yamaha'"}, "specs": {"type": "array", "items": {"type": "string", "enum": ["open hole", "C foot", "silver headjoint"]}, "description": "The specifications of the flute desired."}}, "required": ["brand", "specs"]}}]}
{"id": "multiple_112", "question": [[{"role": "user", "content": "Predict the growth of forest in Yellowstone National Park for the next 5 years including human impact."}]], "function": [{"name": "forest_growth_forecast", "description": "Predicts the forest growth over the next N years based on current trends.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location where you want to predict forest growth."}, "years": {"type": "integer", "description": "The number of years for the forecast."}, "include_human_impact": {"type": "boolean", "description": "Whether or not to include the impact of human activities in the forecast. If not provided, defaults to false."}}, "required": ["location", "years"]}}, {"name": "get_scientist_for_discovery", "description": "Retrieve the scientist's name who is credited for a specific scientific discovery or theory.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The scientific discovery or theory."}}, "required": ["discovery"]}}]}
{"id": "multiple_113", "question": [[{"role": "user", "content": "Calculate the expected evolutionary fitness of a creature, with trait A contributing to 40% of the fitness and trait B contributing 60%, if trait A has a value of 0.8 and trait B a value of 0.7."}]], "function": [{"name": "chess.rating", "description": "Fetches the current chess rating of a given player", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The full name of the chess player."}, "variant": {"type": "string", "description": "The variant of chess for which rating is requested (e.g., 'classical', 'blitz', 'bullet'). Default is 'classical'."}}, "required": ["player_name"]}}, {"name": "calculate_fitness", "description": "Calculate the expected evolutionary fitness of a creature based on the individual values and contributions of its traits.", "parameters": {"type": "dict", "properties": {"trait_values": {"type": "array", "items": {"type": "float"}, "description": "List of trait values, which are decimal numbers between 0 and 1, where 1 represents the trait maximally contributing to fitness."}, "trait_contributions": {"type": "array", "items": {"type": "float"}, "description": "List of the percentage contributions of each trait to the overall fitness, which must sum to 1."}}, "required": ["trait_values", "trait_contributions"]}}, {"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is empty array."}}, "required": ["loc", "product_list"]}}, {"name": "lawyer.find_nearby", "description": "Locate nearby lawyers based on specific criteria like specialty, fee per hour and city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city and state, e.g. Chicago, IL."}, "specialty": {"type": "array", "items": {"type": "string", "enum": ["Civil", "Divorce", "Immigration", "Business", "Criminal"]}, "description": "Specialization of the lawyer."}, "fee": {"type": "integer", "description": "Hourly fee charged by lawyer", "maximum": 400}}, "required": ["city", "specialty", "fee"]}}]}
{"id": "multiple_114", "question": [[{"role": "user", "content": "Get me the predictions of the evolutionary rate for Homo Sapiens for next 50 years using Darwin model"}]], "function": [{"name": "modify_painting", "description": "Modify an existing painting's attributes such as size, medium, and color.", "parameters": {"type": "dict", "properties": {"size": {"type": "string", "description": "The size of the painting in inches, width by height."}, "medium": {"type": "string", "description": "The medium of the painting, such as oil, acrylic, etc."}, "dominant_color": {"type": "string", "description": "The dominant color of the painting. Default ''"}}, "required": ["size", "medium"]}}, {"name": "prediction.evolution", "description": "Predict the evolutionary rate for a specific species for a given timeframe.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species that the evolution rate will be predicted for."}, "years": {"type": "integer", "description": "Number of years for the prediction."}, "model": {"type": "string", "description": "The model used to make the prediction, options: 'Darwin', 'Lamarck', default is 'Darwin'."}}, "required": ["species", "years"]}}, {"name": "calculate_probability", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "Total number of possible outcomes."}, "favorable_outcomes": {"type": "integer", "description": "Number of outcomes considered as 'successful'."}, "round_to": {"type": "integer", "description": "Number of decimal places to round the result to.", "default": 2}}, "required": ["total_outcomes", "favorable_outcomes"]}}]}
{"id": "multiple_115", "question": [[{"role": "user", "content": "I want to find 5 restaurants nearby my location, Manhattan, offering Thai food and a vegan menu."}]], "function": [{"name": "find_instrument", "description": "Search for a musical instrument within specified budget and of specific type.", "parameters": {"type": "dict", "properties": {"budget": {"type": "float", "description": "Your budget for the instrument."}, "type": {"type": "string", "description": "Type of the instrument"}, "make": {"type": "string", "description": "Maker of the instrument, Optional parameter. Default is 'all'"}}, "required": ["budget", "type"]}}, {"name": "get_stock_info", "description": "Retrieves information about a specific stock based on company's name.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "detail_level": {"type": "string", "description": "Level of detail for stock information. Can be 'summary' or 'detailed'."}, "market": {"type": "string", "description": "The stock market of interest. Default is 'NASDAQ'"}}, "required": ["company_name", "detail_level"]}}, {"name": "find_restaurants", "description": "Locate nearby restaurants based on location and food preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The specific location or area."}, "food_type": {"type": "string", "description": "The type of food preferred."}, "number": {"type": "integer", "description": "Number of results to return."}, "dietary_requirements": {"type": "array", "items": {"type": "string"}, "description": "Special dietary requirements, e.g. vegan, gluten-free.", "default": "None"}}, "required": ["location", "food_type", "number"]}}, {"name": "sports.match_schedule", "description": "Retrieve the match schedule for a specific sports team.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "num_matches": {"type": "integer", "description": "The number of upcoming matches you want to get."}, "league": {"type": "string", "description": "The sports league of the team. This is an optional parameter. Default 'all'"}}, "required": ["team_name", "num_matches"]}}]}
{"id": "multiple_116", "question": [[{"role": "user", "content": "Calculate the Body Mass Index (BMI) of a person with a weight of 85 kilograms and height of 180 cm."}]], "function": [{"name": "calculate_bmi", "description": "Calculate the Body Mass Index (BMI) of a person.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "Weight of the person in kilograms."}, "height": {"type": "integer", "description": "Height of the person in centimeters."}, "unit": {"type": "string", "description": "Optional parameter to choose between 'imperial' and 'metric' systems. Default is 'metric'."}}, "required": ["weight", "height"]}}, {"name": "celebrity_net_worth.get", "description": "Get the total net worth of a sports celebrity based on most recent data.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The full name of the sports celebrity."}, "currency": {"type": "string", "description": "The currency in which the net worth will be returned. Default is 'USD'."}}, "required": ["name", "currency"]}}]}
{"id": "multiple_117", "question": [[{"role": "user", "content": "Calculate the BMI (Body Mass Index) of a person who weighs 70kg and is 1.75m tall."}]], "function": [{"name": "calculate_BMI", "description": "Calculate the Body Mass Index (BMI) given a person's weight and height.", "parameters": {"type": "dict", "properties": {"weight_kg": {"type": "integer", "description": "The weight of the person in kilograms."}, "height_m": {"type": "float", "description": "The height of the person in meters."}}, "required": ["weight_kg", "height_m"]}}, {"name": "soccer.get_last_match", "description": "Retrieve the details of the last match played by a specified soccer club.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the soccer club."}, "include_stats": {"type": "boolean", "description": "If true, include match statistics like possession, shots on target etc. Default is false."}}, "required": ["team_name"]}}, {"name": "hotel_booking", "description": "Books a hotel room given the location, room type, stay duration and any additional preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you want to book the hotel."}, "room_type": {"type": "string", "description": "Type of the room required. Options: 'single', 'double', 'deluxe', etc."}, "duration": {"type": "integer", "description": "The number of nights you want to book the hotel for."}, "start_date": {"type": "string", "description": "The date when your stay begins."}, "preferences": {"type": "array", "items": {"type": "string", "enum": ["pet_friendly", "gym", "swimming_pool", "free_breakfast", "parking"]}, "description": "Optional preferences of stay at the hotel. Default is empty array."}}, "required": ["location", "room_type", "duration", "start_date"]}}]}
{"id": "multiple_118", "question": [[{"role": "user", "content": "Find all movies starring Leonardo DiCaprio in the year 2010 from IMDB database."}]], "function": [{"name": "flight.book", "description": "Book a direct flight for a specific date and time from departure location to destination location.", "parameters": {"type": "dict", "properties": {"departure_location": {"type": "string", "description": "The location you are departing from."}, "destination_location": {"type": "string", "description": "The location you are flying to."}, "date": {"type": "string", "description": "The date of the flight. Accepts standard date format e.g., 2022-04-28."}, "time": {"type": "string", "description": "Preferred time of flight. Default is 'anytime'."}, "direct_flight": {"type": "boolean", "description": "If set to true, only direct flights will be searched. Default is false"}}, "required": ["departure_location", "destination_location", "date"]}}, {"name": "lawsuits_search", "description": "Search for lawsuits against a specific company within a specific time and location.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "location": {"type": "string", "description": "The location where the lawsuit was filed."}, "year": {"type": "integer", "description": "The year when the lawsuit was filed."}, "case_type": {"type": "string", "description": "The type of the case. Options include: 'civil', 'criminal', 'small_claims', etc. If not specified, search for 'all' types by default."}}, "required": ["company_name", "location", "year"]}}, {"name": "imdb.find_movies_by_actor", "description": "Searches the database to find all movies by a specific actor within a certain year.", "parameters": {"type": "dict", "properties": {"actor_name": {"type": "string", "description": "The name of the actor."}, "year": {"type": "integer", "description": "The specific year to search in."}, "category": {"type": "string", "description": "The category of the film (e.g. Drama, Comedy, etc). This parameter is optional. Default is 'all'."}}, "required": ["actor_name", "year"]}}]}
{"id": "multiple_119", "question": [[{"role": "user", "content": "Find records in database in user table where age is greater than 25 and job is 'engineer'."}]], "function": [{"name": "average_batting_score", "description": "Get the average batting score of a cricketer for specified past matches.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "Name of the cricket player."}, "matches": {"type": "integer", "description": "Number of past matches to consider for average calculation."}, "match_format": {"type": "string", "description": "Format of the cricket matches considered (e.g., 'T20', 'ODI', 'Test'). Default is 'T20'."}}, "required": ["player_name", "matches"]}}, {"name": "calculate_return_on_investment", "description": "Calculate the return on investment for a given stock based on its purchase price, sale price, and any dividends received.", "parameters": {"type": "dict", "properties": {"purchase_price": {"type": "float", "description": "The price the stock was bought at."}, "sale_price": {"type": "float", "description": "The price the stock was sold at."}, "dividend": {"type": "float", "description": "Any dividends received from the stock.", "default": 0}}, "required": ["purchase_price", "sale_price"]}}, {"name": "database.query", "description": "Query the database based on certain conditions.", "parameters": {"type": "dict", "properties": {"table": {"type": "string", "description": "Name of the table to query."}, "conditions": {"type": "array", "items": {"type": "dict", "properties": {"field": {"type": "string", "description": "The field to apply the condition."}, "operation": {"type": "string", "description": "The operation to be performed.", "enum": ["<", ">", "=", ">=", "<="]}, "value": {"type": "string", "description": "The value to be compared."}}, "required": ["field", "operation", "value"]}, "description": "Conditions for the query."}}, "required": ["table", "conditions"]}}, {"name": "probability_of_event", "description": "Calculates the probability of an event.", "parameters": {"type": "dict", "properties": {"success_outcomes": {"type": "integer", "description": "The number of successful outcomes."}, "total_outcomes": {"type": "integer", "description": "The total number of possible outcomes."}, "format_as_ratio": {"type": "boolean", "description": "When true, formats the output as a ratio instead of a decimal. Default is false."}}, "required": ["success_outcomes", "total_outcomes"]}}]}
{"id": "multiple_120", "question": [[{"role": "user", "content": "How much time will it take for the light to reach earth from a star 4 light years away?"}]], "function": [{"name": "light_travel_time", "description": "Calculate the time taken for light to travel from a celestial body to another.", "parameters": {"type": "dict", "properties": {"distance_in_light_years": {"type": "integer", "description": "The distance between the two celestial bodies in light years."}, "speed_of_light": {"type": "integer", "description": "The speed of light in vacuum, in m/s. Default value is ********* m/s."}}, "required": ["distance_in_light_years"]}}, {"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}, {"name": "book_hotel", "description": "Book a room in a specific hotel with particular preferences", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The location of the hotel."}, "room_type": {"type": "string", "description": "The type of room preferred."}, "start_date": {"type": "string", "description": "The starting date of the stay in format MM-DD-YYYY."}, "stay_duration": {"type": "integer", "description": "The duration of the stay in days."}, "view": {"type": "string", "description": "The preferred view from the room, can be ignored if no preference. If none provided, assumes no preference.", "default": "No preference"}}, "required": ["hotel_name", "location", "room_type", "start_date", "stay_duration"]}}, {"name": "latest_exchange_rate", "description": "Retrieve the latest exchange rate between two specified currencies.", "parameters": {"type": "dict", "properties": {"source_currency": {"type": "string", "description": "The currency you are converting from."}, "target_currency": {"type": "string", "description": "The currency you are converting to."}, "amount": {"type": "float", "description": "The amount to be converted. If omitted, exchange rate of 1 unit source currency is given. Default is 1."}}, "required": ["source_currency", "target_currency"]}}]}
{"id": "multiple_121", "question": [[{"role": "user", "content": "Calculate the area of a triangle with base 6 and height 10."}]], "function": [{"name": "map_service.get_directions", "description": "Retrieve directions from a starting location to an ending location, including options for route preferences.", "parameters": {"type": "dict", "properties": {"start": {"type": "string", "description": "Starting location for the route."}, "end": {"type": "string", "description": "Ending location for the route."}, "avoid": {"type": "array", "items": {"type": "string", "enum": ["tolls", "highways", "ferries"]}, "description": "Route features to avoid. Default is empty array."}}, "required": ["start", "end"]}}, {"name": "science_history.get_invention", "description": "Retrieve the inventor and year of invention based on the invention's name.", "parameters": {"type": "dict", "properties": {"invention_name": {"type": "string", "description": "The name of the invention."}, "want_year": {"type": "boolean", "default": false, "description": "Return the year of invention if set to true."}}, "required": ["invention_name", "want_year"]}}, {"name": "geometry.area_triangle", "description": "Calculate the area of a triangle.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The length of the base of the triangle."}, "height": {"type": "integer", "description": "The height of the triangle from the base."}, "unit": {"type": "string", "description": "The measurement unit for the area. Defaults to square meters."}}, "required": ["base", "height"]}}]}
{"id": "multiple_122", "question": [[{"role": "user", "content": "Run a linear regression model with predictor variables 'Age', 'Income' and 'Education' and a target variable 'Purchase_Amount'. Also apply standardization."}]], "function": [{"name": "cooking_conversion.convert", "description": "Convert cooking measurements from one unit to another.", "parameters": {"type": "dict", "properties": {"quantity": {"type": "integer", "description": "The quantity to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from."}, "to_unit": {"type": "string", "description": "The unit to convert to."}, "item": {"type": "string", "description": "The item to be converted."}}, "required": ["quantity", "from_unit", "to_unit", "item"]}}, {"name": "run_linear_regression", "description": "Build a linear regression model using given predictor variables and a target variable.", "parameters": {"type": "dict", "properties": {"predictors": {"type": "array", "items": {"type": "string"}, "description": "Array containing the names of predictor variables."}, "target": {"type": "string", "description": "The name of target variable."}, "standardize": {"type": "boolean", "description": "Option to apply standardization on the predictors. Defaults to False."}}, "required": ["predictors", "target"]}}, {"name": "find_recipe", "description": "Locate a recipe based on name and its calorie content", "parameters": {"type": "dict", "properties": {"recipeName": {"type": "string", "description": "The recipe's name."}, "maxCalories": {"type": "integer", "description": "The maximum calorie content of the recipe.", "default": 1000}}, "required": ["recipeName"]}}, {"name": "travel_itinerary_generator", "description": "Generate a travel itinerary based on specific destination, duration and daily budget, with preferred exploration type.", "parameters": {"type": "dict", "properties": {"destination": {"type": "string", "description": "Destination city of the trip."}, "days": {"type": "integer", "description": "Number of days for the trip."}, "daily_budget": {"type": "float", "description": "The maximum daily budget for the trip."}, "exploration_type": {"type": "string", "enum": ["nature", "urban", "history", "culture"], "description": "The preferred exploration type.", "default": "urban"}}, "required": ["destination", "days", "daily_budget"]}}]}
{"id": "multiple_123", "question": [[{"role": "user", "content": "Calculate the probability of drawing a king from a deck of cards."}]], "function": [{"name": "calculate_probability", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "Total number of possible outcomes."}, "favorable_outcomes": {"type": "integer", "description": "Number of outcomes considered as 'successful'."}, "round_to": {"type": "integer", "description": "Number of decimal places to round the result to.", "default": 2}}, "required": ["total_outcomes", "favorable_outcomes"]}}, {"name": "lawsuit_search", "description": "Retrieve all lawsuits involving a particular entity from specified jurisdiction.", "parameters": {"type": "dict", "properties": {"entity": {"type": "string", "description": "The entity involved in lawsuits."}, "county": {"type": "string", "description": "The jurisdiction for the lawsuit search."}, "state": {"type": "string", "description": "The state for the lawsuit search. Default is California."}}, "required": ["entity", "county"]}}, {"name": "predict_house_price", "description": "Predict house price based on area, number of rooms and year of construction.", "parameters": {"type": "dict", "properties": {"area": {"type": "integer", "description": "Area of the house in square feet."}, "rooms": {"type": "integer", "description": "Number of rooms in the house."}, "year": {"type": "integer", "description": "Year when the house was constructed."}, "location": {"type": "string", "description": "The location or city of the house."}}, "required": ["area", "rooms", "year", "location"]}}]}
{"id": "multiple_124", "question": [[{"role": "user", "content": "What's the probability of drawing a king from a well shuffled standard deck of 52 cards?"}]], "function": [{"name": "math.power", "description": "Calculate the power of one number raised to another.", "parameters": {"type": "dict", "properties": {"base": {"type": "float", "description": "The base number."}, "exponent": {"type": "float", "description": "The exponent."}, "mod": {"type": "integer", "description": "The modulus. Default is None. Calculates pow(base, exponent) % mod when provided."}}, "required": ["base", "exponent"]}}, {"name": "probabilities.calculate_single", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "The total number of outcomes."}, "event_outcomes": {"type": "integer", "description": "The number of outcomes where the event occurs."}, "round": {"type": "integer", "description": "Round the answer to a specified number of decimal places. Defaults to 2."}}, "required": ["total_outcomes", "event_outcomes"]}}, {"name": "fetch_DNA_sequence", "description": "Retrieve the sequence of a DNA molecule with the given id from a public database.", "parameters": {"type": "dict", "properties": {"DNA_id": {"type": "string", "description": "Unique ID of the DNA molecule in the database."}, "format": {"type": "string", "description": "Optional parameter to get sequence in specific format (default to 'fasta')."}, "upstream": {"type": "integer", "description": "Optional parameter to include certain number of base pairs upstream the DNA sequence (default to 0)."}}, "required": ["DNA_id"]}}]}
{"id": "multiple_125", "question": [[{"role": "user", "content": "Run a two sample T-test to compare the average of Group A [3, 4, 5, 6, 4] and Group B [7, 8, 9, 8, 7] assuming equal variance."}]], "function": [{"name": "restaurant_search.find_closest", "description": "Locate the closest sushi restaurant based on certain criteria, such as the presence of a patio.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city, for instance Boston, MA"}, "cuisine": {"type": "string", "description": "Type of food like Sushi."}, "amenities": {"type": "array", "items": {"type": "string", "enum": ["Patio", "Wi-Fi", "Happy Hour", "Wheelchair Accessible"]}, "description": "Preferred amenities in the restaurant. Default is empty array."}}, "required": ["location", "cuisine"]}}, {"name": "run_two_sample_ttest", "description": "Runs a two sample t-test for two given data groups.", "parameters": {"type": "dict", "properties": {"group1": {"type": "array", "items": {"type": "integer"}, "description": "First group of data points."}, "group2": {"type": "array", "items": {"type": "integer"}, "description": "Second group of data points."}, "equal_variance": {"type": "boolean", "description": "Assumption about whether the two samples have equal variance.", "default": true}}, "required": ["group1", "group2"]}}, {"name": "get_personality_traits", "description": "Retrieve the common personality traits of people based on their hobbies or activities.", "parameters": {"type": "dict", "properties": {"hobby": {"type": "string", "description": "The hobby or activity of interest."}, "trait_count": {"type": "integer", "description": "The number of top traits to return, default is 5"}}, "required": ["hobby"]}}]}
{"id": "multiple_126", "question": [[{"role": "user", "content": "Find the statistical significance between two set of variables, dataset_A with the values 12, 24, 36 and dataset_B with the values 15, 30, 45."}]], "function": [{"name": "event_finder.find_upcoming", "description": "Find upcoming events of a specific genre in a given location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the search will take place, e.g. New York, NY."}, "genre": {"type": "string", "description": "The genre of events."}, "days_ahead": {"type": "integer", "description": "The number of days from now to include in the search.", "default": 7}}, "required": ["location", "genre"]}}, {"name": "t_test", "description": "Perform a statistical t-test to check if the means of two independent datasets are statistically different.", "parameters": {"type": "dict", "properties": {"dataset_A": {"type": "array", "items": {"type": "integer"}, "description": "Dataset A for comparison."}, "dataset_B": {"type": "array", "items": {"type": "integer"}, "description": "Dataset B for comparison."}, "alpha": {"type": "float", "description": "Significance level for the test. Default is 0.05."}}, "required": ["dataset_A", "dataset_B"]}}, {"name": "geometry.area_triangle", "description": "Calculate the area of a triangle.", "parameters": {"type": "dict", "properties": {"base": {"type": "float", "description": "The length of the base of the triangle."}, "height": {"type": "float", "description": "The height of the triangle from the base."}, "unit": {"type": "string", "description": "The measurement unit for the area. Defaults to square meters."}}, "required": ["base", "height"]}}]}
{"id": "multiple_127", "question": [[{"role": "user", "content": "What's the quarterly dividend per share of a company with 100 million outstanding shares and total dividend payout of 50 million USD?"}]], "function": [{"name": "get_song_lyrics", "description": "Retrieve the lyrics of a song based on the artist's name and song title.", "parameters": {"type": "dict", "properties": {"song_title": {"type": "string", "description": "The title of the song."}, "artist_name": {"type": "string", "description": "The name of the artist who performed the song."}, "lang": {"type": "string", "description": "The language of the lyrics. Default is English.", "enum": ["English", "French", "Spanish", "German", "Italian"]}}, "required": ["song_title", "artist_name"]}}, {"name": "mix_paint_color", "description": "Combine two primary paint colors and adjust the resulting color's lightness level.", "parameters": {"type": "dict", "properties": {"color1": {"type": "string", "description": "The first primary color to be mixed."}, "color2": {"type": "string", "description": "The second primary color to be mixed."}, "lightness": {"type": "integer", "description": "The desired lightness level of the resulting color in percentage. The default level is set to 50%."}}, "required": ["color1", "color2"]}}, {"name": "finance.calculate_quarterly_dividend_per_share", "description": "Calculate quarterly dividend per share for a company given total dividend payout and outstanding shares", "parameters": {"type": "dict", "properties": {"total_payout": {"type": "integer", "description": "The total amount of dividends paid out in USD"}, "outstanding_shares": {"type": "integer", "description": "Total number of outstanding shares"}}, "required": ["total_payout", "outstanding_shares"]}}, {"name": "movie_details.brief", "description": "This function retrieves a brief about a specified movie.", "parameters": {"type": "dict", "properties": {"title": {"type": "string", "description": "Title of the movie"}, "extra_info": {"type": "boolean", "description": "Option to get additional information like Director, Cast, Awards etc.", "default": "false"}}, "required": ["title"]}}]}
{"id": "multiple_128", "question": [[{"role": "user", "content": "Calculate the company's return on equity given its net income of $2,000,000, shareholder's equity of $10,000,000, and dividends paid of $200,000."}]], "function": [{"name": "public_library.find_nearby", "description": "Locate nearby public libraries based on specific criteria like English fiction availability and Wi-Fi.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Boston, MA"}, "facilities": {"type": "array", "items": {"type": "string", "enum": ["Wi-Fi", "Reading Room", "Fiction", "Children Section", "Cafe"]}, "description": "Facilities and sections in public library."}}, "required": ["location", "facilities"]}}, {"name": "get_song_lyrics", "description": "Retrieve the lyrics of a song based on the artist's name and song title.", "parameters": {"type": "dict", "properties": {"song_title": {"type": "string", "description": "The title of the song."}, "artist_name": {"type": "string", "description": "The name of the artist who performed the song."}, "lang": {"type": "string", "description": "The language of the lyrics. Default is English.", "enum": ["English", "French", "Spanish", "German", "Italian"]}}, "required": ["song_title", "artist_name"]}}, {"name": "law_case_search.find_historical", "description": "Search for a historical law case based on specific criteria like the subject and year.", "parameters": {"type": "dict", "properties": {"subject": {"type": "string", "description": "The subject matter of the case, e.g., 'fraud'"}, "from_year": {"type": "integer", "description": "The start year for the range of the case. The case should happen after this year."}, "to_year": {"type": "integer", "description": "The end year for the range of the case. The case should happen before this year."}}, "required": ["subject", "from_year", "to_year"]}}, {"name": "calculate_return_on_equity", "description": "Calculate a company's return on equity based on its net income, shareholder's equity, and dividends paid.", "parameters": {"type": "dict", "properties": {"net_income": {"type": "integer", "description": "The company's net income."}, "shareholder_equity": {"type": "integer", "description": "The company's total shareholder's equity."}, "dividends_paid": {"type": "integer", "description": "The total dividends paid by the company. Optional. If not given, default it's 0."}}, "required": ["net_income", "shareholder_equity"]}}]}
{"id": "multiple_129", "question": [[{"role": "user", "content": "Find the compound interest for an investment of $10000 with an annual interest rate of 5% compounded monthly for 5 years."}]], "function": [{"name": "lawsuits_search", "description": "Search for lawsuits against a specific company within a specific time and location.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "location": {"type": "string", "description": "The location where the lawsuit was filed."}, "year": {"type": "integer", "description": "The year when the lawsuit was filed."}, "case_type": {"type": "string", "description": "The type of the case. Options include: 'civil', 'criminal', 'small_claims', etc. If not specified, search for all types. Default is 'all'"}}, "required": ["company_name", "location", "year"]}}, {"name": "compound_interest", "description": "Calculate compound interest for a certain time period.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The initial amount of money that was invested or loaned out."}, "annual_rate": {"type": "float", "description": "The interest rate for a year as a percentage."}, "compounding_freq": {"type": "string", "enum": ["monthly", "quarterly", "annually"], "description": "The number of times that interest is compounded per unit period."}, "time_in_years": {"type": "integer", "description": "The time the money is invested for in years."}}, "required": ["principal", "annual_rate", "compounding_freq", "time_in_years"]}}]}
{"id": "multiple_130", "question": [[{"role": "user", "content": "Calculate the Compound Annual Growth Rate (CAGR) for an initial investment of $2000, final value of $3000 in a period of 4 years."}]], "function": [{"name": "chess.rating", "description": "Fetches the current chess rating of a given player", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The full name of the chess player."}, "variant": {"type": "string", "description": "The variant of chess for which rating is requested (e.g., 'classical', 'blitz', 'bullet'). Default is 'classical'."}}, "required": ["player_name"]}}, {"name": "solve_quadratic", "description": "Find the roots of a quadratic equation. Returns both roots.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of x\u00b2."}, "b": {"type": "integer", "description": "Coefficient of x."}, "c": {"type": "integer", "description": "Constant term."}}, "required": ["a", "b", "c"]}}, {"name": "calculate_cagr", "description": "Calculate the Compound Annual Growth Rate (CAGR) given an initial investment value, a final investment value, and the number of years.", "parameters": {"type": "dict", "properties": {"initial_value": {"type": "integer", "description": "The initial investment value."}, "final_value": {"type": "integer", "description": "The final investment value."}, "period_in_years": {"type": "integer", "description": "The period of the investment in years."}}, "required": ["initial_value", "final_value", "period_in_years"]}}]}
{"id": "multiple_131", "question": [[{"role": "user", "content": "Find the market performance of the S&P 500 and the Dow Jones over the past 5 days."}]], "function": [{"name": "restaurant.find_nearby", "description": "Locate nearby restaurants based on specific dietary preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Los Angeles, CA"}, "dietary_preference": {"type": "array", "items": {"type": "string", "enum": ["Vegan", "Vegetarian", "Gluten-free", "Dairy-free", "Nut-free"]}, "description": "Dietary preference. Default is empty array."}}, "required": ["location"]}}, {"name": "market_performance.get_data", "description": "Retrieve the market performance data for specified indexes over a specified time period.", "parameters": {"type": "dict", "properties": {"indexes": {"type": "array", "items": {"type": "string"}, "description": "Array of stock market indexes. Supported indexes are 'S&P 500', 'Dow Jones', 'NASDAQ', 'FTSE 100', 'DAX' etc."}, "days": {"type": "integer", "description": "Number of days in the past for which the performance data is required."}, "detailed": {"type": "boolean", "description": "Whether to return detailed performance data. If set to true, returns high, low, opening, and closing prices. If false, returns only closing prices. Default is false."}}, "required": ["indexes", "days"]}}, {"name": "sports.match_results", "description": "Returns the results of a given match between two teams.", "parameters": {"type": "dict", "properties": {"team1": {"type": "string", "description": "The name of the first team."}, "team2": {"type": "string", "description": "The name of the second team."}, "season": {"type": "string", "description": "The season when the match happened. Default is the current season."}}, "required": ["team1", "team2"]}}]}
{"id": "multiple_132", "question": [[{"role": "user", "content": "Calculate the future value of an investment with an annual rate of return of 8%, an initial investment of $20000, and a time frame of 5 years."}]], "function": [{"name": "mix_paint_color", "description": "Combine two primary paint colors and adjust the resulting color's lightness level.", "parameters": {"type": "dict", "properties": {"color1": {"type": "string", "description": "The first primary color to be mixed."}, "color2": {"type": "string", "description": "The second primary color to be mixed."}, "lightness": {"type": "integer", "description": "The desired lightness level of the resulting color in percentage. The default level is set to 50%."}}, "required": ["color1", "color2"]}}, {"name": "finance.calculate_future_value", "description": "Calculate the future value of an investment given an initial investment, annual rate of return, and a time frame.", "parameters": {"type": "dict", "properties": {"initial_investment": {"type": "integer", "description": "The initial investment amount."}, "rate_of_return": {"type": "float", "description": "The annual rate of return."}, "years": {"type": "integer", "description": "The time frame of the investment in years."}, "contribution": {"type": "integer", "description": "Optional: Additional regular contributions. Default is 0."}}, "required": ["initial_investment", "rate_of_return", "years"]}}, {"name": "create_histogram", "description": "Create a histogram based on provided data.", "parameters": {"type": "dict", "properties": {"data": {"type": "array", "items": {"type": "float"}, "description": "The data for which histogram needs to be plotted."}, "bins": {"type": "integer", "description": "The number of equal-width bins in the range. Default is 10."}}, "required": ["data", "bins"]}}]}
{"id": "multiple_133", "question": [[{"role": "user", "content": "Calculate the balance of a mutual fund given a total investment of $50000 with a 5% annual yield after 3 years."}]], "function": [{"name": "calculate_mutual_fund_balance", "description": "Calculate the final balance of a mutual fund investment based on the total initial investment, annual yield rate and the time period.", "parameters": {"type": "dict", "properties": {"investment_amount": {"type": "integer", "description": "The initial total amount invested in the fund."}, "annual_yield": {"type": "float", "description": "The annual yield rate of the fund."}, "years": {"type": "integer", "description": "The period of time for the fund to mature."}}, "required": ["investment_amount", "annual_yield", "years"]}}, {"name": "geometry.calculate_area_circle", "description": "Calculate the area of a circle given its radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "float", "description": "The radius of the circle."}, "unit": {"type": "string", "description": "The measurement unit of the radius (optional parameter, default is 'units')."}}, "required": ["radius"]}}]}
{"id": "multiple_134", "question": [[{"role": "user", "content": "Look up details of a felony crime record for case number CA123456 in San Diego County"}]], "function": [{"name": "calculate_density", "description": "Calculate the population density of a specific country in a specific year.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country for which the density needs to be calculated."}, "year": {"type": "string", "description": "The year in which the density is to be calculated."}, "population": {"type": "integer", "description": "The population of the country."}, "land_area": {"type": "float", "description": "The land area of the country in square kilometers."}}, "required": ["country", "year", "population", "land_area"]}}, {"name": "crime_record.get_record", "description": "Retrieve detailed felony crime records using a specific case number and location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The case number related to the crime."}, "county": {"type": "string", "description": "The county in which the crime occurred."}, "details": {"type": "boolean", "description": "To get a detailed report, set as true. Defaults to false."}}, "required": ["case_number", "county"]}}, {"name": "get_highest_scoring_player", "description": "Retrieve the highest scoring player in a specific game and season.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The game in which you want to find the highest scoring player."}, "season": {"type": "string", "description": "The season during which the high score was achieved."}, "region": {"type": "string", "description": "The geographical region in which the game is being played (Optional). Defaults to 'USA'"}}, "required": ["game", "season"]}}, {"name": "calculate_compound_interest", "description": "Calculates the compound interest of an investment over a given time period.", "parameters": {"type": "dict", "properties": {"principle": {"type": "float", "description": "The initial amount of the investment."}, "interest_rate": {"type": "float", "description": "The yearly interest rate of the investment."}, "time": {"type": "integer", "description": "The time, in years, the money is invested or borrowed for."}, "compounds_per_year": {"type": "integer", "description": "The number of times the interest is compounded per year. Default is 1 (interest is compounded yearly)."}}, "required": ["principle", "interest_rate", "time"]}}]}
{"id": "multiple_135", "question": [[{"role": "user", "content": "Who was the victim in the case docket numbered 2022/AL2562 in California?"}]], "function": [{"name": "get_case_info", "description": "Retrieve case details using a specific case docket number and court location.", "parameters": {"type": "dict", "properties": {"docket": {"type": "string", "description": "Docket number for the specific court case."}, "court": {"type": "string", "description": "Court in which the case was heard."}, "info_type": {"type": "string", "description": "Specify the information type needed for the case. i.e., victim, accused, verdict etc."}}, "required": ["docket", "court", "info_type"]}}, {"name": "calculate_triangle_area", "description": "Calculate the area of a triangle given its base and height.", "parameters": {"type": "dict", "properties": {"base": {"type": "float", "description": "The base of the triangle."}, "height": {"type": "float", "description": "The height of the triangle."}, "unit": {"type": "string", "description": "The unit of measure (defaults to 'units' if not specified)"}}, "required": ["base", "height"]}}]}
{"id": "multiple_136", "question": [[{"role": "user", "content": "Provide me the official crime rate of violent crime in San Francisco in 2020."}]], "function": [{"name": "get_crime_rate", "description": "Retrieve the official crime rate of a city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The name of the city."}, "state": {"type": "string", "description": "The state where the city is located."}, "type": {"type": "string", "description": "Optional. The type of crime. Default ''"}, "year": {"type": "integer", "description": "Optional. The year for the crime rate data. Defaults to 2024."}}, "required": ["city", "state"]}}, {"name": "poker_game_winner", "description": "Identify the winner in a poker game based on the cards.", "parameters": {"type": "dict", "properties": {"players": {"type": "array", "items": {"type": "string"}, "description": "Names of the players in a list."}, "cards": {"type": "dict", "description": "An object containing the player name as key and the cards as values in a list."}, "type": {"type": "string", "description": "Type of poker game. Defaults to 'Texas Holdem'"}}, "required": ["players", "cards"]}}]}
{"id": "multiple_137", "question": [[{"role": "user", "content": "Search for ongoing lawsuits related to the company 'Google' filed after January 1, 2021 in California."}]], "function": [{"name": "lawsuit_search", "description": "Search for lawsuits related to a specific company within a specific date range and location.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company related to the lawsuit."}, "start_date": {"type": "string", "description": "Start of the date range for when the lawsuit was filed."}, "location": {"type": "string", "description": "Location where the lawsuit was filed."}, "status": {"type": "string", "enum": ["ongoing", "settled", "dismissed"], "description": "The status of the lawsuit. Default is 'ongoing'."}}, "required": ["company", "start_date", "location"]}}, {"name": "walmart.check_price", "description": "Calculate total price for given items and their quantities at Walmart.", "parameters": {"type": "dict", "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "List of items to be priced."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item corresponding to the items list."}, "store_location": {"type": "string", "description": "The store location for specific pricing (optional). Default is 'USA'."}}, "required": ["items", "quantities"]}}, {"name": "event_finder.find_upcoming", "description": "Find upcoming events of a specific genre in a given location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the search will take place, e.g. New York, NY."}, "genre": {"type": "string", "description": "The genre of events."}, "days_ahead": {"type": "integer", "description": "The number of days from now to include in the search.", "default": 7}}, "required": ["location", "genre"]}}]}
{"id": "multiple_138", "question": [[{"role": "user", "content": "How to obtain the detailed case information of the R vs Adams legal case?"}]], "function": [{"name": "park_information", "description": "Retrieve the basic information such as elevation and area of a national park.", "parameters": {"type": "dict", "properties": {"park_name": {"type": "string", "description": "The name of the national park."}, "information": {"type": "array", "items": {"type": "string", "enum": ["Elevation", "Area", "Location", "Established Year"]}, "description": "The type of information you want about the park."}}, "required": ["park_name", "information"]}}, {"name": "legal_case.fetch", "description": "Fetch detailed legal case information from database.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "string", "description": "The ID of the legal case."}, "details": {"type": "boolean", "description": "True if need the detail info. Default is false."}}, "required": ["case_id", "details"]}}, {"name": "calculate_stock_return", "description": "Calculate the projected return of a stock investment given the investment amount, the annual growth rate and holding period in years.", "parameters": {"type": "dict", "properties": {"investment_amount": {"type": "float", "description": "The amount of money to invest."}, "annual_growth_rate": {"type": "float", "description": "The expected annual growth rate of the stock."}, "holding_period": {"type": "integer", "description": "The number of years you intend to hold the stock."}, "include_dividends": {"type": "boolean", "description": "Optional. True if the calculation should take into account potential dividends. Default is false."}}, "required": ["investment_amount", "annual_growth_rate", "holding_period"]}}]}
{"id": "multiple_139", "question": [[{"role": "user", "content": "Find details of patent lawsuits involving the company 'Apple Inc.' from the year 2010."}]], "function": [{"name": "get_collectables_in_season", "description": "Retrieve a list of collectable items in a specific game during a specified season.", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "Name of the game."}, "season": {"type": "string", "description": "The season for which to retrieve the collectable items."}, "item_type": {"type": "string", "description": "The type of item to search for. Default is 'all'. Possible values: 'all', 'bug', 'fish', 'sea creatures', etc."}}, "required": ["game_name", "season"]}}, {"name": "game_score.highest", "description": "Retrieve the highest score achieved by any player in a specific game.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the online game."}, "platform": {"type": "string", "description": "The platform where the game is played, e.g. PC, Xbox, Playstation"}, "region": {"type": "string", "description": "The geographic region of the player. Defaults to 'Global'"}}, "required": ["game", "platform"]}}, {"name": "lawsuit_details.find", "description": "Find details of lawsuits involving a specific company from a given year.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "Name of the company."}, "year": {"type": "integer", "description": "Year of the lawsuit."}, "case_type": {"type": "string", "description": "Type of the lawsuit, e.g., 'IPR', 'Patent', 'Commercial', etc. This is an optional parameter. Default is 'all'."}}, "required": ["company_name", "year"]}}, {"name": "calculate_binomial_probability", "description": "Calculates the binomial probability given the number of trials, successes and the probability of success on an individual trial.", "parameters": {"type": "dict", "properties": {"number_of_trials": {"type": "integer", "description": "The total number of trials."}, "number_of_successes": {"type": "integer", "description": "The desired number of successful outcomes."}, "probability_of_success": {"type": "float", "description": "The probability of a successful outcome on any given trial.", "default": 0.5}}, "required": ["number_of_trials", "number_of_successes"]}}]}
{"id": "multiple_140", "question": [[{"role": "user", "content": "Find the lawsuits filed against the company Google in California in the year 2020."}]], "function": [{"name": "lawsuits_search", "description": "Search for lawsuits against a specific company within a specific time and location.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "location": {"type": "string", "description": "The location where the lawsuit was filed."}, "year": {"type": "integer", "description": "The year when the lawsuit was filed."}, "case_type": {"type": "string", "description": "The type of the case. Options include: 'civil', 'criminal', 'small_claims', etc. If not specified, search for all types. Default is 'all'"}}, "required": ["company_name", "location", "year"]}}, {"name": "hilton_hotel.check_availability", "description": "Check hotel availability for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you want to check hotel availability."}, "check_in_date": {"type": "string", "description": "The check-in date in the format YYYY-MM-DD."}, "check_out_date": {"type": "string", "description": "The check-out date in the format YYYY-MM-DD."}, "no_of_adults": {"type": "integer", "description": "The number of adults for the hotel booking."}, "hotel_chain": {"type": "string", "description": "The hotel chain where you want to book the hotel.", "default": "Hilton"}}, "required": ["location", "check_in_date", "check_out_date", "no_of_adults"]}}]}
{"id": "multiple_141", "question": [[{"role": "user", "content": "I need the details of the lawsuit case with case ID of 1234 and verify if it's already closed."}]], "function": [{"name": "get_protein_sequence", "description": "Retrieve the protein sequence encoded by a human gene.", "parameters": {"type": "dict", "properties": {"gene": {"type": "string", "description": "The human gene of interest."}, "species": {"type": "string", "description": "The species for which the gene is to be analyzed.", "default": "Homo sapiens"}}, "required": ["gene"]}}, {"name": "route.estimate_time", "description": "Estimate the travel time for a specific route with optional stops.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting point for the journey."}, "end_location": {"type": "string", "description": "The destination for the journey."}, "stops": {"type": "array", "items": {"type": "string"}, "description": "Additional cities or points of interest to stop at during the journey. Default is an empty array."}}, "required": ["start_location", "end_location"]}}, {"name": "lawsuit.check_case", "description": "Verify the details of a lawsuit case and check its status using case ID.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "integer", "description": "The identification number of the lawsuit case."}, "closed_status": {"type": "boolean", "description": "The status of the lawsuit case to be verified."}}, "required": ["case_id", "closed_status"]}}]}
{"id": "multiple_142", "question": [[{"role": "user", "content": "What is the humidity level in Miami, Florida in the upcoming 7 days?"}]], "function": [{"name": "weather.humidity_forecast", "description": "Retrieve a humidity forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the humidity for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}, "min_humidity": {"type": "integer", "description": "Minimum level of humidity (in percentage) to filter the result. Optional parameter. Default is 0."}}, "required": ["location", "days"]}}, {"name": "get_team_score", "description": "Retrieves the latest game score, individual player stats, and team stats for a specified sports team.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "league": {"type": "string", "description": "The league that the team is part of."}, "include_player_stats": {"type": "boolean", "default": false, "description": "Indicates if individual player statistics should be included in the result. Default is false."}}, "required": ["team_name", "league"]}}]}
{"id": "multiple_143", "question": [[{"role": "user", "content": "Calculate the slope gradient in degree between two points on a landscape with coordinates (40.7128, -74.0060) and (34.0522, -118.2437)."}]], "function": [{"name": "create_player_profile", "description": "Create a new player profile with character name, class and starting level.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The desired name of the player."}, "_class": {"type": "string", "description": "The character class for the player"}, "starting_level": {"type": "integer", "description": "The starting level for the player", "default": 1}}, "required": ["player_name", "_class"]}}, {"name": "poker_probability.full_house", "description": "Calculate the probability of getting a full house in a poker game.", "parameters": {"type": "dict", "properties": {"deck_size": {"type": "integer", "description": "The size of the deck. Default is 52."}, "hand_size": {"type": "integer", "description": "The size of the hand. Default is 5."}}, "required": ["deck_size", "hand_size"]}}, {"name": "concert.find_nearby", "description": "Locate nearby concerts based on specific criteria like genre.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "genre": {"type": "string", "description": "Genre of music to be played at the concert."}}, "required": ["location", "genre"]}}, {"name": "calculate_slope_gradient", "description": "Calculate the slope gradient between two geographical coordinates.", "parameters": {"type": "dict", "properties": {"point1": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates for the first point [Latitude, Longitude]."}, "point2": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates for the second point [Latitude, Longitude]."}, "unit": {"type": "string", "enum": ["degree", "percent", "ratio"], "description": "The unit for the slope gradient. Default is 'degree'."}}, "required": ["point1", "point2"]}}]}
{"id": "multiple_144", "question": [[{"role": "user", "content": "What is the air quality index in London 2022/08/16?"}]], "function": [{"name": "sports_ranking", "description": "Fetch the ranking of a specific sports team in a specific league", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team."}, "league": {"type": "string", "description": "The name of the league."}, "season": {"type": "integer", "description": "Optional parameter to specify the season, default is the current season."}}, "required": ["team", "league"]}}, {"name": "air_quality", "description": "Retrieve the air quality index for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality index for."}, "date": {"type": "string", "description": "The date you want to get the air quality index for. Default is today."}}, "required": ["location", "date"]}}]}
{"id": "multiple_145", "question": [[{"role": "user", "content": "How much CO2 is produced annually by a gas-fueled car that travels 12,000 miles per year with fuel efficiency 20 mpg?"}]], "function": [{"name": "grocery_store.find_best", "description": "Find the closest high-rated grocery stores based on certain product availability.", "parameters": {"type": "dict", "properties": {"my_location": {"type": "string", "description": "The current location of the user."}, "rating": {"type": "float", "description": "The minimum required store rating. Default is 0.0."}, "products": {"type": "array", "items": {"type": "string"}, "description": "Required products in a list."}}, "required": ["my_location", "products"]}}, {"name": "calculate_emissions", "description": "Calculates the annual carbon dioxide emissions produced by a vehicle based on the distance traveled, the fuel type and the fuel efficiency of the vehicle.", "parameters": {"type": "dict", "properties": {"distance": {"type": "integer", "description": "The distance travelled in miles."}, "fuel_type": {"type": "string", "description": "Type of fuel used by the vehicle."}, "fuel_efficiency": {"type": "integer", "description": "The vehicle's fuel efficiency in miles per gallon."}, "efficiency_reduction": {"type": "float", "description": "The percentage decrease in fuel efficiency per year (optional). Default is 0"}}, "required": ["distance", "fuel_type", "fuel_efficiency"]}}, {"name": "sculpture.get_details", "description": "Retrieve details of a sculpture based on the artist and the title of the sculpture.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "The artist who made the sculpture."}, "title": {"type": "string", "description": "The title of the sculpture."}, "detail": {"type": "string", "description": "The specific detail wanted about the sculpture. Default is 'general information'."}}, "required": ["artist", "title"]}}]}
{"id": "multiple_146", "question": [[{"role": "user", "content": "Find restaurants near me within 10 miles that offer Chinese cuisine in Seattle."}]], "function": [{"name": "restaurant.find_nearby", "description": "Locate nearby restaurants based on specific criteria like cuisine type.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine in restaurant."}, "max_distance": {"type": "integer", "description": "Maximum distance (in miles) within which to search for restaurants. Default is 5."}}, "required": ["location", "cuisine"]}}, {"name": "ecology_data.precipitation_stats", "description": "Retrieve precipitation data for a specified location and time period.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location, e.g., 'Amazon rainforest'."}, "time_frame": {"type": "string", "enum": ["six_months", "year", "five_years"], "description": "The time period for which data is required."}}, "required": ["location", "time_frame"]}}]}
{"id": "multiple_147", "question": [[{"role": "user", "content": "Get me the directions from New York to Los Angeles avoiding highways and toll roads."}]], "function": [{"name": "map_service.get_directions", "description": "Retrieve directions from a starting location to an ending location, including options for route preferences.", "parameters": {"type": "dict", "properties": {"start": {"type": "string", "description": "Starting location for the route."}, "end": {"type": "string", "description": "Ending location for the route."}, "avoid": {"type": "array", "items": {"type": "string", "enum": ["tolls", "highways", "ferries"]}, "description": "Route features to avoid. Default is an empty array."}}, "required": ["start", "end"]}}, {"name": "convert_currency", "description": "Converts an amount from a particular currency to another currency.", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The base currency in which the original amount is present."}, "target_currency": {"type": "string", "description": "The currency to which you want to convert."}, "amount": {"type": "integer", "description": "The amount you want to convert."}}, "required": ["base_currency", "target_currency", "amount"]}}, {"name": "ecology.get_turtle_population", "description": "Get the population and species of turtles in a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location."}, "year": {"type": "integer", "description": "The year of the data requested. (optional) Default is 2024."}, "species": {"type": "boolean", "description": "Whether to include species information. Default is false. (optional)"}}, "required": ["location"]}}]}
{"id": "multiple_148", "question": [[{"role": "user", "content": "Give me detail information about stocks of Apple Inc."}]], "function": [{"name": "get_current_time", "description": "Retrieve the current time in a specific time zone.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the city."}, "country": {"type": "string", "description": "The name of the country."}, "timezone": {"type": "string", "description": "The optional timezone to get current time. Default ''"}}, "required": ["location", "country"]}}, {"name": "get_stock_info", "description": "Retrieves information about a specific stock based on company's name.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "detail_level": {"type": "string", "description": "Level of detail for stock information. Can be 'summary' or 'detailed'."}, "market": {"type": "string", "description": "The stock market of interest. Default is 'NASDAQ'"}}, "required": ["company_name", "detail_level"]}}]}
{"id": "multiple_149", "question": [[{"role": "user", "content": "Analyze the sentiment of a customer review 'I love the food here! It's always fresh and delicious.'."}]], "function": [{"name": "hotel_booking", "description": "Books a hotel room for a specific date range.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY."}, "start_date": {"type": "string", "description": "The start date of the reservation. Use format 'YYYY-MM-DD'."}, "end_date": {"type": "string", "description": "The end date of the reservation. Use format 'YYYY-MM-DD'."}, "rooms": {"type": "integer", "default": 1, "description": "The number of rooms to reserve."}}, "required": ["hotel_name", "location", "start_date", "end_date"]}}, {"name": "sentiment_analysis", "description": "Perform sentiment analysis on a given piece of text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text on which to perform sentiment analysis."}, "language": {"type": "string", "description": "The language in which the text is written."}}, "required": ["text", "language"]}}, {"name": "get_time_difference", "description": "Get the time difference between two places.", "parameters": {"type": "dict", "properties": {"place1": {"type": "string", "description": "The first place for time difference."}, "place2": {"type": "string", "description": "The second place for time difference."}}, "required": ["place1", "place2"]}}, {"name": "calculate_bmi", "description": "Calculate the Body Mass Index (BMI) for a person based on their weight and height.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "The weight of the person in kilograms."}, "height": {"type": "integer", "description": "The height of the person in centimeters."}, "system": {"type": "string", "description": "The system of units to be used, 'metric' or 'imperial'. Default is 'metric'."}}, "required": ["weight", "height"]}}]}
{"id": "multiple_150", "question": [[{"role": "user", "content": "Calculate the neuronal activity based on synaptic input rate of 200 and weight 0.5 and decay rate of 0.1."}]], "function": [{"name": "calculate_electrostatic_potential", "description": "Calculate the electrostatic potential between two charged bodies using the principle of Coulomb's Law.", "parameters": {"type": "dict", "properties": {"charge1": {"type": "float", "description": "The quantity of charge on the first body."}, "charge2": {"type": "float", "description": "The quantity of charge on the second body."}, "distance": {"type": "float", "description": "The distance between the two bodies."}, "constant": {"type": "float", "description": "The value of the electrostatic constant. Default is 898755178.73"}}, "required": ["charge1", "charge2", "distance"]}}, {"name": "calculate_neuronal_activity", "description": "Calculate the neuronal activity (rate of firing) based on a given input synaptic rate, weight of inputs, and decay rate. Higher input or weight increases firing rate and higher decay rate decreases it.", "parameters": {"type": "dict", "properties": {"input_synaptic_rate": {"type": "integer", "description": "The synaptic input rate, usually represented as number of inputs per second."}, "weight": {"type": "float", "description": "The weight of the input, denoting its influence on the neuron's state. Default is 1.0."}, "decay_rate": {"type": "float", "description": "The rate at which the neuron's potential decays in the absence of inputs."}}, "required": ["input_synaptic_rate", "decay_rate"], "optional": ["weight"]}}, {"name": "calculate_displacement", "description": "Calculates the displacement of an object in motion given initial velocity, time, and acceleration.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "float", "description": "The initial velocity of the object in m/s."}, "time": {"type": "integer", "description": "The time in seconds that the object has been in motion."}, "acceleration": {"type": "float", "description": "The acceleration of the object in m/s^2.", "default": 0}}, "required": ["initial_velocity", "time"]}}, {"name": "grocery_store.find_best", "description": "Find the closest high-rated grocery stores based on certain product availability.", "parameters": {"type": "dict", "properties": {"my_location": {"type": "string", "description": "The current location of the user."}, "rating": {"type": "float", "description": "The minimum required store rating. Default is 0.0."}, "products": {"type": "array", "items": {"type": "string"}, "description": "Required products in a list."}}, "required": ["my_location", "products"]}}]}
{"id": "multiple_151", "question": [[{"role": "user", "content": "Find the most followed person on twitter who tweets about psychology related to behaviour and group dynamics."}]], "function": [{"name": "social_media_analytics.most_followed", "description": "Find the most followed Twitter user related to certain topics.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The main topic of interest."}, "sub_topics": {"type": "array", "items": {"type": "string"}, "description": "Sub-topics related to main topic, Optional. Default is an empty list."}, "region": {"type": "string", "description": "Region of interest for twitter search, Optional. Default is 'global'."}}, "required": ["topic"]}}, {"name": "calculate_probability", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "Total number of possible outcomes."}, "favorable_outcomes": {"type": "integer", "description": "Number of outcomes considered as 'successful'."}, "round_to": {"type": "integer", "description": "Number of decimal places to round the result to.", "default": 2}}, "required": ["total_outcomes", "favorable_outcomes"]}}, {"name": "concert_info.get", "description": "Retrieve information about concerts based on specific genre, location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the concert will take place."}, "date": {"type": "string", "description": "Time frame to get the concert for."}, "genre": {"type": "string", "description": "Genre of the concert.", "enum": ["Pop", "Rock", "Country", "Classical", "Electronic", "Hip-Hop"]}}, "required": ["location", "date", "genre"]}}]}
{"id": "multiple_152", "question": [[{"role": "user", "content": "Provide key war events in German history from 1871 to 1945."}]], "function": [{"name": "history.get_key_events", "description": "Retrieve key historical events within a specific period for a certain country.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The name of the country for which history is queried."}, "start_year": {"type": "integer", "description": "Start year of the period for which history is queried."}, "end_year": {"type": "integer", "description": "End year of the period for which history is queried."}, "event_type": {"type": "array", "items": {"type": "string", "enum": ["War", "Revolutions", "Diplomacy", "Economy"]}, "description": "Types of event. If none is provided, all types will be considered. Default is ['all']."}}, "required": ["country", "start_year", "end_year"]}}, {"name": "get_sculpture_value", "description": "Retrieve the current market value of a particular sculpture by a specific artist.", "parameters": {"type": "dict", "properties": {"sculpture": {"type": "string", "description": "The name of the sculpture."}, "artist": {"type": "string", "description": "The name of the artist who created the sculpture."}, "year": {"type": "integer", "description": "The year the sculpture was created. This is optional and is not required for all sculptures. Default is the 2024."}}, "required": ["sculpture", "artist"]}}]}
{"id": "multiple_153", "question": [[{"role": "user", "content": "When was the signing of the Treaty of Lisbon?"}]], "function": [{"name": "locate_tallest_mountains", "description": "Find the tallest mountains within a specified radius of a location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city from which to calculate distance."}, "radius": {"type": "float", "description": "The radius within which to find mountains, measured in kilometers."}, "amount": {"type": "integer", "description": "The number of mountains to find, listed from tallest to smallest."}}, "required": ["location", "radius", "amount"]}}, {"name": "calculate_entropy_change", "description": "Calculate the entropy change for an isothermal and reversible process.", "parameters": {"type": "dict", "properties": {"initial_temp": {"type": "float", "description": "The initial temperature in Kelvin."}, "final_temp": {"type": "float", "description": "The final temperature in Kelvin."}, "heat_capacity": {"type": "float", "description": "The heat capacity in J/K."}, "isothermal": {"type": "boolean", "description": "Whether the process is isothermal. Default is True."}}, "required": ["initial_temp", "final_temp", "heat_capacity"]}}, {"name": "get_event_date", "description": "Retrieve the date of a historical event.", "parameters": {"type": "dict", "properties": {"event": {"type": "string", "description": "The name of the historical event."}, "location": {"type": "string", "description": "Location where the event took place. Defaults to global if not specified"}}, "required": ["event"]}}]}
{"id": "multiple_154", "question": [[{"role": "user", "content": "Who was the full name of the president of the United States in 1861?"}]], "function": [{"name": "US_president.in_year", "description": "Retrieve the name of the U.S. president in a given year.", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "The year in question."}, "full_name": {"type": "boolean", "default": true, "description": "Option to return full name with middle initial, if applicable."}}, "required": ["year"]}}, {"name": "find_card_in_deck", "description": "Locate a particular card in a deck based on rank and suit.", "parameters": {"type": "dict", "properties": {"rank": {"type": "string", "description": "Rank of the card (e.g. Ace, Two, King)."}, "suit": {"type": "string", "description": "Suit of the card (e.g. Hearts, Spades, Diamonds, Clubs)."}, "deck": {"type": "array", "items": {"type": "dict", "properties": {"rank": {"type": "string"}, "suit": {"type": "string"}}}, "description": "Deck of cards. If not provided, the deck will be a standard 52 card deck by default"}}, "required": ["rank", "suit"]}}, {"name": "soccer.get_last_match", "description": "Retrieve the details of the last match played by a specified soccer club.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the soccer club."}, "include_stats": {"type": "boolean", "description": "If true, include match statistics like possession, shots on target etc. Default is false."}}, "required": ["team_name"]}}, {"name": "update_user_info", "description": "Update user information in the database.", "parameters": {"type": "dict", "properties": {"user_id": {"type": "integer", "description": "The user ID of the customer."}, "update_info": {"type": "dict", "properties": {"name": {"type": "string", "description": "The customer's updated name."}, "email": {"type": "string", "description": "The customer's updated email."}}, "description": "The new information to update."}, "database": {"type": "string", "description": "The database where the user's information is stored.", "default": "CustomerInfo"}}, "required": ["user_id", "update_info"]}}]}
{"id": "multiple_155", "question": [[{"role": "user", "content": "Who discovered the neutron? Give me detail information."}]], "function": [{"name": "get_discoverer", "description": "Get the person or team who made a particular scientific discovery", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The discovery for which the discoverer's information is needed."}, "detail": {"type": "boolean", "description": "Optional flag to get additional details about the discoverer, such as birth date and nationality. Defaults to false."}}, "required": ["discovery", "detail"]}}, {"name": "diabetes_prediction", "description": "Predict the likelihood of diabetes type 2 based on a person's weight and height.", "parameters": {"type": "dict", "properties": {"weight": {"type": "float", "description": "Weight of the person in lbs."}, "height": {"type": "float", "description": "Height of the person in inches."}, "activity_level": {"type": "string", "enum": ["sedentary", "lightly active", "moderately active", "very active", "extra active"], "description": "Physical activity level of the person."}}, "required": ["weight", "height", "activity_level"]}}, {"name": "museum_working_hours.get", "description": "Get the working hours of a museum in a specific location.", "parameters": {"type": "dict", "properties": {"museum": {"type": "string", "description": "The name of the museum."}, "location": {"type": "string", "description": "The location of the museum."}, "day": {"type": "string", "description": "Specific day of the week. Optional parameter. Default is 'today'."}}, "required": ["museum", "location"]}}]}
{"id": "multiple_156", "question": [[{"role": "user", "content": "What was Albert Einstein's contribution to science on March 17, 1915?"}]], "function": [{"name": "historical_contrib.get_contrib", "description": "Retrieve historical contribution made by a scientist on a specific date.", "parameters": {"type": "dict", "properties": {"scientist": {"type": "string", "description": "The scientist whose contributions need to be searched."}, "date": {"type": "string", "description": "The date when the contribution was made in yyyy-mm-dd format."}, "category": {"type": "string", "description": "The field of the contribution, such as 'Physics' or 'Chemistry'. Default is all fields."}}, "required": ["scientist", "date"]}}, {"name": "music.calculate_note_duration", "description": "Calculate the duration between two notes based on their frequencies and harmonic rhythm.", "parameters": {"type": "dict", "properties": {"first_note_frequency": {"type": "float", "description": "The frequency of the first note in Hz."}, "second_note_frequency": {"type": "float", "description": "The frequency of the second note in Hz."}, "tempo": {"type": "integer", "description": "The tempo of the music in beats per minute. Defaults to 120 beats per minute."}}, "required": ["first_note_frequency", "second_note_frequency"]}}, {"name": "math.gcd", "description": "Calculate the greatest common divisor of two integers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "First number."}, "num2": {"type": "integer", "description": "Second number."}}, "required": ["num1", "num2"]}}]}
{"id": "multiple_157", "question": [[{"role": "user", "content": "What is the earliest reference of Jesus Christ in history from historical record?"}]], "function": [{"name": "sports_ranking.get_current", "description": "Retrieve the current ranking of a specific team in a particular league.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team whose ranking is sought."}, "league": {"type": "string", "description": "The league in which the team participates."}, "season": {"type": "string", "description": "The season for which the ranking is sought. Defaults to the current season if not provided."}}, "required": ["team", "league"]}}, {"name": "get_earliest_reference", "description": "Retrieve the earliest historical reference of a person.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "source": {"type": "string", "enum": ["scriptures", "historical records"], "description": "Source to fetch the reference. Default is 'scriptures'"}}, "required": ["name"]}}]}
{"id": "multiple_158", "question": [[{"role": "user", "content": "Get the biography and main contributions of Pope Innocent III."}]], "function": [{"name": "publication_year.find", "description": "Fetches the year a particular scientific work was published.", "parameters": {"type": "dict", "properties": {"author": {"type": "string", "description": "Name of the author of the work."}, "work_title": {"type": "string", "description": "Title of the scientific work."}, "location": {"type": "string", "description": "Place of the publication, if known. Default is 'global'."}}, "required": ["author", "work_title"]}}, {"name": "portfolio_future_value", "description": "Calculate the future value of an investment in a specific stock based on the invested amount, expected annual return and number of years.", "parameters": {"type": "dict", "properties": {"stock": {"type": "string", "description": "The ticker symbol of the stock."}, "invested_amount": {"type": "float", "description": "The invested amount in USD."}, "expected_annual_return": {"type": "float", "description": "The expected annual return on investment as a decimal. E.g. 5% = 0.05"}, "years": {"type": "integer", "description": "The number of years for which the investment is made."}}, "required": ["stock", "invested_amount", "expected_annual_return", "years"]}}, {"name": "religious_history.get_papal_biography", "description": "Retrieve the biography and main religious and historical contributions of a Pope based on his papal name.", "parameters": {"type": "dict", "properties": {"papal_name": {"type": "string", "description": "The papal name of the Pope."}, "include_contributions": {"type": "boolean", "default": false, "description": "Include main contributions of the Pope in the response if true."}}, "required": ["papal_name", "include_contributions"]}}, {"name": "board_game_info", "description": "Get the information about a board game from a database. ", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "Name of the board game."}, "info_required": {"type": "array", "items": {"type": "string", "enum": ["average_review_rating", "age_range", "number_of_players", "playing_time", "genre"]}, "description": "Array of information requested for the game."}}, "required": ["game_name", "info_required"]}}]}
{"id": "multiple_159", "question": [[{"role": "user", "content": "Calculate the total quantity of paint needed to cover a wall of 30 feet by 12 feet using a specific brand that covers 400 square feet per gallon."}]], "function": [{"name": "prob_dist.binomial", "description": "Compute the probability of having 'success' outcome from binomial distribution.", "parameters": {"type": "dict", "properties": {"trials": {"type": "integer", "description": "The number of independent experiments."}, "successes": {"type": "integer", "description": "The number of success events."}, "p": {"type": "float", "description": "The probability of success on any given trial, defaults to 0.5"}}, "required": ["trials", "successes"]}}, {"name": "musical_scale", "description": "Get the musical scale of a specific key in music theory.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The musical key for which the scale will be found."}, "scale_type": {"type": "string", "default": "major", "description": "The type of musical scale."}}, "required": ["key"]}}, {"name": "calculate_paint_needed", "description": "Calculate the amount of paint needed to cover a surface area based on the coverage rate of a specific paint brand.", "parameters": {"type": "dict", "properties": {"coverage_rate": {"type": "integer", "description": "The area in square feet that one gallon of paint can cover."}, "length": {"type": "integer", "description": "Length of the wall to be painted in feet."}, "height": {"type": "integer", "description": "Height of the wall to be painted in feet."}}, "required": ["coverage_rate", "length", "height"]}}]}
{"id": "multiple_160", "question": [[{"role": "user", "content": "Find me the most recent art sculpture by James Plensa with detailed description."}]], "function": [{"name": "get_zodiac_compatibility", "description": "Retrieve the compatibility score between two Zodiac signs.", "parameters": {"type": "dict", "properties": {"sign1": {"type": "string", "description": "The first Zodiac sign."}, "sign2": {"type": "string", "description": "The second Zodiac sign."}, "scale": {"type": "string", "enum": ["percentage", "0-10 scale"], "description": "The scale on which compatibility should be shown. Default is 'percentage'."}}, "required": ["sign1", "sign2"]}}, {"name": "local_nursery.find", "description": "Locate local nurseries based on location and plant types availability.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city or locality where the nursery needs to be located."}, "plant_types": {"type": "array", "items": {"type": "string", "enum": ["Annual", "Perennial", "Shrub", "Tree", "Herbs", "Fruits"]}, "description": "Type of plants the nursery should provide."}}, "required": ["location", "plant_types"]}}, {"name": "get_sculpture_info", "description": "Retrieves the most recent artwork by a specified artist with its detailed description.", "parameters": {"type": "dict", "properties": {"artist_name": {"type": "string", "description": "The name of the artist."}, "detail": {"type": "boolean", "description": "If True, it provides detailed description of the sculpture. Defaults to False."}}, "required": ["artist_name"]}}, {"name": "monarch.getMonarchOfYear", "description": "Retrieve the monarch of a specific location during a specified year.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location (e.g., country) whose monarch needs to be found."}, "year": {"type": "integer", "description": "The year to search the monarch."}, "fullName": {"type": "boolean", "default": "false", "description": "If true, returns the full name and title of the monarch."}}, "required": ["location", "year"]}}]}
{"id": "multiple_161", "question": [[{"role": "user", "content": "Find the top rated modern sculpture exhibition happening in New York in the upcoming month."}]], "function": [{"name": "card_game_probability.calculate", "description": "Calculate the probability of drawing a certain card or suit from a deck of cards.", "parameters": {"type": "dict", "properties": {"total_cards": {"type": "integer", "description": "Total number of cards in the deck."}, "desired_cards": {"type": "integer", "description": "Number of cards in the deck that satisfy the conditions."}, "cards_drawn": {"type": "integer", "default": 1, "description": "Number of cards drawn from the deck."}}, "required": ["total_cards", "desired_cards"]}}, {"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the exhibition is held, e.g., New York, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'high'"}}, "required": ["location", "art_form"]}}, {"name": "get_sculpture_info", "description": "Retrieves the most recent artwork by a specified artist with its detailed description.", "parameters": {"type": "dict", "properties": {"artist_name": {"type": "string", "description": "The name of the artist."}, "year": {"type": "integer", "description": "Year of the sculpture. This is optional. Default 2024"}, "detail": {"type": "boolean", "description": "If True, it provides detailed description of the sculpture. Defaults to False."}}, "required": ["artist_name"]}}]}
{"id": "multiple_162", "question": [[{"role": "user", "content": "What is the structural dynamic analysis of the building with building Id B1004 for 2nd, 3rd and 4th floors?"}]], "function": [{"name": "player_statistic", "description": "Retrieves detailed player's statistics for a specific year.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The player's name."}, "year": {"type": "integer", "description": "Year for which the statistics will be displayed."}, "team_name": {"type": "string", "description": "The name of the team(optional). Default is 'all'"}}, "required": ["player_name", "year"]}}, {"name": "analyze_structure", "description": "Analyze a structure of a building based on its Id and floor numbers.", "parameters": {"type": "dict", "properties": {"building_id": {"type": "string", "description": "The unique identification number of the building."}, "floors": {"type": "array", "items": {"type": "integer"}, "description": "Floor numbers to be analyzed."}, "mode": {"type": "string", "description": "Mode of analysis, e.g. 'static' or 'dynamic'. Default is 'static'."}}, "required": ["building_id", "floors"]}}]}
{"id": "multiple_163", "question": [[{"role": "user", "content": "Get the list of top 5 popular artworks at the Metropolitan Museum of Art. Please sort by popularity."}]], "function": [{"name": "metropolitan_museum.get_top_artworks", "description": "Fetches the list of popular artworks at the Metropolitan Museum of Art. Results can be sorted based on popularity.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number of artworks to fetch"}, "sort_by": {"type": "string", "description": "The criteria to sort the results on. Default is ''.", "enum": ["popularity", "chronological", "alphabetical"]}}, "required": ["number"]}}, {"name": "lawsuit_search", "description": "Search for lawsuits related to a specific company within a specific date range and location.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company related to the lawsuit."}, "start_date": {"type": "string", "description": "Start of the date range for when the lawsuit was filed."}, "location": {"type": "string", "description": "Location where the lawsuit was filed."}, "status": {"type": "string", "enum": ["ongoing", "settled", "dismissed"], "description": "The status of the lawsuit. Default is 'ongoing'."}}, "required": ["company", "start_date", "location"]}}]}
{"id": "multiple_164", "question": [[{"role": "user", "content": "What's the retail price of a Fender American Professional II Stratocaster in Rosewood Finish?"}]], "function": [{"name": "grocery_store.find_nearby", "description": "Locate nearby grocery stores based on specific criteria like organic fruits and vegetables.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Houston, TX"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["Organic", "Vegetables", "Fruits", "Dairy", "Seafood", "Bakery"]}, "description": "Categories of items to be found in the grocery store. Default is empty array"}}, "required": ["location"]}}, {"name": "calculate_NPV", "description": "Calculate the NPV (Net Present Value) of an investment, considering a series of future cash flows, discount rate, and an initial investment.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "float"}, "description": "Series of future cash flows."}, "discount_rate": {"type": "float", "description": "The discount rate to use."}, "initial_investment": {"type": "float", "description": "The initial investment. Default is 0 if not specified."}}, "required": ["cash_flows", "discount_rate"]}}, {"name": "get_stock_price", "description": "Get the closing stock price for a specific company on a specified date.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "Name of the company."}, "date": {"type": "string", "description": "Date of when to get the stock price. Format: yyyy-mm-dd."}, "exchange": {"type": "string", "description": "Name of the stock exchange market where the company's stock is listed. Default is 'NASDAQ'"}}, "required": ["company_name", "date"]}}, {"name": "instrument_price.get", "description": "Retrieve the current retail price of a specific musical instrument.", "parameters": {"type": "dict", "properties": {"brand": {"type": "string", "description": "The brand of the instrument."}, "model": {"type": "string", "description": "The specific model of the instrument."}, "finish": {"type": "string", "description": "The color or type of finish on the instrument."}}, "required": ["brand", "model", "finish"]}}]}
{"id": "multiple_165", "question": [[{"role": "user", "content": "Find the price of a used Gibson Les Paul guitar in excellent condition in the Chicago area."}]], "function": [{"name": "identify_color_rgb", "description": "This function identifies the RGB values of a named color.", "parameters": {"type": "dict", "properties": {"color_name": {"type": "string", "description": "Name of the color."}, "standard": {"type": "string", "description": "The color standard (e.g. basic, pantone). Default is 'basic'"}}, "required": ["color_name"]}}, {"name": "board_game.chess.get_top_players", "description": "Find top chess players in a location based on rating.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city you want to find the players from."}, "minimum_rating": {"type": "integer", "description": "Minimum rating to filter the players."}, "number_of_players": {"type": "integer", "default": 10, "description": "Number of players you want to retrieve, default value is 10"}}, "required": ["location", "minimum_rating"]}}, {"name": "guitar_price.find", "description": "Retrieve the price of a specific used guitar model based on its condition and location.", "parameters": {"type": "dict", "properties": {"model": {"type": "string", "description": "The model of the guitar."}, "condition": {"type": "string", "enum": ["Poor", "Good", "Excellent"], "description": "The condition of the guitar."}, "location": {"type": "string", "description": "The location where the guitar is being sold."}}, "required": ["model", "condition", "location"]}}]}
{"id": "multiple_166", "question": [[{"role": "user", "content": "Find me a classical concert this weekend in Los Angeles with cheap tickets."}]], "function": [{"name": "religion.history_info", "description": "Provides comprehensive historical details about a specified religion till a specified century.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "The name of the religion for which historical details are needed."}, "till_century": {"type": "integer", "description": "The century till which historical details are needed."}, "include_people": {"type": "boolean", "description": "To include influential people related to the religion during that time period, default is False"}}, "required": ["religion", "till_century"]}}, {"name": "team_score.get_latest", "description": "Retrieve the score of the most recent game for a specified sports team.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "Name of the sports team."}, "include_opponent": {"type": "boolean", "description": "Include the name of the opponent team in the return.", "default": "false"}}, "required": ["team"]}}, {"name": "concert.search", "description": "Locate a concert based on specific criteria like genre, location, and date.", "parameters": {"type": "dict", "properties": {"genre": {"type": "string", "description": "Genre of the concert."}, "location": {"type": "string", "description": "City of the concert."}, "date": {"type": "string", "description": "Date of the concert, e.g. this weekend, today, tomorrow, or date string."}, "price_range": {"type": "string", "enum": ["free", "cheap", "moderate", "expensive", "any"], "description": "Expected price range of the concert tickets. Default is 'any'"}}, "required": ["genre", "location", "date"]}}]}
{"id": "multiple_167", "question": [[{"role": "user", "content": "Generate a melody in C major scale, starting with the note C4, 16 measures long, at 120 beats per minute."}]], "function": [{"name": "calculate_density", "description": "Calculate the population density of a specific country in a specific year.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country for which the density needs to be calculated."}, "year": {"type": "string", "description": "The year in which the density is to be calculated."}, "population": {"type": "integer", "description": "The population of the country."}, "land_area": {"type": "float", "description": "The land area of the country in square kilometers."}}, "required": ["country", "year", "population", "land_area"]}}, {"name": "get_directions", "description": "Retrieve directions from one location to another.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting point of the journey."}, "end_location": {"type": "string", "description": "The destination point of the journey."}, "route_type": {"type": "string", "description": "Type of route to use (e.g., fastest, scenic). Default is fastest.", "enum": ["fastest", "scenic"]}}, "required": ["start_location", "end_location"]}}, {"name": "music_generator.generate_melody", "description": "Generate a melody based on certain musical parameters.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key of the melody. E.g., 'C' for C major."}, "start_note": {"type": "string", "description": "The first note of the melody, specified in scientific pitch notation. E.g., 'C4'."}, "length": {"type": "integer", "description": "The number of measures in the melody."}, "tempo": {"type": "integer", "description": "The tempo of the melody, in beats per minute. Optional parameter. If not specified, defaults to 120."}}, "required": ["key", "start_note", "length"]}}]}
{"id": "multiple_168", "question": [[{"role": "user", "content": "Find the lyrics to the song 'Bohemian Rhapsody' by Queen."}]], "function": [{"name": "get_stock_price", "description": "Retrieves the current stock price of the specified companies", "parameters": {"type": "dict", "properties": {"company_names": {"type": "array", "items": {"type": "string"}, "description": "The list of companies for which to retrieve the stock price."}}, "required": ["company_names"]}}, {"name": "get_song_lyrics", "description": "Retrieve the lyrics of a song based on the artist's name and song title.", "parameters": {"type": "dict", "properties": {"song_title": {"type": "string", "description": "The title of the song."}, "artist_name": {"type": "string", "description": "The name of the artist who performed the song."}, "lang": {"type": "string", "description": "The language of the lyrics. Default is English.", "enum": ["English", "French", "Spanish", "German", "Italian"]}}, "required": ["song_title", "artist_name"]}}, {"name": "park_information", "description": "Retrieve the basic information such as elevation and area of a national park.", "parameters": {"type": "dict", "properties": {"park_name": {"type": "string", "description": "The name of the national park."}, "information": {"type": "array", "items": {"type": "string", "enum": ["Elevation", "Area", "Location", "Established Year"]}, "description": "The type of information you want about the park."}}, "required": ["park_name", "information"]}}]}
{"id": "multiple_169", "question": [[{"role": "user", "content": "What is the musical scale associated with C sharp major?"}]], "function": [{"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is an empty array"}}, "required": ["loc", "product_list"]}}, {"name": "musical_scale", "description": "Get the musical scale of a specific key in music theory.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The musical key for which the scale will be found."}, "scale_type": {"type": "string", "default": "major", "description": "The type of musical scale."}}, "required": ["key"]}}]}
{"id": "multiple_170", "question": [[{"role": "user", "content": "Get the player stats of Cristiano Ronaldo in the 2019-2020 season"}]], "function": [{"name": "park_information", "description": "Retrieve the basic information such as elevation and area of a national park.", "parameters": {"type": "dict", "properties": {"park_name": {"type": "string", "description": "The name of the national park."}, "information": {"type": "array", "items": {"type": "string", "enum": ["Elevation", "Area", "Location", "Established Year"]}, "description": "The type of information you want about the park."}}, "required": ["park_name", "information"]}}, {"name": "us_history.get_president", "description": "Retrieve the U.S. president during a specific event in American history.", "parameters": {"type": "dict", "properties": {"event": {"type": "string", "description": "The event in U.S. history."}, "year": {"type": "integer", "description": "The specific year of the event."}}, "required": ["event", "year"]}}, {"name": "monopoly_odds_calculator", "description": "Calculates the probability of rolling a certain sum with two dice, commonly used in board game like Monopoly.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number for which the odds are calculated."}, "dice_number": {"type": "integer", "description": "The number of dice involved in the roll."}, "dice_faces": {"type": "integer", "description": "The number of faces on a single die. Default is 6 for standard six-faced die."}}, "required": ["number", "dice_number"]}}, {"name": "soccer_stat.get_player_stats", "description": "Retrieve soccer player statistics for a given season.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "Name of the player."}, "season": {"type": "string", "description": "Soccer season, usually specified by two years."}, "league": {"type": "string", "description": "Optional - the soccer league, defaults to all leagues. Default 'all'"}}, "required": ["player_name", "season"]}}]}
{"id": "multiple_171", "question": [[{"role": "user", "content": "Who won the basketball game between Lakers and Clippers on Jan 28, 2021?"}]], "function": [{"name": "poker_probability.full_house", "description": "Calculate the probability of getting a full house in a poker game.", "parameters": {"type": "dict", "properties": {"deck_size": {"type": "integer", "description": "The size of the deck. Default is 52."}, "hand_size": {"type": "integer", "description": "The size of the hand. Default is 5."}}, "required": ["deck_size", "hand_size"]}}, {"name": "game_result.get_winner", "description": "Get the winner of a specific basketball game.", "parameters": {"type": "dict", "properties": {"teams": {"type": "array", "items": {"type": "string"}, "description": "List of two teams who played the game."}, "date": {"type": "string", "description": "The date of the game, formatted as YYYY-MM-DD."}, "venue": {"type": "string", "optional": true, "description": "Optional: The venue of the game. Default is ''"}}, "required": ["teams", "date"]}}]}
{"id": "multiple_172", "question": [[{"role": "user", "content": "Find me the detailed profile of basketball player Lebron James"}]], "function": [{"name": "get_traffic_info", "description": "Retrieve current traffic conditions for a specified route.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting point of the route."}, "end_location": {"type": "string", "description": "The destination of the route."}, "mode": {"type": "string", "enum": ["driving", "walking", "bicycling", "transit"], "description": "Preferred method of transportation, default to 'driving'."}}, "required": ["start_location", "end_location"]}}, {"name": "calculate_compound_interest", "description": "Calculates the compound interest of an investment over a given time period.", "parameters": {"type": "dict", "properties": {"principle": {"type": "float", "description": "The initial amount of the investment."}, "interest_rate": {"type": "float", "description": "The yearly interest rate of the investment."}, "time": {"type": "integer", "description": "The time, in years, the money is invested or borrowed for."}, "compounds_per_year": {"type": "integer", "description": "The number of times the interest is compounded per year. Default is 1 (interest is compounded yearly)."}}, "required": ["principle", "interest_rate", "time"]}}, {"name": "cooking_conversion.convert", "description": "Convert cooking measurements from one unit to another.", "parameters": {"type": "dict", "properties": {"quantity": {"type": "integer", "description": "The quantity to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from."}, "to_unit": {"type": "string", "description": "The unit to convert to."}, "item": {"type": "string", "description": "The item to be converted."}}, "required": ["quantity", "from_unit", "to_unit", "item"]}}, {"name": "sports_db.find_athlete", "description": "Find the profile information of a sports athlete based on their full name.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The full name of the athlete."}, "team": {"type": "string", "description": "The team the athlete belong to. Default is ''"}, "sport": {"type": "string", "description": "The sport that athlete plays.", "enum": ["Basketball", "Baseball", "Football", "Soccer"]}}, "required": ["name", "sport"]}}]}
{"id": "multiple_173", "question": [[{"role": "user", "content": "Get the NBA team's ranking with the best defence in the 2021 season."}]], "function": [{"name": "get_defense_ranking", "description": "Retrieve the defence ranking of NBA teams in a specified season.", "parameters": {"type": "dict", "properties": {"season": {"type": "integer", "description": "The NBA season to get defence ranking from."}, "top": {"type": "integer", "default": 1, "description": "Number of top teams in defence ranking to fetch."}}, "required": ["season"]}}, {"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting. If not specified, it will default to ascending."}}, "required": ["list", "order"]}}, {"name": "calculate_cagr", "description": "Calculate the Compound Annual Growth Rate (CAGR) given an initial investment value, a final investment value, and the number of years.", "parameters": {"type": "dict", "properties": {"initial_value": {"type": "float", "description": "The initial investment value."}, "final_value": {"type": "float", "description": "The final investment value."}, "period_in_years": {"type": "integer", "description": "The period of the investment in years."}}, "required": ["initial_value", "final_value", "period_in_years"]}}]}
{"id": "multiple_174", "question": [[{"role": "user", "content": "What is the ranking of Manchester United in Premier League?"}]], "function": [{"name": "sports_ranking", "description": "Fetch the ranking of a specific sports team in a specific league", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team."}, "league": {"type": "string", "description": "The name of the league."}, "season": {"type": "integer", "description": "Optional parameter to specify the season, default is the current season, 2024"}}, "required": ["team", "league"]}}, {"name": "calculate_compound_interest", "description": "Calculates the compound interest of an investment over a given time period.", "parameters": {"type": "dict", "properties": {"principle": {"type": "float", "description": "The initial amount of the investment."}, "interest_rate": {"type": "float", "description": "The yearly interest rate of the investment."}, "time": {"type": "integer", "description": "The time, in years, the money is invested or borrowed for."}, "compounds_per_year": {"type": "integer", "description": "The number of times the interest is compounded per year. Default is 1 (interest is compounded yearly)."}}, "required": ["principle", "interest_rate", "time"]}}]}
{"id": "multiple_175", "question": [[{"role": "user", "content": "Who is ranked as the top player in woman tennis?"}]], "function": [{"name": "find_instrument", "description": "Search for a musical instrument within specified budget and of specific type.", "parameters": {"type": "dict", "properties": {"budget": {"type": "float", "description": "Your budget for the instrument."}, "type": {"type": "string", "description": "Type of the instrument"}, "make": {"type": "string", "description": "Maker of the instrument, Optional parameter. Default is ''"}}, "required": ["budget", "type"]}}, {"name": "calculate_binomial_probability", "description": "Calculates the binomial probability given the number of trials, successes and the probability of success on an individual trial.", "parameters": {"type": "dict", "properties": {"number_of_trials": {"type": "integer", "description": "The total number of trials."}, "number_of_successes": {"type": "integer", "description": "The desired number of successful outcomes."}, "probability_of_success": {"type": "float", "description": "The probability of a successful outcome on any given trial.", "default": 0.5}}, "required": ["number_of_trials", "number_of_successes"]}}, {"name": "electromagnetic_force", "description": "Calculate the electromagnetic force between two charges placed at a certain distance.", "parameters": {"type": "dict", "properties": {"charge1": {"type": "float", "description": "The magnitude of the first charge in coulombs."}, "charge2": {"type": "float", "description": "The magnitude of the second charge in coulombs."}, "distance": {"type": "float", "description": "The distance between the two charges in meters."}, "medium_permittivity": {"type": "float", "description": "The relative permittivity of the medium in which the charges are present. Default is 8.854 x 10^-12 F/m (vacuum permittivity)."}}, "required": ["charge1", "charge2", "distance"]}}, {"name": "sports_ranking.get_top_player", "description": "Get the top player in a specific sport.", "parameters": {"type": "dict", "properties": {"sport": {"type": "string", "description": "The type of sport."}, "gender": {"type": "string", "description": "The gender of the sport category. Optional.", "default": "men"}}, "required": ["sport"]}}]}
{"id": "multiple_176", "question": [[{"role": "user", "content": "Give me the schedule of Manchester United for the next 6 games in Premier League."}]], "function": [{"name": "vegan_restaurant.find_nearby", "description": "Locate nearby vegan restaurants based on specific criteria like operating hours.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York, NY"}, "operating_hours": {"type": "integer", "description": "Preferred latest closing time of the restaurant. E.g. if 11 is given, then restaurants that close at or after 11 PM will be considered. This is in 24 hour format. Default is 9."}}, "required": ["location"]}}, {"name": "hotel_booking", "description": "Books a hotel room given the location, room type, stay duration and any additional preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you want to book the hotel."}, "room_type": {"type": "string", "description": "Type of the room required. Options: 'single', 'double', 'deluxe', etc."}, "duration": {"type": "integer", "description": "The number of nights you want to book the hotel for."}, "start_date": {"type": "string", "description": "The date when your stay begins."}, "preferences": {"type": "array", "items": {"type": "string", "enum": ["pet_friendly", "gym", "swimming_pool", "free_breakfast", "parking"]}, "description": "Optional preferences of stay at the hotel. Default is empty array"}}, "required": ["location", "room_type", "duration", "start_date"]}}, {"name": "find_card_in_deck", "description": "Locate a particular card in a deck based on rank and suit.", "parameters": {"type": "dict", "properties": {"rank": {"type": "string", "description": "Rank of the card (e.g. Ace, Two, King)."}, "suit": {"type": "string", "description": "Suit of the card (e.g. Hearts, Spades, Diamonds, Clubs)."}, "deck": {"type": "array", "items": {"type": "dict", "properties": {"rank": {"type": "string"}, "suit": {"type": "string"}}}, "description": "Deck of cards. If not provided, the deck will be a standard 52 card deck by default"}}, "required": ["rank", "suit"]}}, {"name": "sports_team.get_schedule", "description": "Fetches the schedule of the specified sports team for the specified number of games in the given league.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "num_of_games": {"type": "integer", "description": "Number of games for which to fetch the schedule."}, "league": {"type": "string", "description": "The name of the sports league. If not provided, the function will fetch the schedule for all games, regardless of the league."}, "location": {"type": "string", "description": "Optional. The city or venue where games are to be held. If not provided, all venues will be considered. Default to ''."}}, "required": ["team_name", "num_of_games", "league"]}}]}
{"id": "multiple_177", "question": [[{"role": "user", "content": "Find the top chess players in New York with a rating above 2300."}]], "function": [{"name": "board_game.chess.get_top_players", "description": "Find top chess players in a location based on rating.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city you want to find the players from."}, "minimum_rating": {"type": "integer", "description": "Minimum rating to filter the players."}, "number_of_players": {"type": "integer", "default": 10, "description": "Number of players you want to retrieve, default value is 10"}}, "required": ["location", "minimum_rating"]}}, {"name": "get_historical_GDP", "description": "Retrieve historical GDP data for a specific country and time range.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country for which the historical GDP data is required."}, "start_year": {"type": "integer", "description": "Starting year of the period for which GDP data is required."}, "end_year": {"type": "integer", "description": "Ending year of the period for which GDP data is required."}}, "required": ["country", "start_year", "end_year"]}}, {"name": "maps.get_distance_duration", "description": "Retrieve the travel distance and estimated travel time from one location to another via car", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "Starting point of the journey"}, "end_location": {"type": "string", "description": "Ending point of the journey"}, "traffic": {"type": "boolean", "description": "If true, considers current traffic. Default is false."}}, "required": ["start_location", "end_location"]}}]}
{"id": "multiple_178", "question": [[{"role": "user", "content": "Find a Card of rank 'Queen' and suit 'Hearts' in the deck."}]], "function": [{"name": "currency_exchange.convert", "description": "Convert an amount from a base currency to a target currency based on the current exchange rate.", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The currency to convert from."}, "target_currency": {"type": "string", "description": "The currency to convert to."}, "amount": {"type": "float", "description": "The amount in base currency to convert"}}, "required": ["base_currency", "target_currency", "amount"]}}, {"name": "local_nursery.find", "description": "Locate local nurseries based on location and plant types availability.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city or locality where the nursery needs to be located."}, "plant_types": {"type": "array", "items": {"type": "string", "enum": ["Annual", "Perennial", "Shrub", "Tree", "Herbs", "Fruits"]}, "description": "Type of plants the nursery should provide."}}, "required": ["location", "plant_types"]}}, {"name": "find_card_in_deck", "description": "Locate a particular card in a deck based on rank and suit.", "parameters": {"type": "dict", "properties": {"rank": {"type": "string", "description": "Rank of the card (e.g. Ace, Two, King)."}, "suit": {"type": "string", "description": "Suit of the card (e.g. Hearts, Spades, Diamonds, Clubs)."}, "deck": {"type": "array", "items": {"type": "dict", "properties": {"rank": {"type": "string"}, "suit": {"type": "string"}}}, "description": "Deck of cards. If not provided, the deck will be a standard 52 card deck by default"}}, "required": ["rank", "suit"]}}, {"name": "recipe.unit_conversion", "description": "Convert a value from one kitchen unit to another for cooking purposes.", "parameters": {"type": "dict", "properties": {"value": {"type": "float", "description": "The value to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from. Supports 'teaspoon', 'tablespoon', 'cup', etc."}, "to_unit": {"type": "string", "description": "The unit to convert to. Supports 'teaspoon', 'tablespoon', 'cup', etc."}, "precision": {"type": "integer", "description": "The precision to round the output to, in case of a non-integer result. Optional, default is 0."}}, "required": ["value", "from_unit", "to_unit"]}}]}
{"id": "multiple_179", "question": [[{"role": "user", "content": "What is the probability of getting a full house in poker?"}]], "function": [{"name": "poker_probability.full_house", "description": "Calculate the probability of getting a full house in a poker game.", "parameters": {"type": "dict", "properties": {"deck_size": {"type": "integer", "description": "The size of the deck. Default is 52."}, "hand_size": {"type": "integer", "description": "The size of the hand. Default is 5."}}, "required": ["deck_size", "hand_size"]}}, {"name": "hospital.locate", "description": "Locate nearby hospitals based on location and radius. Options to include specific departments are available.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Denver, CO"}, "radius": {"type": "integer", "description": "The radius within which you want to find the hospital in kms."}, "department": {"type": "string", "description": "Specific department within the hospital. Default is ''.", "enum": ["General Medicine", "Emergency", "Pediatrics", "Cardiology", "Orthopedics"]}}, "required": ["location", "radius"]}}, {"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}]}
{"id": "multiple_180", "question": [[{"role": "user", "content": "Fetch player statistics of 'Zelda' on Switch for user 'Sam'."}]], "function": [{"name": "flight.book", "description": "Book a direct flight for a specific date and time from departure location to destination location.", "parameters": {"type": "dict", "properties": {"departure_location": {"type": "string", "description": "The location you are departing from."}, "destination_location": {"type": "string", "description": "The location you are flying to."}, "date": {"type": "string", "description": "The date of the flight. Accepts standard date format e.g., 2022-04-28."}, "time": {"type": "string", "description": "Preferred time of flight. Default is ''"}, "direct_flight": {"type": "boolean", "description": "If set to true, only direct flights will be searched. Default is false"}}, "required": ["departure_location", "destination_location", "date"]}}, {"name": "event_finder.find_upcoming", "description": "Find upcoming events of a specific genre in a given location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the search will take place, e.g. New York, NY."}, "genre": {"type": "string", "description": "The genre of events."}, "days_ahead": {"type": "integer", "description": "The number of days from now to include in the search.", "default": 7}}, "required": ["location", "genre"]}}, {"name": "get_scientist_for_discovery", "description": "Retrieve the scientist's name who is credited for a specific scientific discovery or theory.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The scientific discovery or theory."}}, "required": ["discovery"]}}, {"name": "game_stats.fetch_player_statistics", "description": "Fetch player statistics for a specific video game for a given user.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the video game."}, "username": {"type": "string", "description": "The username of the player."}, "platform": {"type": "string", "description": "The platform user is playing on.", "default": "PC"}}, "required": ["game", "username"]}}]}
{"id": "multiple_181", "question": [[{"role": "user", "content": "Get me the details of the last game played by Liverpool F.C. Include its statistics."}]], "function": [{"name": "building.get_dimensions", "description": "Retrieve the dimensions of a specific building based on its name.", "parameters": {"type": "dict", "properties": {"building_name": {"type": "string", "description": "The name of the building."}, "unit": {"type": "string", "description": "The unit in which you want the dimensions. Default is meter.", "enum": ["meter", "feet"]}}, "required": ["building_name", "unit"]}}, {"name": "plot_sine_wave", "description": "Plot a sine wave for a given frequency in a given range.", "parameters": {"type": "dict", "properties": {"start_range": {"type": "float", "description": "Start of the range in radians."}, "end_range": {"type": "float", "description": "End of the range in radians."}, "frequency": {"type": "float", "description": "Frequency of the sine wave in Hz."}, "amplitude": {"type": "float", "description": "Amplitude of the sine wave. Default is 1."}, "phase_shift": {"type": "float", "description": "Phase shift of the sine wave in radians. Default is 0."}}, "required": ["start_range", "end_range", "frequency"]}}, {"name": "random_forest.train", "description": "Train a Random Forest Model on given data", "parameters": {"type": "dict", "properties": {"n_estimators": {"type": "integer", "description": "The number of trees in the forest."}, "max_depth": {"type": "integer", "description": "The maximum depth of the tree."}, "data": {"type": "any", "description": "The training data for the model."}}, "required": ["n_estimators", "max_depth", "data"]}}, {"name": "soccer.get_last_match", "description": "Retrieve the details of the last match played by a specified soccer club.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the soccer club."}, "include_stats": {"type": "boolean", "description": "If true, include match statistics like possession, shots on target etc. Default is false."}}, "required": ["team_name"]}}]}
{"id": "multiple_182", "question": [[{"role": "user", "content": "Find me a multiplayer game with rating above 4.5 and compatible with Windows 10."}]], "function": [{"name": "geo_distance.calculate", "description": "Calculate the geographic distance between two given locations.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting location for the distance calculation."}, "end_location": {"type": "string", "description": "The destination location for the distance calculation."}, "units": {"type": "string", "description": "Optional. The desired units for the resulting distance ('miles' or 'kilometers'). Defaults to 'miles'."}}, "required": ["start_location", "end_location"]}}, {"name": "multiplayer_game_finder", "description": "Locate multiplayer games that match specific criteria such as rating, platform compatibility, genre, etc.", "parameters": {"type": "dict", "properties": {"platform": {"type": "string", "description": "The platform you want the game to be compatible with, e.g. Windows 10, PS5."}, "rating": {"type": "float", "description": "Desired minimum game rating on a 5.0 scale."}, "genre": {"type": "string", "description": "Desired game genre, e.g. Action, Adventure, Racing. Default is ''.", "enum": ["Action", "Adventure", "Racing", "Strategy", "Simulation"]}}, "required": ["platform", "rating"]}}, {"name": "send_email", "description": "Send an email to the specified email address.", "parameters": {"type": "dict", "properties": {"to": {"type": "string", "description": "The email address to send to."}, "subject": {"type": "string", "description": "The subject of the email."}, "body": {"type": "string", "description": "The body content of the email."}, "cc": {"type": "string", "description": "The email address to carbon copy. Default is ''."}, "bcc": {"type": "string", "description": "The email address to blind carbon copy. Default is ''."}}, "required": ["to", "subject", "body"]}}, {"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}
{"id": "multiple_183", "question": [[{"role": "user", "content": "How many calories in the Beef Lasagna Recipe from Foodnetwork.com?"}]], "function": [{"name": "get_stock_price", "description": "Retrieves the current stock price of the specified companies", "parameters": {"type": "dict", "properties": {"company_names": {"type": "array", "items": {"type": "string"}, "description": "The list of companies for which to retrieve the stock price."}}, "required": ["company_names"]}}, {"name": "get_team_ranking", "description": "Retrieve the FIFA ranking of a specific soccer team for a certain year.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the soccer team."}, "year": {"type": "integer", "description": "The year for which the ranking is to be retrieved."}, "gender": {"type": "string", "description": "The gender of the team. It can be either 'men' or 'women'. Default is 'men'."}}, "required": ["team_name", "year"]}}, {"name": "recipe_info.get_calories", "description": "Retrieve the amount of calories from a specific recipe in a food website.", "parameters": {"type": "dict", "properties": {"website": {"type": "string", "description": "The food website that has the recipe."}, "recipe": {"type": "string", "description": "Name of the recipe."}, "optional_meal_time": {"type": "string", "description": "Specific meal time of the day for the recipe (optional, could be 'Breakfast', 'Lunch', 'Dinner') Default is ''"}}, "required": ["website", "recipe"]}}]}
{"id": "multiple_184", "question": [[{"role": "user", "content": "Give me a recipe for a vegetarian pasta with cheese for 2 servings."}]], "function": [{"name": "detailed_weather_forecast", "description": "Retrieve a detailed weather forecast for a specific location and duration including optional precipitation details.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the weather for."}, "duration": {"type": "integer", "description": "Duration in hours for the detailed forecast."}, "include_precipitation": {"type": "boolean", "description": "Whether to include precipitation data in the forecast. Default is false."}}, "required": ["location", "duration"]}}, {"name": "recipe_search", "description": "Search for a recipe given dietary restriction, ingredients, and number of servings.", "parameters": {"type": "dict", "properties": {"dietary_restriction": {"type": "string", "description": "The dietary restriction, e.g., 'Vegetarian'."}, "ingredients": {"type": "array", "items": {"type": "string"}, "description": "The list of ingredients."}, "servings": {"type": "integer", "description": "The number of servings the recipe should make"}}, "required": ["dietary_restriction", "ingredients", "servings"]}}, {"name": "get_time_difference", "description": "Get the time difference between two places.", "parameters": {"type": "dict", "properties": {"place1": {"type": "string", "description": "The first place for time difference."}, "place2": {"type": "string", "description": "The second place for time difference."}}, "required": ["place1", "place2"]}}]}
{"id": "multiple_185", "question": [[{"role": "user", "content": "Find the closest sushi restaurant with a patio in Boston."}]], "function": [{"name": "board_game.chess.get_top_players", "description": "Find top chess players in a location based on rating.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city you want to find the players from."}, "minimum_rating": {"type": "integer", "description": "Minimum rating to filter the players."}, "number_of_players": {"type": "integer", "default": 10, "description": "Number of players you want to retrieve, default value is 10"}}, "required": ["location", "minimum_rating"]}}, {"name": "restaurant_search.find_closest", "description": "Locate the closest sushi restaurant based on certain criteria, such as the presence of a patio.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city, for instance Boston, MA"}, "cuisine": {"type": "string", "description": "Type of food like Sushi."}, "amenities": {"type": "array", "items": {"type": "string", "enum": ["Patio", "Wi-Fi", "Happy Hour", "Wheelchair Accessible"]}, "description": "Preferred amenities in the restaurant. Default is empty array."}}, "required": ["location", "cuisine"]}}]}
{"id": "multiple_186", "question": [[{"role": "user", "content": "Find me a vegan recipe for brownies which prep time is under 30 minutes."}]], "function": [{"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}, {"name": "science_history.get_discovery_details", "description": "Retrieve the details of a scientific discovery based on the discovery name.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The name of the discovery, e.g. Gravity"}, "method_used": {"type": "string", "description": "The method used for the discovery, default value is 'default' which gives the most accepted method."}}, "required": ["discovery"]}}]}
{"id": "multiple_187", "question": [[{"role": "user", "content": "Check the price of tomatoes and lettuce at the Whole Foods in Los Angeles."}]], "function": [{"name": "geometry.area_circle", "description": "Calculate the area of a circle given the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "float", "description": "The radius of the circle."}, "units": {"type": "string", "description": "The units in which the radius is measured (defaults to meters).", "default": "meters"}}, "required": ["radius"]}}, {"name": "find_recipes", "description": "Find recipes based on dietary restrictions, meal type, and preferred ingredients.", "parameters": {"type": "dict", "properties": {"diet": {"type": "string", "description": "The dietary restrictions, e.g., 'vegan', 'gluten-free'."}, "meal_type": {"type": "string", "description": "The type of meal, e.g., 'dinner', 'breakfast'."}, "ingredients": {"type": "array", "items": {"type": "string"}, "description": "The preferred ingredients. If left blank, it will return general recipes. Default is empty array."}}, "required": ["diet", "meal_type"]}}, {"name": "whole_foods.check_price", "description": "Check the price of items at a specific Whole Foods location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Location of the Whole Foods store."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items for which the price needs to be checked."}}, "required": ["location", "items"]}}, {"name": "calculate_shortest_distance", "description": "Calculate the shortest driving distance between two locations.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting location for the drive."}, "end_location": {"type": "string", "description": "The destination location for the drive."}, "route_preference": {"type": "string", "enum": ["Shortest", "Scenic"], "description": "The preferred type of route."}}, "required": ["start_location", "end_location", "route_preference"]}}]}
{"id": "multiple_188", "question": [[{"role": "user", "content": "Find the grocery store closest to Berkeley that has at least a 4.5 star rating, selling tomatoes and also pet food."}]], "function": [{"name": "sentiment_analysis", "description": "Perform sentiment analysis on a given piece of text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text on which to perform sentiment analysis."}, "language": {"type": "string", "description": "The language in which the text is written."}}, "required": ["text", "language"]}}, {"name": "psych_research.get_preference", "description": "Gathers research data on public preference between two options, based on societal category.", "parameters": {"type": "dict", "properties": {"category": {"type": "string", "description": "The societal category the preference data is about. E.g. reading, transportation, food"}, "option_one": {"type": "string", "description": "The first option people could prefer."}, "option_two": {"type": "string", "description": "The second option people could prefer."}, "demographic": {"type": "string", "description": "Specific demographic of society to narrow down the research.", "default": "all"}}, "required": ["category", "option_one", "option_two"]}}, {"name": "grocery_store.find_best", "description": "Find the closest high-rated grocery stores based on certain product availability.", "parameters": {"type": "dict", "properties": {"my_location": {"type": "string", "description": "The current location of the user."}, "rating": {"type": "float", "description": "The minimum required store rating. Default is 0.0."}, "products": {"type": "array", "items": {"type": "string"}, "description": "Required products in a list."}}, "required": ["my_location", "products"]}}, {"name": "train_random_forest_classifier", "description": "Train a Random Forest classifier with the specified parameters.", "parameters": {"type": "dict", "properties": {"dataset": {"type": "string", "description": "The dataset to train the classifier on."}, "max_depth": {"type": "integer", "description": "The maximum depth of the trees in the forest."}, "n_estimators": {"type": "integer", "description": "The number of trees in the forest."}}, "required": ["dataset", "max_depth", "n_estimators"]}}]}
{"id": "multiple_189", "question": [[{"role": "user", "content": "Convert time 3pm from New York time zone to London time zone."}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'USA'"}}, "required": ["energy_type", "usage_duration"]}}, {"name": "timezone.convert", "description": "Convert time from one time zone to another.", "parameters": {"type": "dict", "properties": {"time": {"type": "string", "description": "The local time you want to convert, e.g. 3pm"}, "from_timezone": {"type": "string", "description": "The time zone you want to convert from."}, "to_timezone": {"type": "string", "description": "The time zone you want to convert to."}}, "required": ["time", "from_timezone", "to_timezone"]}}]}
{"id": "multiple_190", "question": [[{"role": "user", "content": "Book a single room for two nights at the Hilton Hotel in Chicago, starting from 10th December 2022."}]], "function": [{"name": "get_stock_price", "description": "Retrieves the current stock price of the specified companies", "parameters": {"type": "dict", "properties": {"company_names": {"type": "array", "items": {"type": "string"}, "description": "The list of companies for which to retrieve the stock price."}}, "required": ["company_names"]}}, {"name": "currency_converter", "description": "Calculates the cost in target currency given the amount in base currency and exchange rate", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The currency to convert from."}, "target_currency": {"type": "string", "description": "The currency to convert to."}, "amount": {"type": "float", "description": "The amount in base currency"}}, "required": ["base_currency", "target_currency", "amount"]}}, {"name": "book_hotel", "description": "Book a room of specified type for a particular number of nights at a specific hotel, starting from a specified date.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city in which the hotel is located."}, "room_type": {"type": "string", "description": "The type of room to be booked."}, "start_date": {"type": "string", "description": "The start date for the booking."}, "nights": {"type": "integer", "description": "The number of nights for which the booking is to be made."}}, "required": ["hotel_name", "location", "room_type", "start_date", "nights"]}}]}
{"id": "multiple_191", "question": [[{"role": "user", "content": "Book a luxury room in Hotel Paradise, Las Vegas, with a city view for 3 days starting from May 12, 2022."}]], "function": [{"name": "random.normalvariate", "description": "Generates a random number from a normal distribution given the mean and standard deviation.", "parameters": {"type": "dict", "properties": {"mu": {"type": "float", "description": "Mean of the normal distribution."}, "sigma": {"type": "float", "description": "Standard deviation of the normal distribution."}}, "required": ["mu", "sigma"]}}, {"name": "get_personality_traits", "description": "Retrieve the personality traits for a specific personality type, including their strengths and weaknesses.", "parameters": {"type": "dict", "properties": {"type": {"type": "string", "description": "The personality type."}, "traits": {"type": "array", "items": {"type": "string", "enum": ["strengths", "weaknesses"]}, "description": "List of traits to be retrieved, default is ['strengths', 'weaknesses']."}}, "required": ["type"]}}, {"name": "elephant_population_estimate", "description": "Estimate future population of elephants given current population and growth rate.", "parameters": {"type": "dict", "properties": {"current_population": {"type": "integer", "description": "The current number of elephants."}, "growth_rate": {"type": "float", "description": "The annual population growth rate of elephants."}, "years": {"type": "integer", "description": "The number of years to project the population."}}, "required": ["current_population", "growth_rate", "years"]}}, {"name": "book_hotel", "description": "Book a room in a specific hotel with particular preferences", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The location of the hotel."}, "room_type": {"type": "string", "description": "The type of room preferred."}, "start_date": {"type": "string", "description": "The starting date of the stay in format MM-DD-YYYY."}, "stay_duration": {"type": "integer", "description": "The duration of the stay in days."}, "view": {"type": "string", "description": "The preferred view from the room, can be ignored if no preference. If none provided, assumes no preference.", "default": "No preference"}}, "required": ["hotel_name", "location", "room_type", "start_date", "stay_duration"]}}]}
{"id": "multiple_192", "question": [[{"role": "user", "content": "Convert 150 Euros to Canadian dollars."}]], "function": [{"name": "currency_conversion.convert", "description": "Convert a value from one currency to another.", "parameters": {"type": "dict", "properties": {"amount": {"type": "integer", "description": "The amount to be converted."}, "from_currency": {"type": "string", "description": "The currency to convert from."}, "to_currency": {"type": "string", "description": "The currency to convert to."}}, "required": ["amount", "from_currency", "to_currency"]}}, {"name": "calc_absolute_pressure", "description": "Calculates the absolute pressure from gauge and atmospheric pressures.", "parameters": {"type": "dict", "properties": {"atm_pressure": {"type": "float", "description": "The atmospheric pressure in atmospheres (atm). Default is 1 atm if not provided."}, "gauge_pressure": {"type": "float", "description": "The gauge pressure in atmospheres (atm). Must be provided."}}, "required": ["gauge_pressure"]}}, {"name": "calculate_displacement", "description": "Calculates the displacement of an object in motion given initial velocity, time, and acceleration.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "float", "description": "The initial velocity of the object in m/s."}, "time": {"type": "float", "description": "The time in seconds that the object has been in motion."}, "acceleration": {"type": "float", "description": "The acceleration of the object in m/s^2.", "default": 0}}, "required": ["initial_velocity", "time"]}}]}
{"id": "multiple_193", "question": [[{"role": "user", "content": "Get me the travel distance and duration from the Eiffel Tower to the Louvre Museum"}]], "function": [{"name": "math.factorial", "description": "Calculate the factorial of a given number.", "parameters": {"type": "dict", "properties": {"number": {"type": "float", "description": "The number for which factorial needs to be calculated."}}, "required": ["number"]}}, {"name": "grocery_store.find_best", "description": "Find the closest high-rated grocery stores based on certain product availability.", "parameters": {"type": "dict", "properties": {"my_location": {"type": "string", "description": "The current location of the user."}, "rating": {"type": "float", "description": "The minimum required store rating. Default is 0.0."}, "products": {"type": "array", "items": {"type": "string"}, "description": "Required products in a list."}}, "required": ["my_location", "products"]}}, {"name": "restaurant.find_nearby", "description": "Locate nearby restaurants based on specific criteria like cuisine type.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine in restaurant."}, "max_distance": {"type": "float", "description": "Maximum distance (in miles) within which to search for restaurants. Default is 5."}}, "required": ["location", "cuisine"]}}, {"name": "maps.get_distance_duration", "description": "Retrieve the travel distance and estimated travel time from one location to another via car", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "Starting point of the journey"}, "end_location": {"type": "string", "description": "Ending point of the journey"}, "traffic": {"type": "boolean", "description": "If true, considers current traffic. Default is false."}}, "required": ["start_location", "end_location"]}}]}
{"id": "multiple_194", "question": [[{"role": "user", "content": "What are the opening hours of the Metropolitan Museum of Art on Saturday?"}]], "function": [{"name": "discoverer.get", "description": "Retrieve the name of the discoverer of an element based on its name.", "parameters": {"type": "dict", "properties": {"element_name": {"type": "string", "description": "The name of the element."}, "year": {"type": "integer", "description": "Optional parameter that refers to the year of discovery. It could be helpful in case an element was discovered more than once. Default is 0."}, "first": {"type": "boolean", "default": true, "description": "Optional parameter indicating if the first discoverer's name should be retrieved."}}, "required": ["element_name"]}}, {"name": "lawsuit.check_case", "description": "Verify the details of a lawsuit case and check its status using case ID.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "integer", "description": "The identification number of the lawsuit case."}, "closed_status": {"type": "boolean", "description": "The status of the lawsuit case to be verified."}}, "required": ["case_id", "closed_status"]}}, {"name": "get_museum_hours", "description": "Retrieve opening hours of a specified museum for the specified day.", "parameters": {"type": "dict", "properties": {"museum_name": {"type": "string", "description": "The name of the museum."}, "day": {"type": "string", "description": "Day of the week.", "enum": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]}}, "required": ["museum_name", "day"]}}, {"name": "monopoly_odds_calculator", "description": "Calculates the probability of rolling a certain sum with two dice, commonly used in board game like Monopoly.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number for which the odds are calculated."}, "dice_number": {"type": "integer", "description": "The number of dice involved in the roll."}, "dice_faces": {"type": "integer", "description": "The number of faces on a single die. Default is 6 for standard six-faced die."}}, "required": ["number", "dice_number"]}}]}
{"id": "multiple_195", "question": [[{"role": "user", "content": "Calculate the heat capacity at constant pressure for air, given its temperature is 298K and volume is 10 m^3."}]], "function": [{"name": "get_lawsuit_details", "description": "Retrieve the detailed information about a lawsuit based on its case number and the court location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The case number of the lawsuit."}, "court_location": {"type": "string", "description": "The location of the court where the case is filed."}, "additional_details": {"type": "array", "items": {"type": "string", "enum": ["attorneys", "plaintiffs", "defendants", "charges", "court_updates"]}, "description": "Optional. Array containing additional details to be fetched. Default is empty array."}}, "required": ["case_number", "court_location"]}}, {"name": "get_team_rank", "description": "Get the team ranking in a sports league based on season and type.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "league": {"type": "string", "description": "The name of the league in which the team competes."}, "season": {"type": "string", "description": "The season for which the team's ranking is sought."}, "type": {"type": "string", "description": "Type of the season: regular or playoff.", "enum": ["regular", "playoff"]}}, "required": ["team_name", "league", "season", "type"]}}, {"name": "calc_heat_capacity", "description": "Calculate the heat capacity at constant pressure of air using its temperature and volume.", "parameters": {"type": "dict", "properties": {"temp": {"type": "integer", "description": "The temperature of the gas in Kelvin."}, "volume": {"type": "integer", "description": "The volume of the gas in m^3."}, "gas": {"type": "string", "description": "Type of gas, with air as default."}}, "required": ["temp", "volume"]}}]}
{"id": "multiple_196", "question": [[{"role": "user", "content": "What are the names of proteins found in the plasma membrane?"}]], "function": [{"name": "locate_tallest_mountains", "description": "Find the tallest mountains within a specified radius of a location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city from which to calculate distance."}, "radius": {"type": "float", "description": "The radius within which to find mountains, measured in kilometers."}, "amount": {"type": "integer", "description": "The number of mountains to find, listed from tallest to smallest."}}, "required": ["location", "radius", "amount"]}}, {"name": "calculate_electric_field", "description": "Calculate the electric field produced by a charge at a certain distance.", "parameters": {"type": "dict", "properties": {"charge": {"type": "float", "description": "Charge in coulombs producing the electric field."}, "distance": {"type": "float", "description": "Distance from the charge in meters where the field is being measured."}, "permitivity": {"type": "float", "description": "Permitivity of the space where field is being calculated, default is for vacuum."}}, "required": ["charge", "distance"]}}, {"name": "calculate_genotype_frequency", "description": "Calculate the frequency of homozygous dominant genotype based on the allele frequency using Hardy Weinberg Principle.", "parameters": {"type": "dict", "properties": {"allele_frequency": {"type": "float", "description": "The frequency of the dominant allele in the population."}, "genotype": {"type": "string", "description": "The genotype which frequency is needed, default is homozygous dominant. ", "enum": ["AA", "Aa", "aa"]}}, "required": ["allele_frequency", "genotype"]}}, {"name": "cellbio.get_proteins", "description": "Get the list of proteins in a specific cell compartment.", "parameters": {"type": "dict", "properties": {"cell_compartment": {"type": "string", "description": "The specific cell compartment."}, "include_description": {"type": "boolean", "description": "Set true if you want a brief description of each protein.", "default": "false"}}, "required": ["cell_compartment"]}}]}
{"id": "multiple_197", "question": [[{"role": "user", "content": "Find the type of gene mutation based on SNP (Single Nucleotide Polymorphism) ID rs6034464."}]], "function": [{"name": "create_player_profile", "description": "Create a new player profile with character name, class and starting level.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The desired name of the player."}, "_class": {"type": "string", "description": "The character class for the player"}, "starting_level": {"type": "integer", "description": "The starting level for the player", "default": 1}}, "required": ["player_name", "_class"]}}, {"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is an empty array"}}, "required": ["loc", "product_list"]}}, {"name": "mutation_type.find", "description": "Finds the type of a genetic mutation based on its SNP (Single Nucleotide Polymorphism) ID.", "parameters": {"type": "dict", "properties": {"snp_id": {"type": "string", "description": "The ID of the Single Nucleotide Polymorphism (SNP) mutation."}, "species": {"type": "string", "description": "Species in which the SNP occurs, default is 'Homo sapiens' (Humans)."}}, "required": ["snp_id"]}}, {"name": "find_restaurants", "description": "Locate nearby restaurants based on location and food preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The specific location or area."}, "food_type": {"type": "string", "description": "The type of food preferred."}, "number": {"type": "integer", "description": "Number of results to return."}, "dietary_requirements": {"type": "array", "items": {"type": "string"}, "description": "Special dietary requirements, e.g. vegan, gluten-free.", "default": "None"}}, "required": ["location", "food_type", "number"]}}]}
{"id": "multiple_198", "question": [[{"role": "user", "content": "What is the genotype frequency of AA genotype in a population, given that allele frequency of A is 0.3?"}]], "function": [{"name": "calculate_genotype_frequency", "description": "Calculate the frequency of homozygous dominant genotype based on the allele frequency using Hardy Weinberg Principle.", "parameters": {"type": "dict", "properties": {"allele_frequency": {"type": "float", "description": "The frequency of the dominant allele in the population."}, "genotype": {"type": "string", "description": "The genotype which frequency is needed, default is homozygous dominant. ", "enum": ["AA", "Aa", "aa"]}}, "required": ["allele_frequency", "genotype"]}}, {"name": "hotel_booking", "description": "Books a hotel room for a specific date range.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY."}, "start_date": {"type": "string", "description": "The start date of the reservation. Use format 'YYYY-MM-DD'."}, "end_date": {"type": "string", "description": "The end date of the reservation. Use format 'YYYY-MM-DD'."}, "rooms": {"type": "integer", "default": 1, "description": "The number of rooms to reserve."}}, "required": ["hotel_name", "location", "start_date", "end_date"]}}, {"name": "get_highest_scoring_player", "description": "Retrieve the highest scoring player in a specific game and season.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The game in which you want to find the highest scoring player."}, "season": {"type": "string", "description": "The season during which the high score was achieved."}, "region": {"type": "string", "description": "The geographical region in which the game is being played (Optional). Default is 'all'"}}, "required": ["game", "season"]}}, {"name": "science_history.get_invention", "description": "Retrieve the inventor and year of invention based on the invention's name.", "parameters": {"type": "dict", "properties": {"invention_name": {"type": "string", "description": "The name of the invention."}, "want_year": {"type": "boolean", "default": false, "description": "Return the year of invention if set to true."}}, "required": ["invention_name", "want_year"]}}]}
{"id": "multiple_199", "question": [[{"role": "user", "content": "Predict the growth of forest in Yellowstone for the next 5 years including human impact."}]], "function": [{"name": "forest_growth_forecast", "description": "Predicts the forest growth over the next N years based on current trends.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location where you want to predict forest growth."}, "years": {"type": "integer", "description": "The number of years for the forecast."}, "include_human_impact": {"type": "boolean", "description": "Whether or not to include the impact of human activities in the forecast. If not provided, defaults to false."}}, "required": ["location", "years"]}}, {"name": "db_fetch_records", "description": "Fetch records from a specified database table based on certain conditions.", "parameters": {"type": "dict", "properties": {"database_name": {"type": "string", "description": "The name of the database."}, "table_name": {"type": "string", "description": "The name of the table from which records need to be fetched."}, "conditions": {"type": "dict", "properties": {"department": {"type": "string", "description": "The name of the department of students."}, "school": {"type": "string", "description": "The name of the school students are enrolled in."}}, "description": "The conditions based on which records are to be fetched."}, "fetch_limit": {"type": "integer", "description": "Limits the number of records to be fetched. If left empty, it fetches all records. (Optional) Default is 0."}}, "required": ["database_name", "table_name", "conditions"]}}]}