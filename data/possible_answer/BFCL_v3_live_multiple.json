{"id": "live_multiple_0-0-0", "ground_truth": [{"ChaDri.change_drink": {"drink_id": ["latte"], "new_preferences": [{"size": ["large"], "milk_type": ["coconut"], "sweetness_level": ["extra"], "temperature": ["hot"], "special_instructions": ["served boiling hot", "serve boiling hot", "boiling hot", "served 'boiling hot'"]}]}}]}
{"id": "live_multiple_1-0-1", "ground_truth": [{"ChaDri.change_drink": {"drink_id": ["1234"], "new_preferences": [{"sweetness_level": ["none"], "temperature": ["hot"]}]}}]}
{"id": "live_multiple_2-1-0", "ground_truth": [{"uber.ride": {"loc": ["2150 Shattuck Ave, Berkeley, CA"], "type": ["plus"], "time": [10]}}]}
{"id": "live_multiple_3-2-0", "ground_truth": [{"api.weather": {"loc": ["Ha Noi, Vietnam"]}}]}
{"id": "live_multiple_4-2-1", "ground_truth": [{"uber.ride": {"loc": ["123 Hanoi Street"], "type": ["plus"], "time": [10]}}]}
{"id": "live_multiple_5-3-0", "ground_truth": [{"get_current_weather": {"location": ["Shanghai, China"], "unit": ["", "metric"]}}]}
{"id": "live_multiple_6-3-1", "ground_truth": [{"create_workspace": {"name": ["DevelopmentEnv"], "base_branch": ["feature-branch"]}}]}
{"id": "live_multiple_7-3-2", "ground_truth": [{"create_workspace": {"name": ["DevelopersHub"], "base_branch": ["master"]}}]}
{"id": "live_multiple_8-4-0", "ground_truth": [{"cookbook.search_recipe": {"keyword": ["steak"], "cuisine": ["Indian"], "max_results": ["", 10]}}]}
{"id": "live_multiple_9-4-1", "ground_truth": [{"HNA_WQA.search": {"keyword": ["Imjin war"], "result_format": ["", "text"], "language": ["", "EN"], "max_results": ["", 10]}}]}
{"id": "live_multiple_10-4-2", "ground_truth": [{"ControlAppliance.execute": {"command": ["\ub2e4\uc6a9\ub3c4\uc2e4, \ud1b5\ub3cc\uc774, \uc911\uc9c0"]}}]}
{"id": "live_multiple_11-4-3", "ground_truth": [{"HNA_WQA.search": {"keyword": ["Ji Sung Park", "Park Ji Sung", "Ji Sung Park footballer", "Park Ji-sung footballer"], "result_format": ["", "text"], "language": ["", "EN"], "max_results": ["", 10]}}]}
{"id": "live_multiple_12-4-4", "ground_truth": [{"HNA_WQA.search": {"keyword": ["Ji Sung Park", "Park Ji Sung", "Ji Sung Park footballer", "Park Ji-sung footballer"], "result_format": ["", "text"], "language": ["", "EN"], "max_results": ["", 10]}}]}
{"id": "live_multiple_13-4-5", "ground_truth": [{"OpenWeatherMap.get_current_weather": {"location": ["Santa Cruz, USA"], "units": ["imperial"], "api_key": ["", "YOUR_API_KEY_HERE"]}}]}
{"id": "live_multiple_14-4-6", "ground_truth": [{"ControlAppliance.execute": {"command": ["\ub2e4\uc6a9\ub3c4\uc2e4, \ud1b5\ub3cc\uc774, \uc911\uc9c0"]}}]}
{"id": "live_multiple_15-4-7", "ground_truth": [{"HNA_NEWS.search": {"keyword": ["Son's latest goal"], "category": ["Sports"], "date_range": ["", "null"], "sort_by": ["", "date"], "language": ["", "EN"]}}]}
{"id": "live_multiple_16-4-8", "ground_truth": [{"HNA_NEWS.search": {"keyword": ["\ubc15\uc9c0\uc131"], "category": ["", "General"], "date_range": ["", "null"], "sort_by": ["", "date"], "language": ["", "EN"]}}]}
{"id": "live_multiple_17-4-9", "ground_truth": [{"cookbook.search_recipe": {"keyword": ["sikhae"], "cuisine": ["", "Italian"], "max_results": ["", 10]}}]}
{"id": "live_multiple_18-4-10", "ground_truth": [{"HNA_NEWS.search": {"keyword": ["artificial intelligence", "ai"], "category": ["", "General"], "date_range": ["2023-10-01 to 2023-10-07"], "sort_by": ["", "date"], "language": ["", "EN"]}}]}
{"id": "live_multiple_19-4-11", "ground_truth": [{"ControlAppliance.execute": {"command": ["\uac70\uc2e4, \uc5d0\uc5b4\ucee8, \uc2e4\ud589"]}}]}
{"id": "live_multiple_20-4-12", "ground_truth": [{"ControlAppliance.execute": {"command": ["\ub2e4\uc6a9\ub3c4\uc2e4, \ud1b5\ub3cc\uc774, \uc911\uc9c0"]}}]}
{"id": "live_multiple_21-4-13", "ground_truth": [{"cookbook.search_recipe": {"keyword": ["\uc2a4\ud14c\uc774\ud06c"], "cuisine": ["", "Italian"], "max_results": ["", 10]}}]}
{"id": "live_multiple_22-4-14", "ground_truth": [{"HNA_WQA.search": {"keyword": ["\uc784\uc9c4\uc65c\ub780"], "result_format": ["", "text"], "language": ["", "EN"], "max_results": ["", 10]}}]}
{"id": "live_multiple_23-5-0", "ground_truth": [{"search_products": {"color": ["red"], "size": ["8"], "category": ["shoes"], "price_min": ["", 0.0], "price_max": ["", null], "brand": ["", null]}}]}
{"id": "live_multiple_24-5-1", "ground_truth": [{"search_products": {"color": ["", null], "size": ["XL", "Extra Large"], "category": ["clothing"], "price_min": ["", 0.0], "price_max": ["", null], "brand": ["", null]}}]}
{"id": "live_multiple_25-6-0", "ground_truth": [{"recall_memory_search": {"query": ["when is shishir's birthday", "when's shishir's birthday", "shishir birthday", "shishir's birthday"], "page": ["", 0], "request_heartbeat": ["", false]}}]}
{"id": "live_multiple_26-6-1", "ground_truth": [{"recall_memory_search": {"query": ["Shishir Patil's birthday", "Shishir Patil birthday"], "page": ["", 0], "request_heartbeat": ["", false]}}]}
{"id": "live_multiple_27-7-0", "ground_truth": [{"add": {"a": [100], "b": [-40]}}]}
{"id": "live_multiple_28-8-0", "ground_truth": [{"my_core_function": {"name": ["Balkrushn"]}}]}
{"id": "live_multiple_29-9-0", "ground_truth": [{"multiply": {"a": [3], "b": [10]}}]}
{"id": "live_multiple_30-10-0", "ground_truth": [{"duck_duck_go.search": {"query": ["LangFuse and its application in generative AI projects", "Find information on LangFuse and its application in generative AI projects"], "format": ["", "json"], "no_redirect": ["", false], "no_html": ["", false]}}]}
{"id": "live_multiple_31-10-1", "ground_truth": [{"duck_duck_go.search": {"query": ["trending items"], "format": ["", "json"], "no_redirect": ["", false], "no_html": ["", false]}}]}
{"id": "live_multiple_32-10-2", "ground_truth": [{"sub": {"a": [100], "b": [40]}}]}
{"id": "live_multiple_33-10-3", "ground_truth": [{"multiply": {"a": [3], "b": [10]}}]}
{"id": "live_multiple_34-11-0", "ground_truth": [{"get_news_report": {"location": ["Letterkenny, Ireland"]}}]}
{"id": "live_multiple_35-11-1", "ground_truth": [{"get_tesco_locations": {"location": ["Letterkenny, Ireland"], "radius": ["", 10], "limit": ["", 5]}}]}
{"id": "live_multiple_36-12-0", "ground_truth": [{"todo_add": {"content": ["go for shopping at 9 pm"]}}]}
{"id": "live_multiple_37-13-0", "ground_truth": [{"todo_delete": {"content": ["go for shopping at 9 pm"]}}]}
{"id": "live_multiple_38-14-0", "ground_truth": [{"get_interviewer_list": {"skill": ["Python"], "experience_level": ["", "Mid-Level"], "availability": ["", true]}}]}
{"id": "live_multiple_39-14-1", "ground_truth": [{"review_of_interviewer": {"interviewer_name": ["Charlie Brown"], "include_comments": [true]}}]}
{"id": "live_multiple_40-15-0", "ground_truth": [{"oven_preheat": {"duration": [36], "temperature": [201]}}]}
{"id": "live_multiple_41-16-0", "ground_truth": [{"get_services": {"namespace": ["default"]}}]}
{"id": "live_multiple_42-16-1", "ground_truth": [{"get_pods": {"namespace": ["cascade-sandbox"], "label_selector": [""], "field_selector": [""], "include_uninitialized": ["", false]}}]}
{"id": "live_multiple_43-16-2", "ground_truth": [{"get_services": {"namespace": ["cascade-sandbox"]}}]}
{"id": "live_multiple_44-17-0", "ground_truth": [{"BankStatementOverView": {"bank_name": ["U.S. Bank"], "account_number": ["1-523-1713-5704"], "account_type": ["checking"], "full_name": ["SILVER BUSINESS CHECKING"], "transaction_overview": [{"total_deposits": [7132.76], "total_withdrawals": [5927.4], "start_date": ["2019-10-01"], "end_date": ["2019-10-31"]}]}}]}
{"id": "live_multiple_45-18-0", "ground_truth": [{"product_search": {"category": ["clothing"], "color": ["red"], "size": ["medium"]}}]}
{"id": "live_multiple_46-18-1", "ground_truth": [{"inventory_management": {"product_id": ["12345-67890"], "sizes": [["40"]], "color": ["", "any"], "quantity": ["", 1]}}]}
{"id": "live_multiple_47-18-2", "ground_truth": [{"order_status_check": {"order_id": ["*********"], "product": ["Smart LED TV"]}}]}
{"id": "live_multiple_48-18-3", "ground_truth": [{"inventory_management": {"product_id": ["123123"], "sizes": ["", []], "color": ["", "any"], "quantity": [30]}}]}
{"id": "live_multiple_49-19-0", "ground_truth": [{"interviewer_review.get": {"name": ["Charlie Ji"]}}]}
{"id": "live_multiple_50-20-0", "ground_truth": [{"get_movies": {"city": ["Noida"], "cinema_hall": ["", null], "movie_date": ["04-11-2024"], "movie_language": ["", "Hindi"], "movie_format": ["", "2D"]}}]}
{"id": "live_multiple_51-21-0", "ground_truth": [{"order_status_check": {"order_id": ["#123"], "product": ["a pair of sneakers", "pair of sneakers", "sneakers", "sneaker"]}}]}
{"id": "live_multiple_52-21-1", "ground_truth": [{"inventory_management": {"product_id": ["SK123"], "sizes": ["", []], "color": ["Red"], "quantity": ["", 1]}}]}
{"id": "live_multiple_53-22-0", "ground_truth": [{"inventory_management": {"product_id": ["V-neck sweate"], "sizes": [["M", "L"]], "color": ["maroon"]}}]}
{"id": "live_multiple_54-22-1", "ground_truth": [{"inventory_management": {"product_id": ["12345XYZ"], "sizes": [["M"]], "color": ["merah"]}}]}
{"id": "live_multiple_55-22-2", "ground_truth": [{"inventory_management": {"product_id": ["iPhone 12"], "sizes": ["", []], "color": ["white"]}}]}
{"id": "live_multiple_56-22-3", "ground_truth": [{"inventory_management": {"product_id": ["111"], "sizes": [["L"]], "color": ["", "Any"]}}]}
{"id": "live_multiple_57-22-4", "ground_truth": [{"get_product_details": {"product_id": ["282828"], "color": ["", "all colors"], "size": ["", "all sizes"]}}]}
{"id": "live_multiple_58-22-5", "ground_truth": [{"inventory_management": {"product_id": ["XY123"], "sizes": [["M"]], "color": ["", "Any"]}}]}
{"id": "live_multiple_59-22-6", "ground_truth": [{"inventory_management": {"product_id": ["Wonderland maxi dress"], "sizes": [["S"]]}}]}
{"id": "live_multiple_60-22-7", "ground_truth": [{"inventory_management": {"product_id": ["ABC123"], "sizes": [["L"]], "color": ["", "Any"]}}]}
{"id": "live_multiple_61-23-0", "ground_truth": [{"get_movies": {"city": ["Noida"], "cinema_hall": ["", null], "movie_date": ["", null], "movie_language": ["", null], "movie_format": ["", "2D"]}}]}
{"id": "live_multiple_62-24-0", "ground_truth": [{"todoIdx": {"type": ["delete"], "index": [1]}}]}
{"id": "live_multiple_63-25-0", "ground_truth": [{"uber.eat.order": {"restaurant_id": ["McDonald's"], "items": [[{"item": ["burgers"], "quantity": [5]}, {"item": ["chicken wings"], "quantity": [6]}]]}}]}
{"id": "live_multiple_64-26-0", "ground_truth": [{"uber.eat.order": {"restaurant_id": ["McDonald's"], "items": [[{"item_id": ["burger123"], "quantity": [5]}, {"item_id": ["wing456"], "quantity": [6]}]], "delivery_instructions": [""]}}]}
{"id": "live_multiple_65-26-1", "ground_truth": [{"get_current_weather": {"location": ["Boston, MA"], "unit": ["", "fahrenheit"]}}]}
{"id": "live_multiple_66-27-0", "ground_truth": [{"add_postgres_server": {"host": ["http://plgah.ca"], "port": ["", 5432], "database": ["finance_db"], "username": ["pascal"], "password": ["123123"]}}]}
{"id": "live_multiple_67-28-0", "ground_truth": [{"add_postgres_server": {"host": ["http://plgah.ca"], "port": ["", 5432], "database": ["maindb"], "username": ["pascal"], "password": ["securepassword123"]}}]}
{"id": "live_multiple_68-29-0", "ground_truth": [{"add_postgres_server": {"nickname": ["ProjectDB"], "host": ["http://foo.com"], "port": [5432, ""], "database": ["project_data"], "username": ["user1"], "password": ["12345"]}}]}
{"id": "live_multiple_69-30-0", "ground_truth": [{"add_postgres_server": {"nickname": ["PrimaryDB"], "host": ["db.example.com"], "port": [5432, ""], "database": ["inventory"], "username": ["admin"], "password": ["*********0"]}}]}
{"id": "live_multiple_70-30-1", "ground_truth": [{"dartfx_help": {"topic": ["troubleshooting"], "output_format": ["", "text"]}}]}
{"id": "live_multiple_71-31-0", "ground_truth": [{"add_postgres_server": {"nickname": ["EnterpriseDB"], "host": ["db.company.com"], "port": ["", 5432], "database": ["sales_data"], "username": ["dbadmin"], "password": ["secure*pwd123"]}}]}
{"id": "live_multiple_72-32-0", "ground_truth": [{"add_postgres_server": {"nickname": ["PrimaryDB"], "host": ["************"], "port": [5432], "database": ["main"], "username": ["admin_user"], "password": ["secure*pass123"]}}]}
{"id": "live_multiple_73-33-0", "ground_truth": [{"add_postgres_server": {"nickname": ["PrimaryDB"], "host": ["************"], "port": [5432], "database": ["main_db"], "username": ["admin"], "password": ["securePass123"]}}]}
{"id": "live_multiple_74-34-0", "ground_truth": [{"dartfx_help": {"topic": ["trading"], "subtopic": [""], "include_examples": [true]}}]}
{"id": "live_multiple_75-34-1", "ground_truth": [{"add_postgres_server": {"nickname": ["PrimaryDB"], "host": ["plgah.ca"], "port": ["", 5432], "database": ["maindb"], "username": ["admin"], "password": ["12341234"]}}]}
{"id": "live_multiple_76-35-0", "ground_truth": [{"dartfx_help": {"topic": ["charts"], "language": ["Spanish"]}}]}
{"id": "live_multiple_77-35-1", "ground_truth": [{"add_postgres_server": {"nickname": ["X"], "host": ["http://plga.ca"], "port": [5432, ""], "database": ["postgres"], "username": ["admin"], "password": ["secure_password123"]}}]}
{"id": "live_multiple_78-35-2", "ground_truth": [{"dartfx_help": {"topic": ["charts"], "language": ["Spanish"]}}]}
{"id": "live_multiple_79-36-0", "ground_truth": [{"dartfx_help": {"topic": ["installation"], "section": ["", "general"]}}]}
{"id": "live_multiple_80-36-1", "ground_truth": [{"add_mtnards_server": {"name": ["", "localhost"], "host": ["http://plga.ca"], "api_key": ["12344"]}}]}
{"id": "live_multiple_81-36-2", "ground_truth": [{"add_mtnards_server": {"name": ["", "localhost"], "host": ["http://plga.ca"], "api_key": ["1234324"]}}]}
{"id": "live_multiple_82-37-0", "ground_truth": [{"add_postgres_server": {"nickname": ["", "pg"], "host": ["***********"], "port": ["", 5432], "database": ["", "postgres"], "username": ["", "postgres"], "password": ["", "postgres"]}}]}
{"id": "live_multiple_83-38-0", "ground_truth": [{"list_servers": {"type": ["postgres"]}}]}
{"id": "live_multiple_84-38-1", "ground_truth": [{"add_postgres_server": {"nickname": ["PG1"], "host": ["pg.example.org"], "port": [5432], "database": ["main_db"], "username": ["admin"], "password": ["123412"]}}]}
{"id": "live_multiple_85-38-2", "ground_truth": [{"add_mtnards_server": {"nickname": ["primary_db_server"], "host": ["***********"], "api_key": ["1234123"]}}]}
{"id": "live_multiple_86-38-3", "ground_truth": [{"add_mtnards_server": {"nickname": ["RSD1"], "host": ["http://rds.com"], "api_key": ["1234123"]}}]}
{"id": "live_multiple_87-38-4", "ground_truth": [{"list_servers": {"type": ["openai"]}}]}
{"id": "live_multiple_88-38-5", "ground_truth": [{"list_servers": {"type": ["all", ""]}}]}
{"id": "live_multiple_89-39-0", "ground_truth": [{"render_ui_date_picker_widget": {"default_date": ["20-09-2023"]}}]}
{"id": "live_multiple_90-40-0", "ground_truth": [{"add_mtnards_server": {"nickname": ["FastDataServer"], "host": ["data.example.com"], "api_key": ["12345-ABCDE"]}}]}
{"id": "live_multiple_91-40-1", "ground_truth": [{"add_postgres_server": {"nickname": ["primary_db"], "host": ["example.org"], "port": [5432], "database": ["main"], "username": ["admin"], "password": ["12345"]}}]}
{"id": "live_multiple_92-40-2", "ground_truth": [{"list_servers": {"server_type": ["postgres"]}}]}
{"id": "live_multiple_93-41-0", "ground_truth": [{"add_postgres_server": {"nickname": ["", "postgres1"], "host": ["example.org"], "port": [5432], "database": ["inventory"], "username": ["admin"], "password": ["secret"]}}]}
{"id": "live_multiple_94-41-1", "ground_truth": [{"list_servers": {"type": ["all", ""]}}]}
{"id": "live_multiple_95-41-2", "ground_truth": [{"close_project": {"project_id": ["DA-12345"], "archive": [true, ""], "notify_users": [false, ""]}}]}
{"id": "live_multiple_96-42-0", "ground_truth": [{"list_files": {"file_type": ["data"], "include_hidden": ["", false], "recursive": [true]}}]}
{"id": "live_multiple_97-42-1", "ground_truth": [{"connect_to_server": {"nickname": ["xyz"], "timeout": ["", 30], "retry_attempts": ["", 3], "use_ssl": ["", true]}}]}
{"id": "live_multiple_98-42-2", "ground_truth": [{"open_project": {"path": ["/Foo/Bar"], "create_if_missing": [true, ""], "access_mode": ["", "readwrite"]}}]}
{"id": "live_multiple_99-42-3", "ground_truth": [{"connect_to_server": {"nickname": ["pg1"], "timeout": ["", 30], "retry_attempts": ["", 3], "use_ssl": ["", true]}}]}
{"id": "live_multiple_100-42-4", "ground_truth": [{"connect_to_server": {"nickname": ["text01"], "timeout": ["", 30], "retry_attempts": ["", 3], "use_ssl": ["", true]}}]}
{"id": "live_multiple_101-42-5", "ground_truth": [{"connect_to_server": {"nickname": ["SQL01"], "timeout": [60], "retry_attempts": ["", 3], "use_ssl": ["", true]}}]}
{"id": "live_multiple_102-43-0", "ground_truth": [{"list_files": {"type": ["json"], "include_hidden": ["", false]}}]}
{"id": "live_multiple_103-43-1", "ground_truth": [{"add_postgres_server": {"nickname": ["PG1"], "host": ["localhost"], "port": [5432], "database": ["main_db"], "username": ["admin"], "password": ["secure_pass123"]}}]}
{"id": "live_multiple_104-43-2", "ground_truth": [{"open_project": {"path": ["/foo/bar"], "create_new": [false, ""], "access_mode": ["", "edit"]}}]}
{"id": "live_multiple_105-43-3", "ground_truth": [{"dartfx_help": {"topic": ["list_files"], "search_deep": ["", false], "language": ["", "English"], "format": ["", "text"]}}]}
{"id": "live_multiple_106-43-4", "ground_truth": [{"add_mtnards_server": {"nickname": ["FastQuery"], "host": ["rds.example.org"], "api_key": ["1231231"]}}]}
{"id": "live_multiple_107-43-5", "ground_truth": [{"list_files": {"type": ["sav"], "include_hidden": [true]}}]}
{"id": "live_multiple_108-43-6", "ground_truth": [{"list_servers": {"type": ["postgres"]}}]}
{"id": "live_multiple_109-43-7", "ground_truth": [{"list_servers": {"type": ["mtna"]}}]}
{"id": "live_multiple_110-43-8", "ground_truth": [{"list_files": {"type": ["txt"], "include_hidden": [true]}}]}
{"id": "live_multiple_111-43-9", "ground_truth": [{"dartfx_help": {"topic": ["database optimization"], "search_deep": ["", false], "language": ["Spanish"], "format": ["video"]}}]}
{"id": "live_multiple_112-43-10", "ground_truth": [{"list_servers": {"type": ["postgres"]}}]}
{"id": "live_multiple_113-44-0", "ground_truth": [{"list_servers": {"server_type": ["mtna"]}}]}
{"id": "live_multiple_114-44-1", "ground_truth": [{"add_postgres_server": {"nickname": ["MainDB"], "host": ["pg.example.org"], "port": [5432, ""], "database": ["sales_db"], "username": ["admin"], "password": ["password"]}}]}
{"id": "live_multiple_115-45-0", "ground_truth": [{"analysis_api.AnalysisApi.retrieve_analysis": {"project": ["SUPERFANCY"], "component": ["AB1010CD"], "vulnerability": ["ef903ac-893-f00"]}}]}
{"id": "live_multiple_116-45-1", "ground_truth": [{"acl_api.add_mapping": {"principal_id": ["user-123"], "resource_id": ["resource-456"], "permissions": ["read"]}}]}
{"id": "live_multiple_117-45-2", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_118-45-3", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_119-45-4", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_120-45-5", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_121-46-0", "ground_truth": [{"get_headway": {"ego_info": [{"position": [{"lateral": 10.5, "longitudinal": 50}], "orientation": [30]}], "lane_info": [{"lane_id": ["L123"], "lane_type": ["regular"]}], "bounding_boxes": [[{"x": [60.2], "y": [12.3]}]]}}]}
{"id": "live_multiple_122-46-1", "ground_truth": [{"get_time_to_collision": {"ego_velocity": [50.0], "ego_acceleration": [-2.0], "leading_object_velocity": [30.0], "leading_object_acceleration": [-1.0], "initial_distance": [100.0]}}]}
{"id": "live_multiple_123-46-2", "ground_truth": [{"get_time_to_collision": {"ego_velocity": [20.0], "ego_acceleration": [2.0], "leading_object_velocity": [15.0], "leading_object_acceleration": [1.0], "initial_distance": [50.0]}}]}
{"id": "live_multiple_124-47-0", "ground_truth": [{"duck_duck_go": {"query": ["What is LangFuse and how can I use it in my genAI project?"], "format": ["", "json"], "no_redirect": ["", false], "no_html": ["", false], "skip_disambiguation": ["", false]}}]}
{"id": "live_multiple_125-47-1", "ground_truth": [{"multiply": {"a": [3], "b": [10]}}]}
{"id": "live_multiple_126-48-0", "ground_truth": [{"analysis_api.AnalysisApi.retrieve_analysis": {"project": ["SUPERFANCY"], "component": ["AB1010CD"], "vulnerability": ["ef903ac-893-f00"]}}]}
{"id": "live_multiple_127-49-0", "ground_truth": [{"search_engine.query": {"prompt": ["history of kites and how they're made", "history of kites and how they are made"], "since_year": ["", 2023], "source": ["", "all"], "include_facts": ["", true]}}]}
{"id": "live_multiple_128-50-0", "ground_truth": [{"generate_human_image": {"prompt": ["a man wearing a red dress"], "image_quality": ["high", ""], "image_format": ["PNG", ""], "include_metadata": ["", false]}}]}
{"id": "live_multiple_129-50-1", "ground_truth": [{"search_engine.query": {"prompt": ["current prime minister of India"], "include_after_year": [true], "source": ["", "Google"]}}]}
{"id": "live_multiple_130-50-2", "ground_truth": [{"english_llm": {"q": ["I'd like to read a poem about kites. Could you generate one with a creative touch?"], "max_tokens": ["", 50], "temperature": [0.8], "return_probabilities": ["", false]}}]}
{"id": "live_multiple_131-50-3", "ground_truth": [{"multilingual_llm": {"q": ["\u091a\u093e\u092f \u0915\u0948\u0938\u0947 \u092c\u0928\u093e\u090f\u0902?"], "language": ["Hindi"], "max_length": ["", 150], "temperature": ["", 0.5]}}]}
{"id": "live_multiple_132-50-4", "ground_truth": [{"search_engine.query": {"prompt": ["who is the current pm of india"], "include_after_year": [true], "source": ["", "Google"]}}]}
{"id": "live_multiple_133-50-5", "ground_truth": [{"multilingual_llm": {"q": ["chai kaise bnaye"], "language": ["", "Hindi"], "max_length": ["", 150], "temperature": ["", 0.5]}}]}
{"id": "live_multiple_134-51-0", "ground_truth": [{"stock_price.get": {"ticker": ["AAPL"], "exchange": ["NYSE"]}}]}
{"id": "live_multiple_135-51-1", "ground_truth": [{"weather.get": {"city": ["London"], "country": ["UK", "GB"], "units": ["", "metric"], "include_forecast": ["", false]}}]}
{"id": "live_multiple_136-52-0", "ground_truth": [{"weather.get_weather": {"city": ["London"], "country": ["United Kingdom"]}}]}
{"id": "live_multiple_137-52-1", "ground_truth": [{"stock_price.get": {"ticker": ["AAPL"], "exchange": ["NASDAQ"]}}]}
{"id": "live_multiple_138-53-0", "ground_truth": [{"weather.get": {"city": ["London"], "country": ["United Kingdom"], "units": ["", "metric"]}}]}
{"id": "live_multiple_139-53-1", "ground_truth": [{"stock_price.get": {"ticker": ["AAPL"], "exchange": ["NASDAQ"], "date": ["", null]}}]}
{"id": "live_multiple_140-54-0", "ground_truth": [{"weather.get": {"city": ["London"], "country": ["GB"], "units": ["", "metric"], "include_forecast": ["", false]}}]}
{"id": "live_multiple_141-54-1", "ground_truth": [{"stock_price.get": {"ticker": ["AAPL"], "exchange": ["NASDAQ"]}}]}
{"id": "live_multiple_142-55-0", "ground_truth": [{"weather.get_weather_data": {"city": ["London"], "country": ["GB"], "units": ["", "metric"], "language": ["", "en"]}}]}
{"id": "live_multiple_143-55-1", "ground_truth": [{"stock_price.get": {"ticker": ["AAPL"], "exchange": ["", "NASDAQ"], "date": ["", null]}}]}
{"id": "live_multiple_144-56-0", "ground_truth": [{"get_sensor_readings_latest": {"perPage": ["", 100], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": ["", []], "serials": ["", []], "metrics": [["temperature"]]}}]}
{"id": "live_multiple_145-57-0", "ground_truth": [{"get_shopping": {"item_name": ["winter jacket"], "location": ["Berkeley, CA"], "currency": ["", "USD"]}}]}
{"id": "live_multiple_146-58-0", "ground_truth": [{"get_sensor_readings_latest": {"perPage": [100], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": [["n1", "n2"]], "serials": ["", []], "metrics": ["", []]}}]}
{"id": "live_multiple_147-58-1", "ground_truth": [{"get_sensor_readings_history": {"perPage": [10], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": [["L_579838452023959405"]], "serials": ["", []], "metrics": [["temperature"]], "timespan": [10800], "t0": ["2024-03-05T12:00:00Z"], "t1": ["2024-03-05T15:00:00Z"]}}]}
{"id": "live_multiple_148-58-2", "ground_truth": [{"get_sensor_readings_latest": {"perPage": [10], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": ["", []], "serials": ["", []], "metrics": ["", []]}}]}
{"id": "live_multiple_149-58-3", "ground_truth": [{"get_sensor_alerts": {"perPage": [10], "startingAfter": ["", null], "endingBefore": ["", null], "t0": ["", null], "t1": ["", null], "networkId": [["L_579838452023959405"]], "timespan": ["", 31536000], "sensorSerial": ["", null], "triggerMetric": ["", null]}}]}
{"id": "live_multiple_150-58-4", "ground_truth": [{"get_sensor_readings_latest": {"perPage": [100], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": ["", []], "serials": ["", []], "metrics": ["", []]}}]}
{"id": "live_multiple_151-58-5", "ground_truth": [{"get_sensor_readings_latest": {"perPage": [10], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": ["", []], "serials": ["", []], "metrics": ["", []]}}]}
{"id": "live_multiple_152-58-6", "ground_truth": [{"get_sensor_readings_latest": {"perPage": [100], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": [["office-network-001"]], "serials": ["", []], "metrics": [["temperature", "humidity"]]}}]}
{"id": "live_multiple_153-58-7", "ground_truth": [{"get_sensor_readings_history": {"perPage": [10], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": [["12312"]], "serials": ["", []], "metrics": [["temperature"]], "timespan": [3600], "t0": ["", null], "t1": ["", null]}}]}
{"id": "live_multiple_154-58-8", "ground_truth": [{"get_sensor_alerts": {"perPage": [50], "startingAfter": ["", null], "endingBefore": ["", null], "t0": ["", null], "t1": ["", null], "networkId": [["L_579838452023959405"]], "timespan": ["", 31536000], "sensorSerial": ["", null], "triggerMetric": ["", null]}}]}
{"id": "live_multiple_155-58-9", "ground_truth": [{"get_sensor_readings_latest": {"perPage": [50], "startingAfter": ["", null], "endingBefore": ["", null], "networkId": ["", []], "serials": ["", []], "metrics": ["", []]}}]}
{"id": "live_multiple_156-59-0", "ground_truth": [{"analysis_api.AnalysisApi.retrieve_analysis": {"project": ["SUPERFANCY"], "component": ["AB1010CD"], "vulnerability": ["ef903ac-893-f00"]}}]}
{"id": "live_multiple_157-60-0", "ground_truth": [{"get_adriel_projects": {"start_date": ["2021-01-01"], "end_date": ["2021-12-31"], "include_archived": ["", false]}}]}
{"id": "live_multiple_158-61-0", "ground_truth": [{"get_adriel_projects": {"user_id": [7623], "include_completed": [true], "date_range": ["", null]}}]}
{"id": "live_multiple_159-62-0", "ground_truth": [{"get_detail_adriel_projects": {"project_name": ["e-commerce platform overhaul"], "include_archived": ["", false]}}]}
{"id": "live_multiple_160-62-1", "ground_truth": [{"get_adriel_projects": {"user_id": ["Adriel"], "include_completed": ["", false], "status_filter": ["", "active"]}}]}
{"id": "live_multiple_161-63-0", "ground_truth": [{"get_detail_adriel_project": {"project_name": ["portfolio-web"], "include_financials": ["", false], "date_format": ["", "YYYY-MM-DD"]}}]}
{"id": "live_multiple_162-63-1", "ground_truth": [{"get_detail_adriel_project": {"project_name": ["e-commerce-web"], "include_financials": ["", false], "date_format": ["", "YYYY-MM-DD"]}}]}
{"id": "live_multiple_163-64-0", "ground_truth": [{"get_detail_adriel_project": {"project_name": ["portfolio-web"], "include_tasks": [true], "include_financials": [true]}}]}
{"id": "live_multiple_164-65-0", "ground_truth": [{"get_detail_adriel_project": {"project_name": ["portfolio-web"], "include_financials": ["", false], "status_filter": ["", "active"]}}]}
{"id": "live_multiple_165-65-1", "ground_truth": [{"get_adriel_list_projects": {"user_id": [123], "include_inactive": [true], "sort_order": ["", "name"]}}]}
{"id": "live_multiple_166-66-0", "ground_truth": [{"get_adriel_profile": {"user_id": ["12445"], "include_contacts": [true], "format": ["", "json"]}}]}
{"id": "live_multiple_167-67-0", "ground_truth": [{"get_adriel_profile": {"user_id": ["12345"], "include_preferences": [true], "format": ["", "json"]}}]}
{"id": "live_multiple_168-68-0", "ground_truth": [{"get_adriel_profile": {"user_id": [12345], "include_private": [true]}}]}
{"id": "live_multiple_169-69-0", "ground_truth": [{"get_adriel_detail_experience_and_education": {"experience_or_education_type": ["", "experience"], "experience_or_education_name": ["Internship at Sebelas Maret University", "Sebelas Maret University", "Internship"]}}]}
{"id": "live_multiple_170-70-0", "ground_truth": [{"get_adriel_detail_experience_and_education": {"experience_or_education_type": ["", "work_experience"], "experience_or_education_name": ["Internship at Sebelas Maret University", "Sebelas Maret University", "Internship"]}}]}
{"id": "live_multiple_171-71-0", "ground_truth": [{"get_adriel_detail_experience_and_education": {"experience_or_education_type": ["Internship"], "experience_or_education_name": ["", "Not specified"], "details": [""], "start_date": ["", null], "end_date": ["", null]}}]}
{"id": "live_multiple_172-71-1", "ground_truth": [{"get_adriel_list_projects": {"user_id": [123], "include_completed": ["", false], "project_status": ["", "active"], "date_filter": ["", null]}}]}
{"id": "live_multiple_173-71-2", "ground_truth": [{"get_adriel_tech_stack": {"employee_id": ["123"], "include_past_technologies": [true], "category": ["", "programming_languages"]}}]}
{"id": "live_multiple_174-72-0", "ground_truth": [{"get_adriel_detail_experience_and_education": {"experience_or_education_type": ["Internship at Sebelas Maret University (UNS)"]}}]}
{"id": "live_multiple_175-72-1", "ground_truth": [{"get_adriel_list_projects": {"user_id": ["12345"], "status": ["", "active"], "include_dates": ["", false]}}]}
{"id": "live_multiple_176-73-0", "ground_truth": [{"recall_memory_search": {"query": ["Shishir's birthday"], "page": ["", 0], "request_heartbeat": ["", false]}}]}
{"id": "live_multiple_177-74-0", "ground_truth": [{"get_trip": {"location": ["New York, NY"]}}]}
{"id": "live_multiple_178-75-0", "ground_truth": [{"HNA_NEWS.search": {"keyword": ["Ji Sung Park"], "category": ["", "World"], "language": ["", "en"], "limit": [5], "sort_by": ["", "Date"]}}]}
{"id": "live_multiple_179-75-1", "ground_truth": [{"HNA_NEWS.search": {"keyword": ["artificial intelligence"], "category": ["Technology"], "language": ["", "en"], "limit": ["", 10], "sort_by": ["", "Date"]}}]}
{"id": "live_multiple_180-76-0", "ground_truth": [{"order_processing.handle_order": {"order_id": ["ORD123"], "items": [["ITM456", "ITM789"]], "customer_info": [{"customer_id": ["CUST001"], "shipping_address": ["123 Maple Street, Springfield, IL, 62704"], "billing_address": ["456 Oak Avenue, Springfield, IL, 62705"]}], "apply_discount": [true], "discount_code": ["DISC20"], "notify_customer": ["", true], "item_quantities": [[2, 1]], "item_prices": [[15.99, 42.5]]}}]}
{"id": "live_multiple_181-76-1", "ground_truth": [{"api_name.get_weather_forecast": {"location": ["New York City, NY"], "date": ["2023-07-04"], "temperature_unit": ["", "Fahrenheit"], "include_humidity": ["", true], "include_condition": ["", true]}}]}
{"id": "live_multiple_182-77-0", "ground_truth": [{"math_gcd": {"a": [100], "b": [120]}}]}
{"id": "live_multiple_183-78-0", "ground_truth": [{"finish": {"input_text": ["The quick brown fox jumps over the lazy dog."], "tgt_lang": ["da"], "src_lang": ["en"], "max_length": ["", 500], "access_token": ["", "example_token"]}}]}
{"id": "live_multiple_184-79-0", "ground_truth": [{"search_advice": {"query": ["career growth"], "language": ["", "EN"]}}]}
{"id": "live_multiple_185-80-0", "ground_truth": [{"get_activity_by_participants": {"participant_count": [5], "activity_type": ["education"], "price": ["", 0.0], "accessibility": ["", 0.0]}}]}
{"id": "live_multiple_186-81-0", "ground_truth": [{"weather_forecast.get": {"location": ["\u5317\u4eac", "Beijing"], "date": ["the day after tomorrow"], "unit": ["celsius"]}}]}
{"id": "live_multiple_187-82-0", "ground_truth": [{"quarterly_earnings": {"company_name": ["Berkshire Hathaway"], "cik": ["0001067983"]}}]}
{"id": "live_multiple_188-82-1", "ground_truth": [{"holdings.get_13F_HR": {"company_name": ["Berkshire Hathaway"], "cik": ["0001067983"]}}]}
{"id": "live_multiple_189-83-0", "ground_truth": [{"sendHttpRequest": {"method": ["POST"], "url": ["https://httpbin.org/post"], "headers": [{"Content-Type": ["application/json"]}], "data": [{"name": ["John Doe"], "email": ["<EMAIL>"]}]}}]}
{"id": "live_multiple_190-84-0", "ground_truth": [{"game_rewards.get": {"game": ["Fortnite"], "platform": ["PlayStation"], "mission": ["", "All Missions"], "trophy": ["", "all levels"]}}]}
{"id": "live_multiple_191-85-0", "ground_truth": [{"sort_list": {"elements": [["Sam", "Alice", "Jack"]], "order": ["", "asc"]}}]}
{"id": "live_multiple_192-86-0", "ground_truth": [{"analyze_image_with_question.pipeline": {"image_path": ["image.png"], "question": ["generate with technically complex attention to detail a description of what you see"]}}]}
{"id": "live_multiple_193-87-0", "ground_truth": [{"CalcProduct": {"a": [394], "b": [213]}}]}
{"id": "live_multiple_194-87-1", "ground_truth": [{"CalcProduct": {"a": [443], "b": [349]}}]}
{"id": "live_multiple_195-87-2", "ground_truth": [{"getCurrentTime": {"timezone": ["America/Los_Angeles"], "include_date": [true]}}]}
{"id": "live_multiple_196-88-0", "ground_truth": [{"get_tickets": {"customer": ["\u963f\u8fea\u8fbe\u65af"]}}]}
{"id": "live_multiple_197-89-0", "ground_truth": [{"get_tickets": {"customer": ["\u963f\u8fea\u8fbe\u65af"], "priority": [4]}}]}
{"id": "live_multiple_198-90-0", "ground_truth": [{"adriel_contact": {}}]}
{"id": "live_multiple_199-90-1", "ground_truth": [{"adriel_tech_stack": {}}]}
{"id": "live_multiple_200-90-2", "ground_truth": [{"adriel_list_projects": {"user_id": ["3"], "include_completed": ["", false], "sort_order": ["", "asc"]}}]}
{"id": "live_multiple_201-90-3", "ground_truth": [{"adriel_contact": {"contact_id": ["", 1], "format": ["xml"]}}]}
{"id": "live_multiple_202-90-4", "ground_truth": [{"adriel_list_projects": {"user_id": ["1234"], "include_completed": ["", false], "sort_order": ["", "asc"]}}]}
{"id": "live_multiple_203-90-5", "ground_truth": [{"detail_adriel_project": {"project_name": ["e-commerce-website"], "include_financials": ["", false], "completion_date": ["", null]}}]}
{"id": "live_multiple_204-90-6", "ground_truth": [{"adriel_experiences_and_education": {}}]}
{"id": "live_multiple_205-90-7", "ground_truth": [{"adriel_experiences_and_education": {}}]}
{"id": "live_multiple_206-91-0", "ground_truth": [{"contact": {"person_name": ["Adriel"], "phone_number": [""], "email_address": [""]}}]}
{"id": "live_multiple_207-91-1", "ground_truth": [{"get_tech_stack": {"employee_id": ["Adriel"], "include_tools": ["", false], "as_of_date": ["", null]}}]}
{"id": "live_multiple_208-91-2", "ground_truth": [{"list_projects": {"user_id": ["1234"], "include_completed": [true], "sort_order": ["", "asc"]}}]}
{"id": "live_multiple_209-91-3", "ground_truth": [{"detail_project": {"project_name": ["turing-machine"], "include_status": ["", false], "start_date": ["", null]}}]}
{"id": "live_multiple_210-91-4", "ground_truth": [{"detail_experience_and_education": {"experience_or_education_type": ["Freelance at Pingfest"], "experience_or_education_name": ["", "Not specified"]}}]}
{"id": "live_multiple_211-91-5", "ground_truth": [{"get_tech_stack": {"employee_id": ["1234"], "include_tools": ["", false], "as_of_date": ["", null]}}]}
{"id": "live_multiple_212-91-6", "ground_truth": [{"detail_project": {"project_name": ["car-rental"], "include_status": [true], "start_date": ["", null]}}]}
{"id": "live_multiple_213-91-7", "ground_truth": [{"detail_experience_and_education": {"experience_or_education_type": ["Internship at Universitas Sebelas Maret (UNS)"], "experience_or_education_name": ["", "Not specified"]}}]}
{"id": "live_multiple_214-91-8", "ground_truth": [{"experiences_and_education": {"person_id": ["1234"], "include_experiences": ["", true], "include_education": ["", true], "years_experience": ["", 0]}}]}
{"id": "live_multiple_215-91-9", "ground_truth": [{"experiences_and_education": {"person_id": ["1234"], "include_experiences": ["", true], "include_education": [false], "years_experience": ["", 0]}}]}
{"id": "live_multiple_216-92-0", "ground_truth": [{"find_flights": {"origin": ["BER"], "destination": ["JFK"], "date": ["2021-10-10"], "passengers": [2]}}]}
{"id": "live_multiple_217-93-0", "ground_truth": [{"Subdomain": {"domain": ["google.com"]}}]}
{"id": "live_multiple_218-94-0", "ground_truth": [{"get_sensor_readings_history_by_interval": {"perPage": [100], "startingAfter": ["", null], "endingBefore": ["", null], "networkIds": [["L_579838452023959405"]], "serials": ["", []], "metrics": [["temperature"]], "timespan": [3600], "t0": ["", null], "t1": ["", null], "interval": ["", 86400], "models": ["", []]}}]}
{"id": "live_multiple_219-94-1", "ground_truth": [{"get_sensor_readings_history": {"perPage": [100], "startingAfter": ["", null], "endingBefore": ["", null], "networkIds": ["", []], "serials": ["", []], "metrics": [["current"]], "timespan": [120], "t0": ["2024-03-10T15:30:00Z"], "t1": ["2024-03-10T15:32:00Z"]}}]}
{"id": "live_multiple_220-94-2", "ground_truth": [{"get_sensor_readings_latest": {"perPage": [100], "startingAfter": ["", null], "endingBefore": ["", null], "networkIds": ["", []], "serials": ["", []], "metrics": [["temperature"]]}}]}
{"id": "live_multiple_221-95-0", "ground_truth": [{"segment": {"img_path": ["./yoha.jpg"]}}]}
{"id": "live_multiple_222-96-0", "ground_truth": [{"uber.ride": {"loc": ["456 Elm St, Springville, IL"], "type": ["comfort"], "time": [10]}}]}
{"id": "live_multiple_223-97-0", "ground_truth": [{"list_directory_contents": {"dir_path": ["/out/programs"], "include_hidden": [true], "sort_order": ["desc"]}}]}
{"id": "live_multiple_224-98-0", "ground_truth": [{"reschedule": {"identifier": ["dentist"], "dateortime": ["2024-11-01T16:00:00"], "timezone": ["America/New_York"]}}]}
{"id": "live_multiple_225-99-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_226-100-0", "ground_truth": [{"AclApi.add_mapping": {"resource_id": ["123-resource"], "user_id": ["user-456"], "permissions": [["read", "write"]], "expiry_date": ["", null]}}]}
{"id": "live_multiple_227-101-0", "ground_truth": [{"project_api.ProjectApi.get_children_projects_by_tag": {"tag": ["environment"], "uuid": ["123e4567-e89b-12d3-a456-************"], "excludeInactive": ["", false]}}]}
{"id": "live_multiple_228-102-0", "ground_truth": [{"analysis_api.AnalysisApi.retrieve_analysis": {"project": ["SUPERFANCY"], "component": ["AB1010CD"], "vulnerability": ["ef903ac-893-f00"]}}]}
{"id": "live_multiple_229-103-0", "ground_truth": [{"get_contact_information": {"name": ["", null], "id_no": ["A1234567B"]}}]}
{"id": "live_multiple_230-103-1", "ground_truth": [{"get_contact_information": {"name": ["Liam Neeson"], "id_no": ["", null]}}]}
{"id": "live_multiple_231-104-0", "ground_truth": [{"get_contact_information": {"name": ["", null], "id_no": ["123"]}}]}
{"id": "live_multiple_232-104-1", "ground_truth": [{"get_contact_information": {"name": ["Liam Neeson"], "id_no": ["", null]}}]}
{"id": "live_multiple_233-105-0", "ground_truth": [{"get_contact_information": {"name": ["Rayson Goh"], "identification_number": ["", null]}}]}
{"id": "live_multiple_234-106-0", "ground_truth": [{"set_integer": {"name": ["columnCount"], "value": [8]}}]}
{"id": "live_multiple_235-106-1", "ground_truth": [{"set_integer": {"name": ["columnCount"], "value": [12]}}]}
{"id": "live_multiple_236-106-2", "ground_truth": [{"set_point": {"x": [2.0], "y": [3.0], "z": [4.0], "point_name": ["pointA"]}}]}
{"id": "live_multiple_237-106-3", "ground_truth": [{"set_point": {"x": [2.0], "y": [3.0], "z": [4.0], "point_name": ["pointA"]}}]}
{"id": "live_multiple_238-106-4", "ground_truth": [{"set_float": {"number_name": ["roomHeight"], "number_value": [4.2]}}]}
{"id": "live_multiple_239-107-0", "ground_truth": [{"search_web_tool": {"query": ["apple"], "num_results": ["", 3], "source": ["", "text"]}}]}
{"id": "live_multiple_240-107-1", "ground_truth": [{"search_web_tool": {"query": ["weather in India", "current weather in India", "What's the current weather in India", "What's the current weather in India?"], "num_results": ["", 3], "source": ["", "text"]}}]}
{"id": "live_multiple_241-107-2", "ground_truth": [{"search_web_tool": {"query": ["flight prices from NYC to New Delhi"], "num_results": ["", 3], "source": ["", "text"]}}]}
{"id": "live_multiple_242-107-3", "ground_truth": [{"tts_tool": {"content": ["If you could have dinner with any historical figure, who would you choose and why?, also genart a photo of that figure"]}}]}
{"id": "live_multiple_243-107-4", "ground_truth": [{"search_web_tool": {"query": ["Find a recent news article about corona virus", "recent news article about corona virus", "recent news corona virus"], "num_results": ["", 3], "source": ["news"]}}]}
{"id": "live_multiple_244-108-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_245-109-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_246-110-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_247-111-0", "ground_truth": [{"analysis_api.AnalysisApi.retrieve_analysis": {"project": ["SUPERFANCY"], "component": ["AB1010CD"], "vulnerability": ["ef903ac-893-f00"]}}]}
{"id": "live_multiple_248-112-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_249-113-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_250-114-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_251-115-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_252-116-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_253-117-0", "ground_truth": [{"badge_api.BadgeApi.get_project_vulnerabilities_badge": {"name": ["bandana", "bandana project"], "version": ["5"]}}]}
{"id": "live_multiple_254-118-0", "ground_truth": [{"BadgeApi.get_project_policy_violations_badge1": {"project_name": ["bandana"], "project_version": ["5"]}}]}
{"id": "live_multiple_255-119-0", "ground_truth": [{"badge_api.BadgeApi.get_project_policy_violations_badge": {"uuid": ["badae"], "format": ["", "svg"], "style": ["", "flat"]}}]}
{"id": "live_multiple_256-120-0", "ground_truth": [{"vex_api.VexApi.export_project_as_cyclone_dx1": {"uuid": ["123e4567-e89b-12d3-a456-************"], "download": ["", false]}}]}
{"id": "live_multiple_257-121-0", "ground_truth": [{"vex_api.VexApi.export_project_as_cyclone_dx1": {"uuid": ["1a1a-c6"], "download": ["", false]}}]}
{"id": "live_multiple_258-122-0", "ground_truth": [{"project_api.ProjectApi.get_project_by_name_and_version": {"name": ["Orion"], "version": ["2.1.3"]}}]}
{"id": "live_multiple_259-123-0", "ground_truth": [{"project_api.ProjectApi.get_project_by_name_and_version": {"name": ["my_version_proj"], "version": ["1.2.3"]}}]}
{"id": "live_multiple_260-124-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_261-125-0", "ground_truth": [{"rotateImageAction": {"degrees": [30], "image_format": ["", "JPEG"], "keep_original": ["", true]}}]}
{"id": "live_multiple_262-125-1", "ground_truth": [{"rotateImageAction": {"degrees": [20], "image_format": ["", "JPEG"], "keep_original": ["", true]}}]}
{"id": "live_multiple_263-126-0", "ground_truth": [{"EventQuery": {"search_string": ["gym session"], "start_date": ["", "null"], "end_date": ["", "null"], "include_recurring": ["", false]}}]}
{"id": "live_multiple_264-126-1", "ground_truth": [{"EventQuery": {"search_string": ["trip"], "start_date": ["2023-04-01"], "end_date": ["2023-04-30"], "include_recurring": [true]}}]}
{"id": "live_multiple_265-127-0", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Hanoi"], "date": ["", null]}}]}
{"id": "live_multiple_266-127-1", "ground_truth": [{"Weather_1_GetWeather": {"city": ["San Diego"], "date": ["", null]}}]}
{"id": "live_multiple_267-127-2", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Vancouver"], "date": ["2023-04-04"]}}]}
{"id": "live_multiple_268-127-3", "ground_truth": [{"Weather_1_GetWeather": {"city": ["London, UK", "London"], "date": ["2023-03-12"]}}]}
{"id": "live_multiple_269-127-4", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Philadelphia"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_270-127-5", "ground_truth": [{"Weather_1_GetWeather": {"city": ["New York"], "date": ["2023-04-17"]}}]}
{"id": "live_multiple_271-127-6", "ground_truth": [{"Weather_1_GetWeather": {"city": ["San Diego"], "date": ["2023-03-01"]}}]}
{"id": "live_multiple_272-127-7", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Nairobi"], "date": ["", null]}}]}
{"id": "live_multiple_273-127-8", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Phoenix", "Phoenix, AZ"], "date": ["2023-04-14"]}}]}
{"id": "live_multiple_274-127-9", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Chicago", "Chi-town"], "date": ["2024-02-13"]}}]}
{"id": "live_multiple_275-127-10", "ground_truth": [{"Weather_1_GetWeather": {"city": ["American Canyon"], "date": ["2023-03-12"]}}]}
{"id": "live_multiple_276-127-11", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Los Angeles"], "date": ["2023-03-09"]}}]}
{"id": "live_multiple_277-128-0", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["American"], "location": ["New York, NY"], "price_range": ["", "moderate"], "has_vegetarian_options": ["", false], "has_seating_outdoors": [true]}}]}
{"id": "live_multiple_278-128-1", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Italian"], "location": ["Oakland, CA"], "price_range": ["", "moderate"], "has_vegetarian_options": ["", false], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_279-128-2", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Asian Fusion"], "location": ["Santa Clara, CA"], "price_range": ["", "moderate"], "has_vegetarian_options": ["", false], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_280-128-3", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Coffeehouse"], "location": ["New York, NY"], "price_range": ["", "moderate"], "has_vegetarian_options": ["", false], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_281-128-4", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Vegetarian"], "location": ["Berkeley, CA"], "price_range": ["cheap"], "has_vegetarian_options": [true], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_282-128-5", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["American"], "location": ["Mountain View, CA"], "price_range": ["", "moderate"], "has_vegetarian_options": ["", false], "has_seating_outdoors": [true]}}]}
{"id": "live_multiple_283-128-6", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Izakaya"], "location": ["San Francisco, CA"], "price_range": ["cheap"], "has_vegetarian_options": ["", false], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_284-128-7", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Italian"], "location": ["San Francisco, CA"], "price_range": ["", "moderate"], "has_vegetarian_options": ["", false], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_285-129-0", "ground_truth": [{"Services_4_FindProvider": {"city": ["Pittsburgh, PA"], "type": ["Psychiatrist"], "insurance_accepted": ["", true]}}]}
{"id": "live_multiple_286-129-1", "ground_truth": [{"Services_4_FindProvider": {"city": ["San Jose, CA"], "type": ["Psychologist"], "insurance_accepted": ["", true]}}]}
{"id": "live_multiple_287-129-2", "ground_truth": [{"Services_4_FindProvider": {"city": ["Los Gatos, CA"], "type": ["Family Counselor"], "insurance_accepted": ["", true]}}]}
{"id": "live_multiple_288-129-3", "ground_truth": [{"Services_4_FindProvider": {"city": ["Berkeley, CA"], "type": ["Family Counselor"], "insurance_accepted": ["", true]}}]}
{"id": "live_multiple_289-129-4", "ground_truth": [{"Services_4_FindProvider": {"city": ["Novato, CA"], "type": ["Psychologist"], "insurance_accepted": ["", true]}}]}
{"id": "live_multiple_290-129-5", "ground_truth": [{"Services_4_FindProvider": {"city": ["Walnut Creek, CA"], "type": ["Family Counselor"], "insurance_accepted": ["", true]}}]}
{"id": "live_multiple_291-130-0", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Austin, TX"], "has_laundry_service": ["", "dontcare"], "number_of_adults": ["", 0], "rating": ["", 0.0]}}]}
{"id": "live_multiple_292-130-1", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Long Beach, CA"], "has_laundry_service": ["", "dontcare"], "number_of_adults": [1], "rating": [4.2]}}]}
{"id": "live_multiple_293-130-2", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["New York, NY"], "has_laundry_service": ["True"], "number_of_adults": ["", 0], "rating": [3.7]}}]}
{"id": "live_multiple_294-130-3", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Kuala Lumpur, Malaysia"], "has_laundry_service": ["", "dontcare"], "number_of_adults": [1], "rating": [3.8]}}]}
{"id": "live_multiple_295-130-4", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Los Angeles, CA"], "has_laundry_service": ["", "dontcare"], "number_of_adults": ["", 0], "rating": ["", 0.0]}}]}
{"id": "live_multiple_296-130-5", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Austin, TX"], "has_laundry_service": ["True"], "number_of_adults": [4], "rating": [4.0]}}]}
{"id": "live_multiple_297-130-6", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Long Beach, CA"], "has_laundry_service": ["True"], "number_of_adults": [1], "rating": ["", 0.0]}}]}
{"id": "live_multiple_298-130-7", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Chicago, IL"], "has_laundry_service": ["True"], "number_of_adults": ["", 0], "rating": [3.9]}}]}
{"id": "live_multiple_299-130-8", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Sydney, Australia"], "has_laundry_service": ["True"], "number_of_adults": [3], "rating": ["", 0.0]}}]}
{"id": "live_multiple_300-130-9", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Rio de Janeiro, Brazil"], "has_laundry_service": ["", "dontcare"], "number_of_adults": [2], "rating": [4.2]}}]}
{"id": "live_multiple_301-131-0", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["London, UK"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": ["", 1]}}]}
{"id": "live_multiple_302-131-1", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["New York, NY"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": ["", 1]}}]}
{"id": "live_multiple_303-131-2", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Vancouver"], "star_rating": ["3"], "smoking_allowed": ["", false], "number_of_rooms": ["", 1]}}]}
{"id": "live_multiple_304-131-3", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Los Angeles, CA"], "star_rating": ["4"], "smoking_allowed": ["", false], "number_of_rooms": ["", 1]}}]}
{"id": "live_multiple_305-131-4", "ground_truth": [{"Hotels_4_ReserveHotel": {"place_name": ["dontcare"], "check_in_date": ["2023-07-15"], "stay_length": [5], "location": ["dontcare"], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_306-131-5", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Kuala Lumpur, Malaysia"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_307-131-6", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Philadelphia, PA"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": [3]}}]}
{"id": "live_multiple_308-131-7", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Phoenix, AZ"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": ["", 1]}}]}
{"id": "live_multiple_309-131-8", "ground_truth": [{"Hotels_4_ReserveHotel": {"place_name": ["dontcare"], "check_in_date": ["2023-08-15"], "stay_length": [2], "location": ["Berkeley, CA"], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_310-132-0", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Herbert Ross"], "genre": ["Family"], "cast": ["Betsy Widhalm"]}}]}
{"id": "live_multiple_311-132-1", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Wes Anderson"], "genre": ["Comedy"], "cast": ["Bill Murray"]}}]}
{"id": "live_multiple_312-132-2", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Jordan Peele"], "genre": ["Horror"], "cast": ["Lupita Nyong'o"]}}]}
{"id": "live_multiple_313-132-3", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Martin Kove"]}}]}
{"id": "live_multiple_314-132-4", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Jim Henson"], "genre": ["", "dontcare"], "cast": ["Jennifer Connelly"]}}]}
{"id": "live_multiple_315-132-5", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Herbert Ross"], "genre": ["", "dontcare"], "cast": ["James Shapkoff III"]}}]}
{"id": "live_multiple_316-132-6", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Offbeat"], "cast": ["Camila Sosa"]}}]}
{"id": "live_multiple_317-132-7", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Guillermo del Toro"], "genre": ["Fantasy"], "cast": ["Emma Watson"]}}]}
{"id": "live_multiple_318-132-8", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Daniel Camp"]}}]}
{"id": "live_multiple_319-132-9", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Gavin Hood"], "genre": ["Mystery"], "cast": ["Hattie Morahan"]}}]}
{"id": "live_multiple_320-132-10", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Thurop Van Orman"], "genre": ["", "dontcare"], "cast": ["Pete Davidson"]}}]}
{"id": "live_multiple_321-132-11", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Quentin Tarantino"], "genre": ["Bizarre"], "cast": ["Maya Hawke"]}}]}
{"id": "live_multiple_322-132-12", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Peter Jackson"], "genre": ["Fantasy"], "cast": ["Dominic Monaghan"]}}]}
{"id": "live_multiple_323-132-13", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Steven Spielberg"], "genre": ["", "dontcare"], "cast": ["Josef Sommer"]}}]}
{"id": "live_multiple_324-132-14", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Zoe Margaret Colletti"]}}]}
{"id": "live_multiple_325-132-15", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Riley Stearns"], "genre": ["", "dontcare"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_326-132-16", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Gurinder Chadha"], "genre": ["", "dontcare"], "cast": ["Vincent Andriano"]}}]}
{"id": "live_multiple_327-132-17", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Steven Spielberg"], "genre": ["Sci-fi"], "cast": ["James Keane"]}}]}
{"id": "live_multiple_328-132-18", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Peter Strickland"], "genre": ["Horror"], "cast": ["Gavin Brocker"]}}]}
{"id": "live_multiple_329-132-19", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["quentin tarantino"], "genre": ["", "dontcare"], "cast": ["eric stoltz"]}}]}
{"id": "live_multiple_330-132-20", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Joel Zwick"], "genre": ["", "dontcare"], "cast": ["Joey Fatone"]}}]}
{"id": "live_multiple_331-132-21", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Josephine Decker"], "genre": ["Thriller"], "cast": ["Sunita Mani"]}}]}
{"id": "live_multiple_332-132-22", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Mystery"], "cast": ["Kathy Griffin"]}}]}
{"id": "live_multiple_333-132-23", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_334-132-24", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Thriller"], "cast": ["Lisa Tharps"]}}]}
{"id": "live_multiple_335-132-25", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Taika Waititi"], "genre": ["Comedy"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_336-133-0", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Shape of You"], "artist": ["Ed Sheeran"], "device": ["Kitchen"], "album": ["", "dontcare"]}}]}
{"id": "live_multiple_337-133-1", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Remind Me"], "artist": ["Carrie Underwood"], "device": ["", "Living room"], "album": ["", "dontcare"]}}]}
{"id": "live_multiple_338-133-2", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Rock"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_339-133-3", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Speak Now"], "genre": ["", "dontcare"], "year": ["", 2010]}}]}
{"id": "live_multiple_340-133-4", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["", "dontcare"], "year": [2022]}}]}
{"id": "live_multiple_341-133-5", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Ores Aixmis"], "genre": ["Pop"], "year": [2019]}}]}
{"id": "live_multiple_342-133-6", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Eric Church"], "album": ["Chief"], "genre": ["Country"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_343-133-7", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Martin Garrix"], "album": ["The Martin Garrix Experience"], "genre": ["House"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_344-133-8", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Prequelle"], "genre": ["", "dontcare"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_345-133-9", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Kesha"], "album": ["Rainbow"], "genre": ["Pop"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_346-133-10", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Justin Bieber"], "album": ["", "dontcare"], "genre": ["Pop"], "year": [2013]}}]}
{"id": "live_multiple_347-133-11", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Electropop"], "year": [2018]}}]}
{"id": "live_multiple_348-133-12", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Meghan Trainor"], "album": ["", "dontcare"], "genre": ["Pop"], "year": [2018]}}]}
{"id": "live_multiple_349-133-13", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Vybz Kartel"], "album": ["", "dontcare"], "genre": ["Reggae"], "year": [2019]}}]}
{"id": "live_multiple_350-133-14", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Jinjer"], "album": ["", "dontcare"], "genre": ["Metal"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_351-133-15", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Imagine Dragons"], "album": ["Night Visions"], "genre": ["", "dontcare"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_352-133-16", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Pitbull"], "album": ["", "dontcare"], "genre": ["", "dontcare"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_353-133-17", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Halcyon"], "genre": ["Pop"], "year": [2016]}}]}
{"id": "live_multiple_354-133-18", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Enrique Iglesias"], "album": ["Euphoria"], "genre": ["", "dontcare"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_355-134-0", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Herbert Ross"], "genre": ["Family"], "cast": ["Ronald Young"]}}]}
{"id": "live_multiple_356-134-1", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Quentin Tarantino"], "genre": ["", "dontcare"], "cast": ["Lawrence Bender"]}}]}
{"id": "live_multiple_357-134-2", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Ving Rhames"]}}]}
{"id": "live_multiple_358-134-3", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Steven Spielberg"], "genre": ["Sci-fi"], "cast": ["J. Patrick McNamara"]}}]}
{"id": "live_multiple_359-134-4", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Josh Cooley"], "genre": ["Animation"], "cast": ["Bill Hader"]}}]}
{"id": "live_multiple_360-134-5", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Paul Downs Colaizzo"], "genre": ["Play"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_361-134-6", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["david leitch"], "genre": ["Action"], "cast": ["ryan reynolds"]}}]}
{"id": "live_multiple_362-134-7", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Sujeeth Reddy"], "genre": ["Action"], "cast": ["Supreet Reddy"]}}]}
{"id": "live_multiple_363-134-8", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Thurop Van Orman"], "genre": ["", "dontcare"], "cast": ["Zach Woods"]}}]}
{"id": "live_multiple_364-134-9", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Wes Anderson"], "genre": ["Comedy"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_365-134-10", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Gene Stupnitsky"], "genre": ["Comedy-drama"], "cast": ["Josh Barclay Caras"]}}]}
{"id": "live_multiple_366-134-11", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Herbert Ross"], "genre": ["", "dontcare"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_367-134-12", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Action"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_368-134-13", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Peter Strickland"], "genre": ["Horror"], "cast": ["Gwendoline Christie"]}}]}
{"id": "live_multiple_369-134-14", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Peter Jackson"], "genre": ["Fantasy"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_370-134-15", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Jim Henson"], "genre": ["Fantasy"], "cast": ["Danny John-Jules"]}}]}
{"id": "live_multiple_371-134-16", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Gregory La Cava"], "genre": ["Drama"], "cast": ["Franklin Pangborn"]}}]}
{"id": "live_multiple_372-134-17", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Joel Zwick"], "genre": ["Comedy"], "cast": ["Fiona Reid"]}}]}
{"id": "live_multiple_373-134-18", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Jagan Shakti"], "genre": ["Action"], "cast": ["Sanjay Kapoor"]}}]}
{"id": "live_multiple_374-134-19", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Riley Stearns"], "genre": ["", "dontcare"], "cast": ["C.J. Rush"]}}]}
{"id": "live_multiple_375-134-20", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Sameh Zoabi"], "genre": ["", "dontcare"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_376-135-0", "ground_truth": [{"Services_4_FindProvider": {"city": ["Santa Rosa, CA"], "type": ["Psychologist"]}}]}
{"id": "live_multiple_377-135-1", "ground_truth": [{"Services_4_FindProvider": {"city": ["Berkeley, CA"], "type": ["Family Counselor"]}}]}
{"id": "live_multiple_378-135-2", "ground_truth": [{"Services_4_FindProvider": {"city": ["Mountain View, CA"], "type": ["Family Counselor"]}}]}
{"id": "live_multiple_379-136-0", "ground_truth": [{"Alarm_1_GetAlarms": {"user_id": ["12345"], "include_disabled": ["", false]}}]}
{"id": "live_multiple_380-136-1", "ground_truth": [{"Alarm_1_GetAlarms": {"user_id": ["12345"], "include_disabled": ["", false]}}]}
{"id": "live_multiple_381-136-2", "ground_truth": [{"Alarm_1_GetAlarms": {"user_id": ["U123456"], "include_disabled": ["", false]}}]}
{"id": "live_multiple_382-137-0", "ground_truth": [{"Services_1_FindProvider": {"city": ["Fremont, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_383-137-1", "ground_truth": [{"Services_1_FindProvider": {"city": ["New York, NY"], "is_unisex": [true]}}]}
{"id": "live_multiple_384-137-2", "ground_truth": [{"Services_1_FindProvider": {"city": ["San Jose, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_385-137-3", "ground_truth": [{"Services_1_FindProvider": {"city": ["Campbell, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_386-137-4", "ground_truth": [{"Services_1_FindProvider": {"city": ["San Francisco, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_387-137-5", "ground_truth": [{"Services_1_FindProvider": {"city": ["New York, NY"], "is_unisex": [true]}}]}
{"id": "live_multiple_388-137-6", "ground_truth": [{"Services_1_FindProvider": {"city": ["Alameda, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_389-137-7", "ground_truth": [{"Services_1_FindProvider": {"city": ["Corte Madera, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_390-137-8", "ground_truth": [{"Services_1_FindProvider": {"city": ["Pleasanton, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_391-137-9", "ground_truth": [{"Services_1_FindProvider": {"city": ["San Lorenzo, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_392-138-0", "ground_truth": [{"Services_1_FindProvider": {"city": ["Austin, TX"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_393-138-1", "ground_truth": [{"Services_1_FindProvider": {"city": ["Pinole, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_394-138-2", "ground_truth": [{"Services_1_FindProvider": {"city": ["Berkeley, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_395-138-3", "ground_truth": [{"Services_1_FindProvider": {"city": ["Rohnert Park, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_396-139-0", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Chicago, IL"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_397-139-1", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Palo Alto, CA"], "date": ["2023-03-13"]}}]}
{"id": "live_multiple_398-139-2", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["San Diego, CA"], "date": ["2023-05-02"]}}]}
{"id": "live_multiple_399-139-3", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Chicago, IL"], "date": ["2023-05-02"]}}]}
{"id": "live_multiple_400-139-4", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Chicago, IL"], "date": ["2023-10-02"]}}]}
{"id": "live_multiple_401-139-5", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Toronto, ON", "Toronto, Canada"], "date": ["2023-10-02"]}}]}
{"id": "live_multiple_402-139-6", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["London, UK"], "date": ["2023-10-02"]}}]}
{"id": "live_multiple_403-139-7", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["London, UK"], "date": ["2023-04-05"]}}]}
{"id": "live_multiple_404-140-0", "ground_truth": [{"RideSharing_2_GetRide": {"destination": ["123 Beijing Street, San Francisco"], "number_of_seats": [1], "ride_type": ["Regular"]}}]}
{"id": "live_multiple_405-140-1", "ground_truth": [{"RideSharing_2_GetRide": {"destination": ["123 Main Street, Anytown"], "number_of_seats": [2], "ride_type": ["Luxury"]}}]}
{"id": "live_multiple_406-140-2", "ground_truth": [{"RideSharing_2_GetRide": {"destination": ["2508 University Avenue, Palo Alto"], "number_of_seats": [""], "ride_type": [""]}}]}
{"id": "live_multiple_407-140-3", "ground_truth": [{"RideSharing_2_GetRide": {"destination": ["540 El Camino Real, Berkeley"], "number_of_seats": [""], "ride_type": ["Regular"]}}]}
{"id": "live_multiple_408-140-4", "ground_truth": [{"RideSharing_2_GetRide": {"destination": ["123 Park Branham Apartments, San Jose"], "number_of_seats": [2], "ride_type": ["Pool"]}}]}
{"id": "live_multiple_409-140-5", "ground_truth": [{"RideSharing_2_GetRide": {"destination": ["123 White Street, San Jose"], "number_of_seats": [1], "ride_type": ["Pool"]}}]}
{"id": "live_multiple_410-140-6", "ground_truth": [{"RideSharing_2_GetRide": {"destination": ["3236 Grand Avenue, Oakland"], "number_of_seats": [""], "ride_type": ["Luxury"]}}]}
{"id": "live_multiple_411-141-0", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Union City, CA"], "theater_name": ["", "dontcare"], "genre": ["Animation"], "show_type": ["imax"]}}]}
{"id": "live_multiple_412-141-1", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["The Grand Adventure"], "number_of_tickets": [2], "show_date": ["2023-11-15"], "location": ["Santa Rosa, CA"], "show_time": ["17:30"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_413-141-2", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["After The Wedding"], "number_of_tickets": [2], "show_date": ["2023-04-22"], "location": ["Berkeley, CA"], "show_time": ["19:00"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_414-141-3", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Newark, NJ"], "theater_name": ["", "dontcare"], "genre": ["", "dontcare"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_415-141-4", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["Space Adventure"], "number_of_tickets": [2], "show_date": ["2023-10-02"], "location": ["San Jose, CA"], "show_time": ["23:00"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_416-141-5", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Healdsburg, CA"], "theater_name": ["Raven Film Center"], "genre": ["", "dontcare"], "show_type": ["3d"]}}]}
{"id": "live_multiple_417-141-6", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Jose, CA"], "theater_name": ["", "dontcare"], "genre": ["", "dontcare"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_418-141-7", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["Space Adventure"], "number_of_tickets": [2], "show_date": ["2023-04-15"], "location": ["New York, NY"], "show_time": ["19:30"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_419-141-8", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Ramon, CA"], "theater_name": ["Regal Crow Canyon"], "genre": ["Drama"], "show_type": ["regular"]}}]}
{"id": "live_multiple_420-141-9", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["Avengers"], "number_of_tickets": [3], "show_date": ["2023-10-06"], "location": ["Los Angeles, CA"], "show_time": ["19:00"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_421-141-10", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Mountain View, CA"], "theater_name": ["", "dontcare"], "genre": ["", "dontcare"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_422-141-11", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Santa Rosa, CA"], "theater_name": ["", "dontcare"], "genre": ["Animation"], "show_type": ["imax"]}}]}
{"id": "live_multiple_423-141-12", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["The Last Adventure"], "number_of_tickets": [2], "show_date": [""], "location": ["San Francisco, CA"], "show_time": ["20:30"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_424-141-13", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Bruno, CA"], "theater_name": ["Century at Tanforan and XD"], "genre": ["Sci-fi"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_425-141-14", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["Ad Astra"], "number_of_tickets": [1], "show_date": [""], "location": ["Berkeley, CA"], "show_time": ["21:00"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_426-141-15", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Hayward, CA"], "theater_name": ["", "dontcare"], "genre": ["Sci-fi"], "show_type": ["regular"]}}]}
{"id": "live_multiple_427-141-16", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Jose, CA"], "theater_name": ["Century 20 Oakridge and XD"], "genre": ["Drama"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_428-141-17", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Berkeley, CA"], "theater_name": ["", "dontcare"], "genre": ["Sci-fi"], "show_type": ["imax"]}}]}
{"id": "live_multiple_429-141-18", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["Toy Story 4"], "number_of_tickets": [4], "show_date": ["2023-06-15"], "location": ["Los Angeles, CA"], "show_time": ["14:00"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_430-141-19", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["Eternal Warriors"], "number_of_tickets": [3], "show_date": ["2023-04-15"], "location": ["Los Angeles, CA"], "show_time": ["19:00"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_431-141-20", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Danville, CA"], "theater_name": ["Century Blackhawk Plaza"], "genre": ["", "dontcare"], "show_type": ["imax"]}}]}
{"id": "live_multiple_432-141-21", "ground_truth": [{"Movies_1_BuyMovieTickets": {"movie_name": ["Once Upon a Time In Hollywood"], "number_of_tickets": [2], "show_date": ["2023-04-15"], "location": ["Los Angeles, CA"], "show_time": ["19:00"], "show_type": ["", "regular"]}}]}
{"id": "live_multiple_433-141-22", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Napa, CA"], "theater_name": ["Century Napa Valley and XD"], "genre": ["", "dontcare"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_434-142-0", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-03-09"], "pickup_time": ["09:00"], "end_date": ["2023-03-10"], "car_type": ["Sedan"]}}]}
{"id": "live_multiple_435-142-1", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-04-21"], "pickup_time": ["10:00"], "end_date": ["2023-04-25"], "car_type": ["Sedan"]}}]}
{"id": "live_multiple_436-142-2", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Portland, OR"], "start_date": ["2023-04-22"], "pickup_time": ["10:00"], "end_date": ["2023-04-27"], "car_type": ["", "dontcare"]}}]}
{"id": "live_multiple_437-142-3", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-05-05"], "pickup_time": ["12:30"], "end_date": ["2023-05-11"], "car_type": ["", "dontcare"]}}]}
{"id": "live_multiple_438-142-4", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-04-24"], "pickup_time": ["10:00"], "end_date": ["2023-04-28"], "car_type": ["", "dontcare"]}}]}
{"id": "live_multiple_439-143-0", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["Sacramento, CA"], "to": ["Fresno, CA"], "date_of_journey": ["2023-03-10"]}}]}
{"id": "live_multiple_440-144-0", "ground_truth": [{"Services_4_FindProvider": {"city": ["Berkeley, CA"], "type": ["Family Counselor"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_441-144-1", "ground_truth": [{"Services_4_FindProvider": {"city": ["Los Altos, CA"], "type": ["Family Counselor"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_442-144-2", "ground_truth": [{"Services_4_FindProvider": {"city": ["Campbell, CA"], "type": ["Psychologist"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_443-144-3", "ground_truth": [{"Services_4_FindProvider": {"city": ["Pittsburg, PA"], "type": ["Psychiatrist"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_444-144-4", "ground_truth": [{"Services_4_FindProvider": {"city": ["Los Gatos, CA"], "type": ["Family Counselor"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_445-144-5", "ground_truth": [{"Services_4_FindProvider": {"city": ["Santa Rosa, CA"], "type": ["Psychiatrist"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_446-144-6", "ground_truth": [{"Services_4_FindProvider": {"city": ["Vacaville, CA"], "type": ["Psychologist"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_447-144-7", "ground_truth": [{"Services_4_FindProvider": {"city": ["Novato, CA"], "type": ["Psychologist"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_448-144-8", "ground_truth": [{"Services_4_FindProvider": {"city": ["St. Helena, CA"], "type": ["Family Counselor"], "accepts_insurance": ["", false]}}]}
{"id": "live_multiple_449-145-0", "ground_truth": [{"Flights_4_SearchRoundtripFlights": {"origin_airport": ["JFK"], "destination_airport": ["LAX"], "departure_date": ["2023-04-15"], "return_date": ["2023-04-22"], "seating_class": ["Business"], "number_of_tickets": ["", 1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_450-145-1", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, France"], "free_entry": ["", "dontcare"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_451-145-2", "ground_truth": [{"Flights_4_SearchRoundtripFlights": {"origin_airport": ["Atlanta"], "destination_airport": ["Boston"], "departure_date": ["2023-03-12"], "return_date": ["2023-03-19"], "seating_class": ["", "Economy"], "number_of_tickets": ["", 1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_452-145-3", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["New York, NY"], "free_entry": ["True"], "category": ["Museum"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_453-145-4", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["San Francisco, CA"], "free_entry": ["True"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_454-145-5", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["London, UK"], "free_entry": ["True"], "category": ["Museum"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_455-145-6", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["London, UK"], "free_entry": ["True"], "category": ["Park"], "good_for_kids": ["", "dontcare"]}}]}
{"id": "live_multiple_456-145-7", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["London, UK"], "free_entry": ["True"], "category": ["Performing Arts Venue"], "good_for_kids": ["", "dontcare"]}}]}
{"id": "live_multiple_457-145-8", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, France"], "free_entry": ["", "dontcare"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_458-145-9", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, France"], "free_entry": ["True"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_459-145-10", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Berlin, Germany"], "free_entry": ["True"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_460-145-11", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["New York, NY"], "free_entry": ["True"], "category": ["Park"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_461-145-12", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, France"], "free_entry": ["True"], "category": ["Shopping Area"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_462-145-13", "ground_truth": [{"Flights_4_SearchRoundtripFlights": {"origin_airport": ["San Francisco"], "destination_airport": ["Atlanta"], "departure_date": ["2023-03-01"], "return_date": ["2023-03-06"], "seating_class": ["", "Economy"], "number_of_tickets": ["", 1], "airlines": ["American Airlines"]}}]}
{"id": "live_multiple_463-145-14", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Philadelphia, PA"], "free_entry": ["True"], "category": ["", "dontcare"], "good_for_kids": ["", "dontcare"]}}]}
{"id": "live_multiple_464-145-15", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Orlando, FL"], "free_entry": ["True"], "category": ["Theme Park"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_465-145-16", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["JFK"], "destination_airport": ["LAX"], "departure_date": ["2023-10-06"], "seating_class": ["", "Economy"], "number_of_tickets": [1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_466-145-17", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["New York, NY"], "free_entry": ["True"], "category": ["Shopping Area"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_467-145-18", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Portland, OR"], "free_entry": ["False"], "category": ["Historical Landmark"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_468-145-19", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Seattle, WA"], "free_entry": ["", "dontcare"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_469-145-20", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Toronto, Canada"], "free_entry": ["", "dontcare"], "category": ["Park"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_470-145-21", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, France"], "free_entry": ["True"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_471-145-22", "ground_truth": [{"Flights_4_SearchRoundtripFlights": {"origin_airport": ["JFK"], "destination_airport": ["LAX"], "departure_date": ["2023-04-15"], "return_date": ["2023-04-22"], "seating_class": ["", "Economy"], "number_of_tickets": ["", 1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_472-145-23", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Chicago, IL"], "free_entry": ["", "dontcare"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_473-145-24", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["New York, NY"], "free_entry": ["True"], "category": ["Museum"], "good_for_kids": ["", "dontcare"]}}]}
{"id": "live_multiple_474-145-25", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, France"], "free_entry": ["True"], "category": ["Museum"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_475-146-0", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Electropop"], "year": ["2019"]}}]}
{"id": "live_multiple_476-146-1", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Electropop"], "year": ["2014"]}}]}
{"id": "live_multiple_477-146-2", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["We Are Not Your Kind"], "genre": ["Rock"], "year": ["2019"]}}]}
{"id": "live_multiple_478-146-3", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["", "dontcare"], "year": ["2021"]}}]}
{"id": "live_multiple_479-146-4", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Electropop"], "year": ["2014"]}}]}
{"id": "live_multiple_480-146-5", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Summer Vibes"], "artist": ["DJ Sunny"], "device": ["Kitchen"], "album": ["", "Any Album"]}}]}
{"id": "live_multiple_481-146-6", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["High Expectations"], "genre": ["", "dontcare"], "year": ["2019"]}}]}
{"id": "live_multiple_482-146-7", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Reggae"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_483-146-8", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Sia"], "album": ["This Is Acting"], "genre": ["Pop"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_484-146-9", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["The New Classic"], "genre": ["", "dontcare"], "year": ["2017"]}}]}
{"id": "live_multiple_485-147-0", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["New York, NY"], "to": ["Los Angeles, CA"], "date_of_journey": ["04/25/2023"]}}]}
{"id": "live_multiple_486-147-1", "ground_truth": [{"Trains_1_GetTrainTickets": {"_from": ["New York, NY"], "to": ["Los Angeles, CA"], "date_of_journey": ["04/23/2023"], "journey_start_time": ["10:00"], "number_of_adults": [2], "trip_protection": [false], "_class": ["Business"]}}]}
{"id": "live_multiple_487-147-2", "ground_truth": [{"Trains_1_GetTrainTickets": {"_from": ["New York, NY"], "to": ["Sacramento, CA"], "date_of_journey": ["03/13/2023"], "journey_start_time": ["09:00"], "number_of_adults": [2], "trip_protection": [true], "_class": ["Business"]}}]}
{"id": "live_multiple_488-147-3", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["Portland, OR"], "to": ["Seattle, WA"], "date_of_journey": ["04/22/2023"]}}]}
{"id": "live_multiple_489-147-4", "ground_truth": [{"Trains_1_GetTrainTickets": {"_from": ["New York, NY"], "to": ["Phoenix, AZ"], "date_of_journey": ["04/23/2023"], "journey_start_time": ["13:45"], "number_of_adults": [1], "trip_protection": [false], "_class": ["", "Value"]}}]}
{"id": "live_multiple_490-148-0", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Chicago, IL"], "date": ["2023-04-29"]}}]}
{"id": "live_multiple_491-148-1", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Berkeley, CA"], "date": ["2023-05-12"]}}]}
{"id": "live_multiple_492-148-2", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Berkeley, CA"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_493-148-3", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["New York, NY"], "date": ["2023-04-15"]}}]}
{"id": "live_multiple_494-148-4", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-04-15"]}}]}
{"id": "live_multiple_495-148-5", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["", "null"]}}]}
{"id": "live_multiple_496-148-6", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-03-25"]}}]}
{"id": "live_multiple_497-148-7", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Oakland, CA"], "date": ["2023-04-11"]}}]}
{"id": "live_multiple_498-148-8", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-03-01"]}}]}
{"id": "live_multiple_499-148-9", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-03-09"]}}]}
{"id": "live_multiple_500-148-10", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["San Francisco, CA"], "date": ["", "null"]}}]}
{"id": "live_multiple_501-148-11", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["San Francisco, CA"], "date": ["2023-10-01"]}}]}
{"id": "live_multiple_502-148-12", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["New York, NY"], "date": ["2023-03-12"]}}]}
{"id": "live_multiple_503-149-0", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["JFK"], "destination_airport": ["LAX"], "departure_date": ["2023-04-15"], "seating_class": ["Premium Economy"], "number_of_tickets": ["", 1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_504-149-1", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["New York"], "destination_airport": ["Los Angeles"], "departure_date": ["2023-04-15"], "seating_class": ["", "Economy"], "number_of_tickets": [1], "airlines": ["Delta Airlines"]}}]}
{"id": "live_multiple_505-149-2", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["San Diego"], "destination_airport": ["Chicago"], "departure_date": ["2023-05-20"], "seating_class": ["Business"], "number_of_tickets": [1], "airlines": ["American Airlines"]}}]}
{"id": "live_multiple_506-149-3", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["JFK"], "destination_airport": ["LAX"], "departure_date": ["2023-04-15"], "seating_class": ["", "Economy"], "number_of_tickets": [1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_507-149-4", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": [""], "destination_airport": ["New York"], "departure_date": ["2023-03-14"], "seating_class": ["", "Economy"], "number_of_tickets": [1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_508-149-5", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["JFK"], "destination_airport": ["LAX"], "departure_date": ["2023-04-15"], "seating_class": ["", "Economy"], "number_of_tickets": [1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_509-149-6", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["Vancouver"], "destination_airport": ["Seattle"], "departure_date": ["2023-03-12"], "seating_class": ["Business"], "number_of_tickets": [1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_510-149-7", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["JFK"], "destination_airport": ["Portland", "PDX"], "departure_date": ["2023-04-20"], "seating_class": ["", "Economy"], "number_of_tickets": [1], "airlines": ["", "dontcare"]}}]}
{"id": "live_multiple_511-149-8", "ground_truth": [{"Flights_4_SearchOnewayFlight": {"origin_airport": ["Chicago"], "destination_airport": ["Shanghai"], "departure_date": ["2023-04-15"], "seating_class": ["", "Economy"], "number_of_tickets": ["", 1], "airlines": ["Delta Airlines"]}}]}
{"id": "live_multiple_512-150-0", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Los Angeles, CA"], "theater_name": ["", "dontcare"], "genre": ["", "dontcare"], "show_type": ["3d"]}}]}
{"id": "live_multiple_513-150-1", "ground_truth": [{"Movies_1_FindMovies": {"location": ["New York, NY"], "theater_name": ["", "dontcare"], "genre": ["Action"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_514-150-2", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Jose, CA"], "theater_name": ["Century 20"], "genre": ["", "dontcare"], "show_type": ["regular"]}}]}
{"id": "live_multiple_515-150-3", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Jose, CA"], "theater_name": ["West Wind Capitol Drive-In", "West Wind Capitol"], "genre": ["Horror"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_516-150-4", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Ramon, CA"], "theater_name": ["Regal", "Regal Theater"], "genre": ["", "dontcare"], "show_type": ["imax"]}}]}
{"id": "live_multiple_517-150-5", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Los Angeles, CA"], "theater_name": ["", "dontcare"], "genre": ["Supernatural"], "show_type": ["regular"]}}]}
{"id": "live_multiple_518-150-6", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Sonoma, CA"], "theater_name": ["", "dontcare"], "genre": ["Action"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_519-150-7", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Los Angeles, CA"], "theater_name": ["", "dontcare"], "genre": ["Documentary"], "show_type": ["regular"]}}]}
{"id": "live_multiple_520-150-8", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Saratoga, CA"], "theater_name": ["AMC"], "genre": ["", "dontcare"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_521-150-9", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Sonoma, CA"], "theater_name": ["", "dontcare"], "genre": ["Family"], "show_type": ["3d"]}}]}
{"id": "live_multiple_522-150-10", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Larkspur, CA"], "theater_name": ["", "dontcare"], "genre": ["Action"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_523-150-11", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Jose, CA"], "theater_name": ["3 Below Theaters and Lounge"], "genre": ["War"], "show_type": ["regular"]}}]}
{"id": "live_multiple_524-151-0", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Chicago, IL"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_525-151-1", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Toronto, Canada"], "date": ["05/01/2023"]}}]}
{"id": "live_multiple_526-151-2", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["San Diego, CA"], "date": ["05/05/2023"]}}]}
{"id": "live_multiple_527-151-3", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Seattle, WA"], "date": ["05/15/2023"]}}]}
{"id": "live_multiple_528-151-4", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["New York, NY"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_529-151-5", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Los Angeles, CA"], "date": ["04/07/2023"]}}]}
{"id": "live_multiple_530-151-6", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["09/09/2023"]}}]}
{"id": "live_multiple_531-151-7", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Philadelphia, PA"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_532-151-8", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Portland, OR"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_533-151-9", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["London, UK"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_534-151-10", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Livermore, CA"], "date": ["03/06/2023"]}}]}
{"id": "live_multiple_535-151-11", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Belvedere, CA"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_536-151-12", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Portland, OR"], "date": ["03/09/2023"]}}]}
{"id": "live_multiple_537-151-13", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Chicago, IL"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_538-152-0", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Sunnyvale, CA"], "intent": ["buy"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": ["", false], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_539-152-1", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Francisco, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [2], "has_garage": [true], "in_unit_laundry": [true]}}]}
{"id": "live_multiple_540-152-2", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Fremont, CA"], "intent": ["rent"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": [true], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_541-152-3", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Austin, TX"], "intent": ["rent"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": ["", false], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_542-152-4", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Austin, Texas", "Austin, TX"], "intent": ["buy"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": [true], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_543-152-5", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Francisco, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [1], "has_garage": ["", false], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_544-152-6", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Mountain View, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [1], "has_garage": ["", false], "in_unit_laundry": [true]}}]}
{"id": "live_multiple_545-152-7", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Petaluma, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [2], "has_garage": ["", false], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_546-152-8", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Francisco, CA"], "intent": ["buy"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": ["", false], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_547-152-9", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Francisco, CA"], "intent": ["buy"], "number_of_beds": [3], "number_of_baths": [1], "has_garage": [true], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_548-152-10", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Francisco, CA"], "intent": ["rent"], "number_of_beds": [4], "number_of_baths": [3], "has_garage": ["", false], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_549-152-11", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Jose, CA"], "intent": ["buy"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": [true], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_550-152-12", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Denver, CO"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [2], "has_garage": ["", false], "in_unit_laundry": [true]}}]}
{"id": "live_multiple_551-153-0", "ground_truth": [{"Services_1_FindProvider": {"city": ["Berkeley, CA"], "is_unisex": ["", "dontcare"]}}]}
{"id": "live_multiple_552-153-1", "ground_truth": [{"Services_1_FindProvider": {"city": ["Walnut Creek"], "is_unisex": [true]}}]}
{"id": "live_multiple_553-153-2", "ground_truth": [{"Services_1_FindProvider": {"city": ["San Francisco, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_554-154-0", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["London, UK"], "start_date": ["2023-03-10"], "pickup_time": ["10:00"], "end_date": ["2023-03-17"], "car_type": ["", "dontcare"]}}]}
{"id": "live_multiple_555-154-1", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-04-14"], "pickup_time": ["10:00"], "end_date": ["2023-04-18"], "car_type": ["Sedan"]}}]}
{"id": "live_multiple_556-154-2", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Long Beach, CA"], "start_date": ["2023-04-12"], "pickup_time": ["14:00"], "end_date": ["2023-04-12"], "car_type": ["Sedan"]}}]}
{"id": "live_multiple_557-154-3", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-04-18"], "pickup_time": ["10:00"], "end_date": ["2023-04-24"], "car_type": ["", "dontcare"]}}]}
{"id": "live_multiple_558-154-4", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-05-15"], "pickup_time": ["10:00"], "end_date": ["2023-05-20"], "car_type": ["", "dontcare"]}}]}
{"id": "live_multiple_559-154-5", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-04-08"], "pickup_time": ["10:00"], "end_date": ["2023-04-10"], "car_type": ["", "dontcare"]}}]}
{"id": "live_multiple_560-155-0", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["", "any"]}}]}
{"id": "live_multiple_561-155-1", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["", "any"]}}]}
{"id": "live_multiple_562-155-2", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Seattle, WA"], "date": ["", "any"]}}]}
{"id": "live_multiple_563-155-3", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Philadelphia", "Philadelphia, PA"], "date": ["2023-03-07"]}}]}
{"id": "live_multiple_564-155-4", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Seattle, WA"], "date": ["2023-03-07"]}}]}
{"id": "live_multiple_565-155-5", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-03-12"]}}]}
{"id": "live_multiple_566-155-6", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music", "Theater"], "city": ["Sacramento, CA"], "date": ["", "any"]}}]}
{"id": "live_multiple_567-155-7", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Los Angeles"], "date": ["2023-03-04"]}}]}
{"id": "live_multiple_568-155-8", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["New York, NY"], "date": ["2023-09-09"]}}]}
{"id": "live_multiple_569-155-9", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music", "Theater"], "city": ["Las Vegas, NV"], "date": ["2023-03-06"]}}]}
{"id": "live_multiple_570-155-10", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["", "any"]}}]}
{"id": "live_multiple_571-155-11", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["San Francisco, CA"], "date": ["2023-03-09"]}}]}
{"id": "live_multiple_572-155-12", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Fresno, CA"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_573-155-13", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Seattle, WA"], "date": ["2023-03-11"]}}]}
{"id": "live_multiple_574-156-0", "ground_truth": [{"Services_1_FindProvider": {"city": ["Berkeley, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_575-156-1", "ground_truth": [{"Services_1_FindProvider": {"city": ["Santa Rosa, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_576-156-2", "ground_truth": [{"Services_1_FindProvider": {"city": ["Berkeley, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_577-156-3", "ground_truth": [{"Services_1_FindProvider": {"city": ["San Jose, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_578-156-4", "ground_truth": [{"Services_1_FindProvider": {"city": ["Berkeley, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_579-157-0", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Comedy"], "starring": ["Jim Carrey"]}}]}
{"id": "live_multiple_580-157-1", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Comedy"], "starring": ["Vanessa Przada"]}}]}
{"id": "live_multiple_581-157-2", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Sci-fi"], "starring": ["", "any"]}}]}
{"id": "live_multiple_582-157-3", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Comedy"], "starring": ["Jim Carrey"]}}]}
{"id": "live_multiple_583-157-4", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Fantasy"], "starring": ["Bret McKenzie"]}}]}
{"id": "live_multiple_584-157-5", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Sci-fi"], "starring": ["Chris Hemsworth, Zoe Saldana"]}}]}
{"id": "live_multiple_585-157-6", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Action"], "starring": ["Bruce Willis"]}}]}
{"id": "live_multiple_586-157-7", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Fantasy"], "starring": ["Timothy Bateson"]}}]}
{"id": "live_multiple_587-157-8", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Horror"], "starring": ["Christopher Lee"]}}]}
{"id": "live_multiple_588-157-9", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Horror"], "starring": ["Arthur Lowe"]}}]}
{"id": "live_multiple_589-157-10", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Sci-fi"], "starring": ["Bobby Nish"]}}]}
{"id": "live_multiple_590-157-11", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Animation"], "starring": ["Christina-Ann Zalamea"]}}]}
{"id": "live_multiple_591-157-12", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Drama"], "starring": ["Dan Bittner"]}}]}
{"id": "live_multiple_592-157-13", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Offbeat"], "starring": ["Inbal Amirav"]}}]}
{"id": "live_multiple_593-157-14", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Comedy"], "starring": ["Ellise Chappell"]}}]}
{"id": "live_multiple_594-158-0", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["London, UK"], "star_rating": ["", "dontcare"], "smoking_allowed": [false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_595-158-1", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["New York, NY"], "star_rating": ["3"], "smoking_allowed": [true], "number_of_rooms": [2]}}]}
{"id": "live_multiple_596-158-2", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["San Francisco, CA"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": [1]}}]}
{"id": "live_multiple_597-158-3", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Toronto, Canada"], "star_rating": ["4"], "smoking_allowed": ["", false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_598-158-4", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Washington D.C."], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_599-158-5", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Delhi, India"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_600-158-6", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["London, UK"], "star_rating": ["", "dontcare"], "smoking_allowed": [true], "number_of_rooms": ["2"]}}]}
{"id": "live_multiple_601-158-7", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Kuala Lumpur"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_602-158-8", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Nairobi, KE"], "star_rating": ["4"], "smoking_allowed": ["", false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_603-158-9", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["New York, NY"], "star_rating": ["3"], "smoking_allowed": ["", false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_604-158-10", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Sacramento, CA"], "star_rating": ["", "dontcare"], "smoking_allowed": ["", false], "number_of_rooms": ["", "dontcare"]}}]}
{"id": "live_multiple_605-158-11", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Paris, FR"], "star_rating": ["3"], "smoking_allowed": ["", false], "number_of_rooms": ["1"]}}]}
{"id": "live_multiple_606-158-12", "ground_truth": [{"Hotels_4_SearchHotel": {"location": ["Sydney, AU"], "star_rating": ["4"], "smoking_allowed": [true], "number_of_rooms": ["2"]}}]}
{"id": "live_multiple_607-159-0", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-03-14"]}}]}
{"id": "live_multiple_608-159-1", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Chicago, IL"], "date": ["2023-03-13"]}}]}
{"id": "live_multiple_609-159-2", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Philadelphia, PA"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_610-159-3", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Portland, OR"], "date": ["2023-03-14"]}}]}
{"id": "live_multiple_611-159-4", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Philadelphia, PA"], "date": ["2023-09-30"]}}]}
{"id": "live_multiple_612-159-5", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Los Angeles, CA"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_613-159-6", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["London, UK"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_614-159-7", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Portland, OR"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_615-159-8", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["San Diego, CA"], "date": ["2023-04-08"]}}]}
{"id": "live_multiple_616-159-9", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Los Angeles, CA"], "date": ["2023-03-11"]}}]}
{"id": "live_multiple_617-159-10", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Los Angeles, CA"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_618-159-11", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["London, UK"], "date": ["2023-06-12"]}}]}
{"id": "live_multiple_619-159-12", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Long Beach, CA"], "date": ["2023-03-12"]}}]}
{"id": "live_multiple_620-160-0", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["Mary"], "amount": [200.0], "private_visibility": [true]}}]}
{"id": "live_multiple_621-160-1", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [154.0], "receiver": ["<EMAIL>"], "private_visibility": [true]}}]}
{"id": "live_multiple_622-160-2", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["credit card"], "amount": [29.0], "receiver": ["Thomas"], "private_visibility": ["", false]}}]}
{"id": "live_multiple_623-160-3", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["Wilson"], "amount": [42.0], "private_visibility": [true]}}]}
{"id": "live_multiple_624-160-4", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["<EMAIL>"], "amount": [150.25], "private_visibility": [true]}}]}
{"id": "live_multiple_625-160-5", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [200.0], "receiver": ["<EMAIL>"], "private_visibility": [true]}}]}
{"id": "live_multiple_626-160-6", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["<EMAIL>"], "amount": [83.0], "private_visibility": [true]}}]}
{"id": "live_multiple_627-160-7", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [200.0], "receiver": ["Diego"], "private_visibility": [true]}}]}
{"id": "live_multiple_628-160-8", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["Diego"], "amount": [108.0], "private_visibility": ["", false]}}]}
{"id": "live_multiple_629-160-9", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [250.0], "receiver": ["Rachel"], "private_visibility": [true]}}]}
{"id": "live_multiple_630-160-10", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["Amelia"], "amount": [15500.0], "private_visibility": ["", false]}}]}
{"id": "live_multiple_631-160-11", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["<EMAIL>"], "amount": [4500.0], "private_visibility": ["", false]}}]}
{"id": "live_multiple_632-161-0", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Welcome To The Fishbowl"], "genre": ["", "dontcare"], "year": ["2015"]}}]}
{"id": "live_multiple_633-161-1", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Rock"], "year": ["2015"]}}]}
{"id": "live_multiple_634-161-2", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Country"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_635-161-3", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Electropop"], "year": ["2019"]}}]}
{"id": "live_multiple_636-161-4", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Tove Lo"], "album": ["", "dontcare"], "genre": ["Electropop"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_637-161-5", "ground_truth": [{"Music_3_PlayMedia": {"track": ["any", "rock songs"], "artist": ["Thousand Foot Krutch"], "device": ["Kitchen"], "album": ["", "any"]}}]}
{"id": "live_multiple_638-161-6", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Sia"], "album": ["Everyday Is Christmas"], "genre": ["Holiday"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_639-161-7", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["High Expectations"], "genre": ["", "dontcare"], "year": ["2019"]}}]}
{"id": "live_multiple_640-161-8", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Cody Johnson"], "album": ["Gotta Be Me"], "genre": ["", "dontcare"], "year": ["2019"]}}]}
{"id": "live_multiple_641-161-9", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Jess Glynne"], "album": ["", "dontcare"], "genre": ["", "dontcare"], "year": ["2018"]}}]}
{"id": "live_multiple_642-161-10", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Tyler Shaw"], "album": ["", "dontcare"], "genre": ["Pop"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_643-161-11", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Radwimps"], "album": ["", "dontcare"], "genre": ["Rock"], "year": ["2016"]}}]}
{"id": "live_multiple_644-161-12", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Pop"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_645-161-13", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Madonna"], "album": ["", "dontcare"], "genre": ["", "dontcare"], "year": ["2019"]}}]}
{"id": "live_multiple_646-161-14", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Starboy"], "genre": ["", "dontcare"], "year": ["2020"]}}]}
{"id": "live_multiple_647-161-15", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Ariana Grande"], "album": ["", "dontcare"], "genre": ["", "dontcare"], "year": ["2018"]}}]}
{"id": "live_multiple_648-161-16", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Little Mix"], "album": ["Get Weird"], "genre": ["", "dontcare"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_649-161-17", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Narrated For You"], "genre": ["", "dontcare"], "year": ["2022"]}}]}
{"id": "live_multiple_650-161-18", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Raees"], "genre": ["", "dontcare"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_651-161-19", "ground_truth": [{"Music_3_PlayMedia": {"track": ["The Getaway"], "artist": ["Red Hot Chili Peppers"], "device": ["", "Living room"], "album": ["The Getaway"]}}]}
{"id": "live_multiple_652-161-20", "ground_truth": [{"Music_3_PlayMedia": {"track": ["any"], "artist": ["Outsider"], "device": ["", "Living room"], "album": ["", "any"]}}]}
{"id": "live_multiple_653-161-21", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Halcyon"], "genre": ["", "dontcare"], "year": ["2012"]}}]}
{"id": "live_multiple_654-161-22", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Pop"], "year": ["2018"]}}]}
{"id": "live_multiple_655-161-23", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Warrior"], "genre": ["Pop"], "year": ["2012"]}}]}
{"id": "live_multiple_656-161-24", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Harris J"], "album": ["Salam"], "genre": ["Pop"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_657-161-25", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Shape of You"], "artist": ["Ed Sheeran"], "device": ["", "Living room"], "album": ["", "any"]}}]}
{"id": "live_multiple_658-162-0", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["New York, NY"], "date": ["2023-03-12"]}}]}
{"id": "live_multiple_659-162-1", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["New York, NY"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_660-162-2", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_661-162-3", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-05-21"]}}]}
{"id": "live_multiple_662-162-4", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-10-07"]}}]}
{"id": "live_multiple_663-162-5", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Philadelphia, PA"], "date": ["2023-03-08"]}}]}
{"id": "live_multiple_664-162-6", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_665-162-7", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Los Angeles, CA"], "date": ["2023-03-09"]}}]}
{"id": "live_multiple_666-162-8", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-10-01"]}}]}
{"id": "live_multiple_667-162-9", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["New York, NY"], "date": ["2023-03-05"]}}]}
{"id": "live_multiple_668-162-10", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["New York, NY"], "date": ["2023-10-17"]}}]}
{"id": "live_multiple_669-162-11", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-03-07"]}}]}
{"id": "live_multiple_670-162-12", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Portland, OR"], "date": ["2023-10-09"]}}]}
{"id": "live_multiple_671-162-13", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-10-01"]}}]}
{"id": "live_multiple_672-162-14", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Los Angeles, CA"], "date": ["2023-10-01"]}}]}
{"id": "live_multiple_673-162-15", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-10-01"]}}]}
{"id": "live_multiple_674-162-16", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Los Angeles, CA"], "date": ["", "dontcare"]}}]}
{"id": "live_multiple_675-163-0", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Ciudad de Mexico, CDMX", "Ciudad de Mexico, Mexico"], "date": ["2023-05-05"]}}]}
{"id": "live_multiple_676-163-1", "ground_truth": [{"Weather_1_GetWeather": {"city": ["New York, NY"], "date": ["2023-10-02"]}}]}
{"id": "live_multiple_677-163-2", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Philadelphia, PA"], "date": ["2024-04-13"]}}]}
{"id": "live_multiple_678-163-3", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Washington D.C."], "date": ["20023-04-2"]}}]}
{"id": "live_multiple_679-163-4", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Sydney, AU"], "date": ["2023-03-02"]}}]}
{"id": "live_multiple_680-163-5", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Chicago, IL"], "date": ["2023-03-08"]}}]}
{"id": "live_multiple_681-163-6", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Vancouver, CA"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_682-163-7", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Seattle, WA"], "date": ["2023-03-04"]}}]}
{"id": "live_multiple_683-163-8", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Miami, FL"], "date": ["2024-03-03"]}}]}
{"id": "live_multiple_684-164-0", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Riley Stearns"], "genre": ["Thriller"], "cast": ["Steve Terada"]}}]}
{"id": "live_multiple_685-164-1", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Wes Anderson"], "genre": ["Offbeat"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_686-164-2", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Thriller"], "cast": ["Leland Orser"]}}]}
{"id": "live_multiple_687-164-3", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Guillermo del Toro"], "genre": ["Fantasy"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_688-164-4", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Family"], "cast": ["Carol Sutton"]}}]}
{"id": "live_multiple_689-164-5", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Gavin Hood"], "genre": ["Mystery"], "cast": ["Rhys Ifans"]}}]}
{"id": "live_multiple_690-164-6", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Jack Carson"]}}]}
{"id": "live_multiple_691-164-7", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Herbert Ross"], "genre": ["Family"], "cast": ["Nancy Parsons"]}}]}
{"id": "live_multiple_692-164-8", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Peter Strickland"], "genre": ["Horror"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_693-164-9", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Drama"], "cast": ["Utkarsh Ambudkar"]}}]}
{"id": "live_multiple_694-164-10", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Javier Bardem"]}}]}
{"id": "live_multiple_695-164-11", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Satoshi Kon"], "genre": ["Anime"], "cast": ["Akiko Kawase"]}}]}
{"id": "live_multiple_696-164-12", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Mystery"], "cast": ["Noah Gaynor"]}}]}
{"id": "live_multiple_697-164-13", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Quentin Tarantino"], "genre": ["Offbeat"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_698-164-14", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Offbeat"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_699-164-15", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Family"], "cast": ["Tzi Ma"]}}]}
{"id": "live_multiple_700-164-16", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Hari Sama"], "genre": ["", "dontcare"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_701-164-17", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Comedy"], "cast": ["Vanessa Przada"]}}]}
{"id": "live_multiple_702-164-18", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Katsunosuke Hori"]}}]}
{"id": "live_multiple_703-164-19", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Alex Kendrick"], "genre": ["Drama"], "cast": ["Aryn Wright-Thompson"]}}]}
{"id": "live_multiple_704-164-20", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Comedy"], "cast": ["Claudia Doumit"]}}]}
{"id": "live_multiple_705-164-21", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Nikita Mehta"]}}]}
{"id": "live_multiple_706-164-22", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Fantasy"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_707-164-23", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["", "dontcare"], "cast": ["Pete Davidson"]}}]}
{"id": "live_multiple_708-164-24", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Steven Spielberg"], "genre": ["Sci-fi"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_709-164-25", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Kirill Mikhanovsky"], "genre": ["Comedy-drama"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_710-164-26", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["", "dontcare"], "genre": ["Comedy"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_711-164-27", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["Jim Henson"], "genre": ["Fantasy"], "cast": ["Steve Whitmire"]}}]}
{"id": "live_multiple_712-164-28", "ground_truth": [{"Movies_3_FindMovies": {"directed_by": ["David Leitch"], "genre": ["Action"], "cast": ["", "dontcare"]}}]}
{"id": "live_multiple_713-165-0", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["London, UK"], "has_laundry_service": ["True"], "number_of_adults": ["", 1], "rating": ["", 3.0]}}]}
{"id": "live_multiple_714-165-1", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Phoenix, Arizona", "Phoenix, AZ"], "has_laundry_service": ["", "dontcare"], "number_of_adults": ["", 1], "rating": [4.1]}}]}
{"id": "live_multiple_715-165-2", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Austin, TX"], "has_laundry_service": ["True"], "number_of_adults": [2], "rating": [4.4]}}]}
{"id": "live_multiple_716-165-3", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Austin, TX"], "has_laundry_service": ["", "dontcare"], "number_of_adults": ["", 1], "rating": ["", 3.0]}}]}
{"id": "live_multiple_717-165-4", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Kuala Lumpur, MY"], "has_laundry_service": ["True"], "number_of_adults": ["", 1], "rating": ["", 3.0]}}]}
{"id": "live_multiple_718-165-5", "ground_truth": [{"Hotels_2_BookHouse": {"where_to": ["Austin, TX"], "number_of_adults": [4], "check_in_date": ["05/12/2023"], "check_out_date": ["05/18/2023"]}}]}
{"id": "live_multiple_719-165-6", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Anaheim, CA"], "has_laundry_service": ["", "dontcare"], "number_of_adults": ["", 1], "rating": ["", 3.0]}}]}
{"id": "live_multiple_720-165-7", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["New York, NY"], "has_laundry_service": ["", "dontcare"], "number_of_adults": [4], "rating": ["", 3.0]}}]}
{"id": "live_multiple_721-165-8", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Philadelphia, PA"], "has_laundry_service": ["True"], "number_of_adults": ["", 1], "rating": [4.1]}}]}
{"id": "live_multiple_722-165-9", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Los Angeles, CA"], "has_laundry_service": ["", "dontcare"], "number_of_adults": [4], "rating": [4.1]}}]}
{"id": "live_multiple_723-165-10", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Paris, FR"], "has_laundry_service": ["True"], "number_of_adults": ["", 1], "rating": ["", 3.0]}}]}
{"id": "live_multiple_724-165-11", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Los Angeles, CA"], "has_laundry_service": ["True"], "number_of_adults": ["", 1], "rating": [3.7]}}]}
{"id": "live_multiple_725-166-0", "ground_truth": [{"Services_1_FindProvider": {"city": ["Lafayette, LA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_726-166-1", "ground_truth": [{"Services_1_FindProvider": {"city": ["New York, NY"], "is_unisex": [true]}}]}
{"id": "live_multiple_727-166-2", "ground_truth": [{"Services_1_BookAppointment": {"stylist_name": ["John's Barber Shop"], "appointment_time": ["14:00"], "appointment_date": ["2023-03-15"]}}]}
{"id": "live_multiple_728-166-3", "ground_truth": [{"Services_1_FindProvider": {"city": ["Walnut Creek, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_729-167-0", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Denver, Colorado", "Denver, CO"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [1], "has_garage": ["", false], "in_unit_laundry": ["", "dontcare"]}}]}
{"id": "live_multiple_730-167-1", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Leandro, California", "San Leandro, CA"], "intent": ["buy"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": ["", false], "in_unit_laundry": ["True"]}}]}
{"id": "live_multiple_731-167-2", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Francisco, CA"], "intent": ["rent"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": [true], "in_unit_laundry": [true]}}]}
{"id": "live_multiple_732-167-3", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Seattle, WA"], "intent": ["buy"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": ["", false], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_733-167-4", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Francisco, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [1], "has_garage": [false], "in_unit_laundry": [true]}}]}
{"id": "live_multiple_734-167-5", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Los Angeles, CA"], "intent": ["buy"], "number_of_beds": [2], "number_of_baths": [2], "has_garage": [true], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_735-167-6", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Castro Valley, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [1], "has_garage": ["", false], "in_unit_laundry": [true]}}]}
{"id": "live_multiple_736-167-7", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Benicia, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [1], "has_garage": [true], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_737-167-8", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["San Francisco, CA"], "intent": ["buy"], "number_of_beds": [2], "number_of_baths": [3], "has_garage": [true], "in_unit_laundry": ["", false]}}]}
{"id": "live_multiple_738-168-0", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [75.5], "receiver": ["Peter"], "private_visibility": ["", false]}}]}
{"id": "live_multiple_739-168-1", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [50.0], "receiver": ["Alex"], "private_visibility": [true]}}]}
{"id": "live_multiple_740-168-2", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [250.0], "receiver": ["Margaret"], "private_visibility": [true]}}]}
{"id": "live_multiple_741-168-3", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["credit card"], "amount": [125.0], "receiver": ["Alex"], "private_visibility": [true]}}]}
{"id": "live_multiple_742-168-4", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [84.0], "receiver": ["Yumi"], "private_visibility": [true]}}]}
{"id": "live_multiple_743-168-5", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [50.0], "receiver": ["Rachel"], "private_visibility": [true]}}]}
{"id": "live_multiple_744-168-6", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [250.0], "receiver": ["Svetlana"], "private_visibility": [true]}}]}
{"id": "live_multiple_745-169-0", "ground_truth": [{"Media_3_FindMovies": {"genre": ["World"], "starring": ["Ula Tabari"]}}]}
{"id": "live_multiple_746-169-1", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Fantasy"], "starring": ["Emma Watson"]}}]}
{"id": "live_multiple_747-169-2", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Fantasy"], "starring": ["David Shaughnessy"]}}]}
{"id": "live_multiple_748-169-3", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Action"], "starring": ["Ani Sava"]}}]}
{"id": "live_multiple_749-169-4", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Offbeat"], "starring": ["", "Any"]}}]}
{"id": "live_multiple_750-169-5", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Any"], "starring": ["Madge Brindley"]}}]}
{"id": "live_multiple_751-169-6", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Comedy"], "starring": ["Jim Carrey"]}}]}
{"id": "live_multiple_752-169-7", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Mystery"], "starring": ["Monica Dolan"]}}]}
{"id": "live_multiple_753-169-8", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Animation"], "starring": ["Anri Katsu"]}}]}
{"id": "live_multiple_754-169-9", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Comedy"], "starring": ["Ana de Armas"]}}]}
{"id": "live_multiple_755-169-10", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Fantasy", "Any"], "starring": ["Peter Jackson"]}}]}
{"id": "live_multiple_756-169-11", "ground_truth": [{"Media_3_FindMovies": {"genre": ["horror"], "starring": ["Betsy Widhalm"]}}]}
{"id": "live_multiple_757-169-12", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Family"], "starring": ["Debbie McCann"]}}]}
{"id": "live_multiple_758-169-13", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Thriller"], "starring": ["Roberts Blossom"]}}]}
{"id": "live_multiple_759-169-14", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Comedy", "Animation"], "starring": ["", "Any"]}}]}
{"id": "live_multiple_760-169-15", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Thriller"], "starring": ["Michelle Caspar"]}}]}
{"id": "live_multiple_761-169-16", "ground_truth": [{"Media_3_FindMovies": {"genre": ["Horror"], "starring": ["Dean Norris"]}}]}
{"id": "live_multiple_762-170-0", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Portland, OR"], "to_city": ["Vancouver, BC"], "departure_date": ["2023-03-07"], "num_passengers": [2], "category": ["", "direct"]}}]}
{"id": "live_multiple_763-170-1", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Los Angeles, CA"], "to_city": ["San Diego, CA"], "departure_date": ["2023-10-01"], "num_passengers": [2], "category": ["", "direct"]}}]}
{"id": "live_multiple_764-170-2", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["San Francisco, CA"], "to_city": ["Los Angeles, CA"], "departure_date": ["2023-04-22"], "num_passengers": ["", 1], "category": ["", "direct"]}}]}
{"id": "live_multiple_765-170-3", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Philadelphia, PA"], "to_city": ["New York, NY"], "departure_date": ["2023-04-23"], "num_passengers": [4], "category": ["", "direct"]}}]}
{"id": "live_multiple_766-170-4", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Austin, TX"], "to_city": ["Dallas, TX"], "departure_date": ["2023-03-13"], "num_passengers": [3], "category": ["", "direct"]}}]}
{"id": "live_multiple_767-171-0", "ground_truth": [{"Services_1_FindProvider": {"city": ["New York, NY"], "is_unisex": [true]}}]}
{"id": "live_multiple_768-171-1", "ground_truth": [{"Services_1_FindProvider": {"city": ["Emeryville, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_769-171-2", "ground_truth": [{"Services_1_FindProvider": {"city": ["Mill Valley, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_770-171-3", "ground_truth": [{"Services_1_FindProvider": {"city": ["Burlingame, CA"], "is_unisex": [true]}}]}
{"id": "live_multiple_771-171-4", "ground_truth": [{"Services_1_FindProvider": {"city": ["New York, NY"], "is_unisex": [true]}}]}
{"id": "live_multiple_772-171-5", "ground_truth": [{"Services_1_FindProvider": {"city": ["Fremont, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_773-171-6", "ground_truth": [{"Services_1_FindProvider": {"city": ["San Ramon, CA"], "is_unisex": ["", false]}}]}
{"id": "live_multiple_774-171-7", "ground_truth": [{"Services_1_FindProvider": {"city": ["Morgan Hill, CA", "Morgan Hill"], "is_unisex": [true]}}]}
{"id": "live_multiple_775-172-0", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Las Vegas, NV"], "start_date": ["2023-10-01"], "end_date": ["2023-10-12"], "pickup_time": ["10:00"], "car_type": ["SUV"]}}]}
{"id": "live_multiple_776-172-1", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-03-01"], "end_date": ["2023-03-07"], "pickup_time": ["10:00"], "car_type": ["SUV"]}}]}
{"id": "live_multiple_777-172-2", "ground_truth": [{"RentalCars_3_GetCarsAvailable": {"city": ["Los Angeles, CA"], "start_date": ["2023-04-15"], "end_date": ["2023-04-20"], "pickup_time": ["10:00"], "car_type": ["", "dontcare"]}}]}
{"id": "live_multiple_778-173-0", "ground_truth": [{"Services_4_FindProvider": {"city": ["Gilroy, CA"], "type": ["Family Counselor"]}}]}
{"id": "live_multiple_779-173-1", "ground_truth": [{"Services_4_FindProvider": {"city": ["San Jose, CA"], "type": ["Psychologist"]}}]}
{"id": "live_multiple_780-173-2", "ground_truth": [{"Services_4_FindProvider": {"city": ["New York, NY"], "type": ["Family Counselor"]}}]}
{"id": "live_multiple_781-173-3", "ground_truth": [{"Services_4_FindProvider": {"city": ["San Francisco, CA"], "type": ["Psychologist"]}}]}
{"id": "live_multiple_782-173-4", "ground_truth": [{"Services_4_FindProvider": {"city": ["Sausalito, CA"], "type": ["Psychologist"]}}]}
{"id": "live_multiple_783-173-5", "ground_truth": [{"Services_4_FindProvider": {"city": ["Berkeley, CA"], "type": ["Psychologist", "Family Counselor"]}}]}
{"id": "live_multiple_784-173-6", "ground_truth": [{"Services_4_FindProvider": {"city": ["Berkeley, CA"], "type": ["Psychologist"]}}]}
{"id": "live_multiple_785-173-7", "ground_truth": [{"Services_4_FindProvider": {"city": ["Sunnyvale, CA"], "type": ["Psychologist"]}}]}
{"id": "live_multiple_786-174-0", "ground_truth": [{"Homes_2_ScheduleVisit": {"property_name": ["Riverdale Apartments"], "visit_date": ["2023-04-15"]}}]}
{"id": "live_multiple_787-174-1", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Berkeley, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [1], "has_garage": ["", null], "in_unit_laundry": [true]}}]}
{"id": "live_multiple_788-174-2", "ground_truth": [{"Homes_2_ScheduleVisit": {"property_name": ["Beach Park Apartments"], "visit_date": ["2023-10-01"]}}]}
{"id": "live_multiple_789-174-3", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Berkeley, CA"], "intent": ["rent"], "number_of_beds": [2], "number_of_baths": [1], "has_garage": ["", null], "in_unit_laundry": ["", null]}}]}
{"id": "live_multiple_790-174-4", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Berkeley, CA"], "intent": ["rent"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": ["", null], "in_unit_laundry": ["", null]}}]}
{"id": "live_multiple_791-174-5", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Walnut Creek, CA"], "intent": ["buy"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": [true], "in_unit_laundry": ["", null]}}]}
{"id": "live_multiple_792-174-6", "ground_truth": [{"Homes_2_FindHomeByArea": {"area": ["Berkeley, CA"], "intent": ["rent"], "number_of_beds": [3], "number_of_baths": [2], "has_garage": [true], "in_unit_laundry": [true]}}]}
{"id": "live_multiple_793-174-7", "ground_truth": [{"Homes_2_ScheduleVisit": {"property_name": ["Northridge Apartments"], "visit_date": ["2023-03-14"]}}]}
{"id": "live_multiple_794-175-0", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Pinole, CA"], "date": ["2023-03-21"]}}]}
{"id": "live_multiple_795-175-1", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Tomales, CA"], "date": ["2023-03-11"]}}]}
{"id": "live_multiple_796-175-2", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Sunol, CA"], "date": ["", "2019-03-01"]}}]}
{"id": "live_multiple_797-175-3", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Phoenix, AZ"], "date": ["2023-03-11"]}}]}
{"id": "live_multiple_798-175-4", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Long Beach, CA"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_799-175-5", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Lagunitas, CA"], "date": ["2023-04-15"]}}]}
{"id": "live_multiple_800-175-6", "ground_truth": [{"Weather_1_GetWeather": {"city": ["San Pablo, CA"], "date": ["2023-04-10"]}}]}
{"id": "live_multiple_801-175-7", "ground_truth": [{"Weather_1_GetWeather": {"city": ["New Delhi, IN"], "date": ["", "2019-03-01"]}}]}
{"id": "live_multiple_802-175-8", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Santa Rosa, CA"], "date": ["2023-04-09"]}}]}
{"id": "live_multiple_803-175-9", "ground_truth": [{"Weather_1_GetWeather": {"city": ["San Francisco, CA"], "date": ["2023-03-01"]}}]}
{"id": "live_multiple_804-175-10", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Petaluma, CA"], "date": ["2023-10-01"]}}]}
{"id": "live_multiple_805-175-11", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Vancouver, BC"], "date": ["2023-03-02"]}}]}
{"id": "live_multiple_806-175-12", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Nairobi, KE"], "date": ["2023-04-20"]}}]}
{"id": "live_multiple_807-175-13", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Larkspur, CA"], "date": ["2023-03-10"]}}]}
{"id": "live_multiple_808-175-14", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Antioch, CA"], "date": ["2023-10-01"]}}]}
{"id": "live_multiple_809-176-0", "ground_truth": [{"Alarm_1_AddAlarm": {"new_alarm_time": ["17:00"], "new_alarm_name": ["Grocery run"]}}]}
{"id": "live_multiple_810-176-1", "ground_truth": [{"Alarm_1_GetAlarms": {"user_id": [789], "include_disabled": ["", false], "sort_order": ["", "ascending"]}}]}
{"id": "live_multiple_811-176-2", "ground_truth": [{"Alarm_1_GetAlarms": {"user_id": [12345], "include_disabled": ["", false], "sort_order": ["", "ascending"]}}]}
{"id": "live_multiple_812-176-3", "ground_truth": [{"Alarm_1_AddAlarm": {"new_alarm_time": ["17:00"], "new_alarm_name": ["Music practice"]}}]}
{"id": "live_multiple_813-176-4", "ground_truth": [{"Alarm_1_AddAlarm": {"new_alarm_time": ["16:30"], "new_alarm_name": ["Leave for home"]}}]}
{"id": "live_multiple_814-176-5", "ground_truth": [{"Alarm_1_AddAlarm": {"new_alarm_time": ["17:15"], "new_alarm_name": ["", "New alarm"]}}]}
{"id": "live_multiple_815-176-6", "ground_truth": [{"Alarm_1_AddAlarm": {"new_alarm_time": ["07:30"], "new_alarm_name": ["Morning workout"]}}]}
{"id": "live_multiple_816-176-7", "ground_truth": [{"Alarm_1_AddAlarm": {"new_alarm_time": ["15:30"], "new_alarm_name": ["Cleaning"]}}]}
{"id": "live_multiple_817-177-0", "ground_truth": [{"Events_3_BuyEventTickets": {"event_name": ["Anthony Green"], "number_of_tickets": [2], "date": ["2023-03-10"], "city": ["Seattle, WA"]}}]}
{"id": "live_multiple_818-177-1", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["", null]}}]}
{"id": "live_multiple_819-177-2", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Los Angeles, CA"], "date": ["2023-07-20"]}}]}
{"id": "live_multiple_820-177-3", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-03-04"]}}]}
{"id": "live_multiple_821-177-4", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Seattle, WA"], "date": ["", null]}}]}
{"id": "live_multiple_822-177-5", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Seattle, WA"], "date": ["2023-03-12"]}}]}
{"id": "live_multiple_823-177-6", "ground_truth": [{"Events_3_BuyEventTickets": {"event_name": ["Brockhampton"], "number_of_tickets": [4], "date": ["2023-03-13"], "city": ["Berkeley, CA"]}}]}
{"id": "live_multiple_824-177-7", "ground_truth": [{"Events_3_BuyEventTickets": {"event_name": ["Read The Tempest"], "number_of_tickets": [3], "date": ["2023-03-02"], "city": ["Princeton, NJ"]}}]}
{"id": "live_multiple_825-178-0", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Isolation"], "genre": ["Funk"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_826-178-1", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["", "dontcare"], "year": ["2015"]}}]}
{"id": "live_multiple_827-178-2", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["", "dontcare"], "year": ["2021"]}}]}
{"id": "live_multiple_828-178-3", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Get Scared"], "album": ["", "dontcare"], "genre": ["Rock"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_829-178-4", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Bohemian Rhapsody"], "artist": ["", "any"], "device": ["Kitchen"], "album": ["", "any"]}}]}
{"id": "live_multiple_830-178-5", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Sabaton"], "album": ["", "dontcare"], "genre": ["Metal"], "year": ["2019"]}}]}
{"id": "live_multiple_831-178-6", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Taylor Swift"], "album": ["", "dontcare"], "genre": ["Pop"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_832-178-7", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Pop"], "year": ["2021"]}}]}
{"id": "live_multiple_833-178-8", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Captured"], "artist": ["Spice"], "device": ["", "Living room"], "album": ["Captured"]}}]}
{"id": "live_multiple_834-178-9", "ground_truth": [{"Music_3_PlayMedia": {"track": ["", "Borbena"], "artist": ["Teodora"], "device": ["Kitchen"], "album": ["Borbena"]}}]}
{"id": "live_multiple_835-178-10", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Reincarnation"], "genre": ["Reggaeton"], "year": ["2019"]}}]}
{"id": "live_multiple_836-178-11", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Love"], "artist": ["", "any"], "device": ["Kitchen"], "album": ["Sucker Punch"]}}]}
{"id": "live_multiple_837-178-12", "ground_truth": [{"Music_3_PlayMedia": {"track": ["popular song"], "artist": ["Phan Dinh Tung"], "device": ["Kitchen"], "album": ["Hat Nhan"]}}]}
{"id": "live_multiple_838-178-13", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Narrated For You"], "artist": ["Alec Benjamin"], "device": ["", "Living room"], "album": ["", "Narrated For You", "any"]}}]}
{"id": "live_multiple_839-178-14", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["", "dontcare"], "genre": ["Country"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_840-178-15", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Gotta Be Me"], "genre": ["Country"], "year": ["2019"]}}]}
{"id": "live_multiple_841-178-16", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Run"], "genre": ["Electropop"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_842-178-17", "ground_truth": [{"Music_3_PlayMedia": {"track": ["Konshens"], "artist": ["", "any"], "device": ["Patio"], "album": ["", "any"]}}]}
{"id": "live_multiple_843-178-18", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Sabaton"], "album": ["The Great War"], "genre": ["Metal"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_844-178-19", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["", "dontcare"], "album": ["Cry Pretty"], "genre": ["", "dontcare"], "year": ["2018"]}}]}
{"id": "live_multiple_845-178-20", "ground_truth": [{"Music_3_LookupMusic": {"artist": ["Sara Evans"], "album": ["Stronger"], "genre": ["", "dontcare"], "year": ["", "dontcare"]}}]}
{"id": "live_multiple_846-179-0", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Italian"], "location": ["San Francisco, CA"], "price_range": ["moderate"], "has_vegetarian_options": ["", false], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_847-179-1", "ground_truth": [{"Restaurants_2_ReserveRestaurant": {"restaurant_name": ["Balthazar"], "location": ["New York, NY"], "time": ["20:00"], "number_of_seats": [4], "date": ["2023-10-06"]}}]}
{"id": "live_multiple_848-179-2", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Italian"], "location": ["New York, NY"], "price_range": ["", "dontcare"], "has_vegetarian_options": [true], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_849-179-3", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Indian"], "location": ["Sunnyvale, CA"], "price_range": ["cheap"], "has_vegetarian_options": ["", false], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_850-179-4", "ground_truth": [{"Restaurants_2_ReserveRestaurant": {"restaurant_name": ["The French Laundry"], "location": ["Yountville, CA"], "time": ["13:30"], "number_of_seats": [4], "date": ["2023-03-14"]}}]}
{"id": "live_multiple_851-179-5", "ground_truth": [{"Restaurants_2_FindRestaurants": {"category": ["Japanese"], "location": ["San Francisco, CA"], "price_range": ["", "dontcare"], "has_vegetarian_options": ["", false], "has_seating_outdoors": ["", false]}}]}
{"id": "live_multiple_852-180-0", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["San Francisco, CA"], "to_city": ["Los Angeles, CA"], "departure_date": ["04/25/2023"], "num_passengers": ["", 1], "category": ["", "direct"]}}]}
{"id": "live_multiple_853-180-1", "ground_truth": [{"Buses_3_BuyBusTicket": {"from_city": ["San Francisco, CA"], "to_city": ["Los Angeles, CA"], "departure_date": ["2023-03-07"], "departure_time": ["14:00"], "num_passengers": [2], "additional_luggage": ["", false]}}]}
{"id": "live_multiple_854-180-2", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Philadelphia, PA"], "to_city": ["New York City, NY"], "departure_date": ["03/14/2023"], "num_passengers": ["", 1], "category": ["", "direct"]}}]}
{"id": "live_multiple_855-180-3", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Fresno, CA"], "to_city": ["Los Angeles, CA"], "departure_date": ["03/10/2023"], "num_passengers": ["", 1], "category": ["", "direct"]}}]}
{"id": "live_multiple_856-180-4", "ground_truth": [{"Buses_3_BuyBusTicket": {"from_city": ["Berkeley, CA"], "to_city": ["Portland, OR"], "departure_date": ["2023-05-15"], "departure_time": ["09:00"], "num_passengers": [2], "additional_luggage": [true]}}]}
{"id": "live_multiple_857-180-5", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["San Francisco, CA"], "to_city": ["Los Angeles, CA"], "departure_date": ["03/07/2023"], "num_passengers": ["", 1], "category": ["one-stop"]}}]}
{"id": "live_multiple_858-180-6", "ground_truth": [{"Buses_3_BuyBusTicket": {"from_city": ["San Francisco, CA"], "to_city": ["Los Angeles, CA"], "departure_date": ["2023-05-15"], "departure_time": ["14:00"], "num_passengers": [4], "additional_luggage": [true]}}]}
{"id": "live_multiple_859-181-0", "ground_truth": [{"Trains_1_GetTrainTickets": {"_from": ["New York, NY"], "to": ["Washington, DC"], "date_of_journey": ["10/11/2023"], "journey_start_time": ["09:00"], "number_of_adults": [1, ""], "trip_protection": [true], "_class": ["Business"]}}]}
{"id": "live_multiple_860-181-1", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["San Francisco, CA"], "to": ["Los Angeles, CA"], "date_of_journey": ["2023-04-15"]}}]}
{"id": "live_multiple_861-181-2", "ground_truth": [{"Trains_1_GetTrainTickets": {"_from": ["Sacramento, CA"], "to": ["Portland, OR"], "date_of_journey": ["04/22/2023"], "journey_start_time": ["10:00"], "number_of_adults": [1, ""], "trip_protection": [false, ""], "_class": ["Value", ""]}}]}
{"id": "live_multiple_862-181-3", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["New York, NY"], "to": ["Los Angeles, CA"], "date_of_journey": ["05/15/2023"], "journey_start_time": ["09:00"]}}]}
{"id": "live_multiple_863-182-0", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Orinda, CA"], "theater_name": ["Orinda Theatre"], "genre": ["", "dontcare"], "show_type": ["regular"]}}]}
{"id": "live_multiple_864-182-1", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Los Angeles, CA"], "theater_name": ["", "dontcare"], "genre": ["", "dontcare"], "show_type": ["regular"]}}]}
{"id": "live_multiple_865-182-2", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Berkeley, CA"], "theater_name": ["", "dontcare"], "genre": ["Mystery"], "show_type": ["imax"]}}]}
{"id": "live_multiple_866-182-3", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Livermore, CA"], "theater_name": ["Vine Cinema"], "genre": ["Drama"], "show_type": ["regular"]}}]}
{"id": "live_multiple_867-182-4", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Berkeley, CA"], "theater_name": ["", "dontcare"], "genre": ["Supernatural"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_868-182-5", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Vallejo, CA"], "theater_name": ["", "dontcare"], "genre": ["", "dontcare"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_869-182-6", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Rafael, CA"], "theater_name": ["", "dontcare"], "genre": ["", "dontcare"], "show_type": ["imax"]}}]}
{"id": "live_multiple_870-182-7", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Fremont, CA"], "theater_name": ["Century at Pacific Commons and XD"], "genre": ["Sci-fi", "Sci-fi,Action"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_871-182-8", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Berkeley, CA"], "theater_name": ["Shattuck Cinemas"], "genre": ["Play"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_872-182-9", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Healdsburg, CA"], "theater_name": ["", "dontcare"], "genre": ["Drama"], "show_type": ["regular"]}}]}
{"id": "live_multiple_873-182-10", "ground_truth": [{"Movies_1_FindMovies": {"location": ["San Francisco, CA"], "theater_name": ["", "dontcare"], "genre": ["", "dontcare"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_874-182-11", "ground_truth": [{"Movies_1_FindMovies": {"location": ["Sebastopol, CA"], "theater_name": ["", "dontcare"], "genre": ["Supernatural"], "show_type": ["", "dontcare"]}}]}
{"id": "live_multiple_875-183-0", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["San Francisco, CA"], "free_entry": ["", "dontcare"], "category": ["", "dontcare"], "good_for_kids": ["", "dontcare"]}}]}
{"id": "live_multiple_876-183-1", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, FR"], "free_entry": ["True"], "category": ["Historical Landmark"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_877-183-2", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, FR"], "free_entry": ["True"], "category": ["Museum"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_878-183-3", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["San Francisco, CA"], "free_entry": ["True"], "category": ["Nature Preserve"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_879-183-4", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["San Francisco, CA"], "free_entry": ["True"], "category": ["Museum"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_880-183-5", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["Paris, FR"], "free_entry": ["True"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_881-183-6", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["San Francisco, CA"], "free_entry": ["True"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_882-183-7", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["San Francisco, CA"], "free_entry": ["True"], "category": ["Museum"], "good_for_kids": ["", "dontcare"]}}]}
{"id": "live_multiple_883-184-0", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["New York, NY"], "to_city": ["Boston, MA"], "departure_date": ["2023-04-20"], "num_passengers": ["", 1], "category": ["", "direct"]}}]}
{"id": "live_multiple_884-184-1", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Anaheim, CA"], "to_city": ["Las Vegas, NV"], "departure_date": ["2023-10-06"], "num_passengers": ["", 1], "category": ["one-stop"]}}]}
{"id": "live_multiple_885-184-2", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["New York, NY"], "to_city": ["Boston, MA"], "departure_date": ["2023-05-15"], "num_passengers": ["", 1], "category": ["", "direct"]}}]}
{"id": "live_multiple_886-184-3", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["San Francisco, CA"], "to_city": ["Fresno, CA"], "departure_date": ["2023-03-04"], "num_passengers": [2], "category": ["", "direct"]}}]}
{"id": "live_multiple_887-184-4", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["San Diego, CA"], "to_city": ["Los Angeles, CA"], "departure_date": ["2023-06-15"], "num_passengers": ["", 4], "category": ["", "direct"]}}]}
{"id": "live_multiple_888-184-5", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Los Angeles, CA"], "to_city": ["San Diego, CA"], "departure_date": ["2023-10-01"], "num_passengers": ["", 1], "category": ["", "direct"]}}]}
{"id": "live_multiple_889-184-6", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Miami, FL"], "to_city": ["Atlanta, GA"], "departure_date": ["2023-04-22"], "num_passengers": [2], "category": ["", "direct"]}}]}
{"id": "live_multiple_890-185-0", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["Mahmoud"], "amount": [43.0], "private_visibility": ["", false]}}]}
{"id": "live_multiple_891-185-1", "ground_truth": [{"Payment_1_RequestPayment": {"receiver": ["John Doe"], "amount": [250.0], "private_visibility": [true]}}]}
{"id": "live_multiple_892-185-2", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [100.0], "receiver": ["Mary"], "private_visibility": [true]}}]}
{"id": "live_multiple_893-185-3", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [90.0], "receiver": ["Alice"], "private_visibility": ["", false]}}]}
{"id": "live_multiple_894-185-4", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [150.0], "receiver": ["Alice"], "private_visibility": [true]}}]}
{"id": "live_multiple_895-185-5", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["credit card"], "amount": [50.0], "receiver": ["Jerry"], "private_visibility": [true]}}]}
{"id": "live_multiple_896-185-6", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [50.0], "receiver": ["Margaret"], "private_visibility": [true]}}]}
{"id": "live_multiple_897-185-7", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [122.0], "receiver": ["John"], "private_visibility": ["", false]}}]}
{"id": "live_multiple_898-185-8", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [50.0], "receiver": ["Jamie"], "private_visibility": [true]}}]}
{"id": "live_multiple_899-185-9", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [50.0], "receiver": ["Emma"], "private_visibility": [true]}}]}
{"id": "live_multiple_900-185-10", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [200.0], "receiver": ["Alice"], "private_visibility": [true]}}]}
{"id": "live_multiple_901-185-11", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["app balance"], "amount": [31.0], "receiver": ["Svetlana"], "private_visibility": ["", false]}}]}
{"id": "live_multiple_902-185-12", "ground_truth": [{"Payment_1_MakePayment": {"payment_method": ["debit card"], "amount": [25.0], "receiver": ["Alex"], "private_visibility": [true]}}]}
{"id": "live_multiple_903-186-0", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["Phoenix, AZ"], "to": ["Los Angeles, CA"], "date_of_journey": ["04/25/2023"]}}]}
{"id": "live_multiple_904-186-1", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["New York, NY"], "to": ["Los Angeles, CA"], "date_of_journey": ["03/04/2023"]}}]}
{"id": "live_multiple_905-186-2", "ground_truth": [{"Trains_1_GetTrainTickets": {"_from": ["Portland, OR"], "to": ["Seattle, WA"], "date_of_journey": ["2023-05-15"], "journey_start_time": ["dontcare"], "number_of_adults": [4, ""], "trip_protection": [true], "_class": ["Business"]}}]}
{"id": "live_multiple_906-186-3", "ground_truth": [{"Trains_1_GetTrainTickets": {"_from": ["New York, NY"], "to": ["Los Angeles, CA"], "date_of_journey": ["2023-05-05"], "journey_start_time": ["15:00"], "number_of_adults": [2, ""], "trip_protection": [true], "_class": ["Business"]}}]}
{"id": "live_multiple_907-186-4", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["Philadelphia, PA"], "to": ["New York, NY"], "date_of_journey": ["03/08/2023"]}}]}
{"id": "live_multiple_908-187-0", "ground_truth": [{"Travel_1_FindAttractions": {"location": ["New York, NY"], "free_entry": ["True"], "category": ["", "dontcare"], "good_for_kids": ["True"]}}]}
{"id": "live_multiple_909-188-0", "ground_truth": [{"youtube.check_videos": {"title": ["The Secrets of the Taj Mahal"], "publisher": ["TravelHistory"], "tags": [["Taj Mahal", "India", "architecture"]]}}]}
{"id": "live_multiple_910-189-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_911-190-0", "ground_truth": [{"view_service_provider_profile": {"service_id": [1]}}]}
{"id": "live_multiple_912-191-0", "ground_truth": [{"get_service_providers": {"avg_rating": [4.0], "province_id": [1], "district_name": ["Bang Khen"], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [2], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_913-191-1", "ground_truth": [{"get_service_providers": {"avg_rating": [4.9], "province_id": [1], "district_name": ["Bangna District", "Bangna"], "sub_district_name": ["", null], "start_available_date": ["2024-03-19 12:00:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_914-191-2", "ground_truth": [{"view_service_provider_profile": {"professional_id": [9974]}}]}
{"id": "live_multiple_915-191-3", "ground_truth": [{"get_service_providers": {"avg_rating": [5.0], "province_id": [1], "district_name": ["Lat Phrao"], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": [true], "is_subscription": ["", false], "service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_916-191-4", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": ["", null], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["2024-03-19 12:00:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_917-191-5", "ground_truth": [{"view_service_provider_profile": {"professional_id": [7434]}}]}
{"id": "live_multiple_918-191-6", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [2], "district_name": ["Bang Kruai"], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [2], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_919-191-7", "ground_truth": [{"get_service_providers": {"avg_rating": [4.0, 4.5], "province_id": ["", null], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["2024-03-19 12:00:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_920-191-8", "ground_truth": [{"get_service_providers": {"avg_rating": [4.5], "province_id": ["", null], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["2024-02-23 10:30:00"], "end_available_date": ["2024-02-23 19:00:00"], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": [true], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_921-191-9", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [2], "district_name": ["Mueang", "Mueang District"], "sub_district_name": ["", null], "start_available_date": ["2024-03-19 00:00:00"], "end_available_date": ["2024-03-19 23:59:59"], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_922-191-10", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": ["", null], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": [25], "max_age": [35], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": [2], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_923-191-11", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [1], "district_name": ["Chatuchak"], "sub_district_name": ["", null], "start_available_date": ["2024-03-19 13:00:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [2], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_924-191-12", "ground_truth": [{"get_service_providers": {"avg_rating": [4.0], "province_id": [1], "district_name": ["Sukhumvit"], "sub_district_name": ["", null], "start_available_date": ["2024-03-04 15:00:00"], "end_available_date": ["", null], "min_age": [25], "max_age": [35], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_925-191-13", "ground_truth": [{"view_service_provider_profile": {"professional_id": [28]}}]}
{"id": "live_multiple_926-191-14", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [2], "district_name": ["Hang Dong"], "sub_district_name": ["", null], "start_available_date": ["2024-03-25 14:30:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_927-191-15", "ground_truth": [{"get_service_providers": {"avg_rating": [4.5], "province_id": [1], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": [true], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_928-191-16", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": ["", null], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": [true], "service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": [2], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_929-191-17", "ground_truth": [{"get_service_providers": {"avg_rating": [4.7], "province_id": [2], "district_name": ["Muang"], "sub_district_name": ["", null], "start_available_date": ["2024-03-19 09:30:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_930-191-18", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": ["", null], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [35], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_931-191-19", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": ["", null], "district_name": ["", null], "sub_district_name": ["Phra Khanong"], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": [true], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_932-191-20", "ground_truth": [{"view_service_provider_profile": {"professional_id": [4724]}}]}
{"id": "live_multiple_933-191-21", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [1], "district_name": ["Ratchathewi"], "sub_district_name": ["", null], "start_available_date": ["2024-03-20 11:00:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "available_for_pet": [true], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_934-191-22", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [1], "district_name": ["Chatuchak"], "sub_district_name": ["", null], "start_available_date": ["2024-03-22 00:00:00"], "end_available_date": ["2024-03-22 23:59:59"], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": [true], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_935-192-0", "ground_truth": [{"set_countdown": {"duration": ["01:00"], "purpose": ["meeting"]}}]}
{"id": "live_multiple_936-192-1", "ground_truth": [{"set_alarm": {"alarm_time": ["2023-04-02 07:30:00"], "purpose": ["wake me up for a meeting"]}}]}
{"id": "live_multiple_937-193-0", "ground_truth": [{"acl_api.AclApi.retrieve_projects": {"uuid": ["123e4567-e89b-12d3-a456-************"], "excludeInactive": ["", false], "onlyRoot": ["", false]}}]}
{"id": "live_multiple_938-194-0", "ground_truth": [{"play_spotify_song": {"query": ["track:Shape of You artist:Ed Sheeran"], "autoplay": [true], "volume": [70]}}]}
{"id": "live_multiple_939-194-1", "ground_truth": [{"set_alarm": {"alarm_time": ["23:00"], "purpose": ["brush my teeth"]}}]}
{"id": "live_multiple_940-194-2", "ground_truth": [{"play_spotify_song": {"query": ["baby shark", "Baby Shark"], "autoplay": ["", false], "volume": ["", 50]}}]}
{"id": "live_multiple_941-195-0", "ground_truth": [{"play_song": {"query": ["track:Baby Shark"]}}]}
{"id": "live_multiple_942-196-0", "ground_truth": [{"play_song": {"query": ["baby Shark"], "volume": ["", 70], "shuffle": ["", false]}}]}
{"id": "live_multiple_943-196-1", "ground_truth": [{"play_song": {"query": ["With You"], "volume": ["", 70], "shuffle": ["", false]}}]}
{"id": "live_multiple_944-196-2", "ground_truth": [{"set_volume": {"volume": [70]}}]}
{"id": "live_multiple_945-196-3", "ground_truth": [{"play_song": {"query": ["Wo noor"], "volume": [50], "shuffle": ["", false]}}]}
{"id": "live_multiple_946-196-4", "ground_truth": [{"play_song": {"query": ["Thriller by Michael Jackson", "Thriller"], "volume": [100], "shuffle": ["", false]}}]}
{"id": "live_multiple_947-197-0", "ground_truth": [{"set_countdown": {"duration": ["5 minutes"], "purpose": ["", "General reminder"]}}]}
{"id": "live_multiple_948-198-0", "ground_truth": [{"set_alarm": {"alarm_time": ["23:00"], "purpose": ["text Raj"]}}]}
{"id": "live_multiple_949-199-0", "ground_truth": [{"play_song": {"query": ["David Guetta"], "shuffle": ["", false], "repeat": ["", false], "volume": ["", 70]}}]}
{"id": "live_multiple_950-199-1", "ground_truth": [{"set_alarm": {"alarm_time": ["23:00"], "purpose": ["", "General reminder"]}}]}
{"id": "live_multiple_951-200-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_952-201-0", "ground_truth": [{"uber.eat.order": {"restaurant": ["McDonald's"], "items": [["fries"]], "quantities": [[5]], "notes": [""], "delivery_option": ["", "standard"]}}]}
{"id": "live_multiple_953-201-1", "ground_truth": [{"flight.status.check": {"flight_identifier": ["IND5463"], "passenger_name": ["karan"], "ticket_number": ["IND5463"]}}]}
{"id": "live_multiple_954-202-0", "ground_truth": [{"flight.status.check": {"airlines": ["Delta"], "person_name": ["Karan Sharma"], "ticket_number": ["IND4567"]}}]}
{"id": "live_multiple_955-202-1", "ground_truth": [{"uber.eat.order": {"restaurant": ["mc donalds", "McDonald's"], "items": [["fries"]], "quantities": [[5]]}}]}
{"id": "live_multiple_956-203-0", "ground_truth": [{"partner.mandates": {"status": ["", "all"]}}]}
{"id": "live_multiple_957-203-1", "ground_truth": [{"user.mandates": {"name": ["John Doe"], "status": ["", "active"]}}]}
{"id": "live_multiple_958-204-0", "ground_truth": [{"partner.sips": {"status": ["active"]}}]}
{"id": "live_multiple_959-204-1", "ground_truth": [{"partner.sips": {"status": ["active"]}}]}
{"id": "live_multiple_960-205-0", "ground_truth": [{"get_current_weather": {"location": ["New York, NY"], "unit": ["", "fahrenheit"]}}]}
{"id": "live_multiple_961-205-1", "ground_truth": [{"web_search": {"query": ["where is beijing"], "results_limit": ["", 10], "language": ["", "en"], "safe_search": ["", true]}}]}
{"id": "live_multiple_962-205-2", "ground_truth": [{"web_search": {"query": ["where is columbia university"], "results_limit": ["", 10], "language": ["", "en"], "safe_search": ["", true]}}]}
{"id": "live_multiple_963-206-0", "ground_truth": [{"GET_PARCEL_STATE": {"parcelTrackingId": ["TX*********0"], "includeHistory": ["", false]}}]}
{"id": "live_multiple_964-207-0", "ground_truth": [{"transfer_to_human_agent": {"question": ["Can you tell me what is the minimum package arrival time?"]}}]}
{"id": "live_multiple_965-207-1", "ground_truth": [{"transfer_to_human_agent": {"statement": ["I've submitted a complaint and would like to discuss its status with a human agent."]}}]}
{"id": "live_multiple_966-208-0", "ground_truth": [{"handover_to_agent": {"statement": ["I've submitted a complaint earlier, and I would like to check its current status with a human agent, please."], "urgency": ["medium"], "language": ["", "en"], "context": [""]}}]}
{"id": "live_multiple_967-209-0", "ground_truth": [{"handover_to_human_agent": {"statement": ["I'd like to speak with a human agent about the status of my complaint, please. It's quite urgent."], "urgency_level": ["high"], "user_id": ["", "anonymous"]}}]}
{"id": "live_multiple_968-210-0", "ground_truth": [{"uber.ride": {"loc": ["2020 Addison Street, Berkeley, CA"], "type": ["plus"], "time": [10]}}]}
{"id": "live_multiple_969-210-1", "ground_truth": [{"uber.eat.order": {"restaurant": ["McDonald's, 123 Fastfood Lane, Chicago, IL"], "items": [["burger", "chicken wing"]], "quantities": [[5, 6]]}}]}
{"id": "live_multiple_970-211-0", "ground_truth": [{"fmp.income_statement": {"symbol": ["APPL"], "period": ["annual"], "cik": ["", null], "datatype": ["", "json"], "limit": [2]}}]}
{"id": "live_multiple_971-212-0", "ground_truth": [{"get_service_providers": {"avg_rating": [4.0], "province_id": [1], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": [true], "is_cleaning_office": ["", false], "service_ids": ["", []]}}]}
{"id": "live_multiple_972-212-1", "ground_truth": [{"get_service_providers": {"avg_rating": [5.0], "province_id": [1], "district_name": ["Lat Phrao"], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": [true], "is_subscription": ["", false], "service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false], "service_ids": ["", []]}}]}
{"id": "live_multiple_973-213-0", "ground_truth": [{"get_service_providers": {"avg_rating": ["", 0.0], "province_id": [1], "district_name": ["", "All"], "sub_district_name": ["", "All"], "start_available_date": ["", "now"], "end_available_date": ["", "indefinite"], "min_age": ["", 18], "max_age": ["", 65], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", 0], "service_ids": [[2]]}}]}
{"id": "live_multiple_974-214-0", "ground_truth": [{"get_service_providers": {"avg_rating": [4.5], "province_id": [1], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "available_for_pet": ["", false], "professional_group_id": ["", 0], "job_qty": ["", null]}}]}
{"id": "live_multiple_975-214-1", "ground_truth": [{"get_service_providers": {"avg_rating": [4.0], "province_id": [1], "district_name": ["", null], "sub_district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [2, 1], "available_for_pet": ["", false], "professional_group_id": ["", 0], "job_qty": ["", null]}}]}
{"id": "live_multiple_976-215-0", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [3], "district_name": ["Bang Kruai District"], "start_available_date": ["2024-03-20 09:00:00"], "end_available_date": ["2024-03-20 12:00:00"], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "extra_service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_977-215-1", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [1], "district_name": ["Ekthai"], "start_available_date": ["2024-03-22 11:00:00"], "end_available_date": ["2024-03-22 13:00:00"], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "extra_service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_978-215-2", "ground_truth": [{"get_service_providers": {"avg_rating": [4.7], "province_id": [1], "district_name": ["Don Mueang District"], "start_available_date": ["2024-03-23 15:00:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "extra_service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": [100], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_979-215-3", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [1], "district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "extra_service_id": [2], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_980-215-4", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [1], "district_name": ["Phaya Thai District"], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": [true], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "extra_service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_981-215-5", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [2], "district_name": ["", null], "start_available_date": ["2024-03-20 12:00:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "extra_service_id": [2], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_982-215-6", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [1], "district_name": ["", null], "start_available_date": ["2024-03-20 18:00:00"], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": [1], "extra_service_id": [2], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_983-215-7", "ground_truth": [{"get_service_providers": {"avg_rating": [5.0], "province_id": [1], "district_name": ["Watthana District"], "start_available_date": ["2024-03-22 11:00:00"], "end_available_date": ["2024-03-22 13:00:00"], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "extra_service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_984-215-8", "ground_truth": [{"get_service_providers": {"avg_rating": ["", null], "province_id": [1], "district_name": ["", null], "start_available_date": ["", null], "end_available_date": ["", null], "min_age": ["", null], "max_age": ["", null], "has_quality_problem": ["", false], "has_late_check_in": ["", false], "is_excellent": ["", false], "is_package": ["", false], "is_subscription": ["", false], "service_id": ["", null], "extra_service_id": ["", null], "available_for_pet": ["", false], "professional_group_id": ["", null], "job_qty": ["", null], "is_cleaning_condo": ["", false], "is_cleaning_home": ["", false], "is_cleaning_office": ["", false]}}]}
{"id": "live_multiple_985-216-0", "ground_truth": [{"reminders_complete": {"token": ["**********"]}}]}
{"id": "live_multiple_986-217-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_987-218-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_988-219-0", "ground_truth": [{"EventSettingsApi.get_event_specification_infos_by_ids": {"event_ids": [["efJG9"]], "include_details": [true]}}]}
{"id": "live_multiple_989-220-0", "ground_truth": [{"get_websites": {"status_filter": ["", "active"], "include_metadata": ["", false]}}]}
{"id": "live_multiple_990-221-0", "ground_truth": [{"releases_api.ReleasesApi.delete_release": {"releaseId": ["RL50"]}}]}
{"id": "live_multiple_991-222-0", "ground_truth": [{"website_configuration_api.WebsiteConfigurationApi.rename_website": {"websiteId": ["1234"], "name": ["Bob"]}}]}
{"id": "live_multiple_992-223-0", "ground_truth": [{"delete_apdex_configuration": {"id": ["d0404"]}}]}
{"id": "live_multiple_993-224-0", "ground_truth": [{"get_apdex_report": {"apdexId": ["ns9934"], "_from": [1617176400000], "to": [1617262800000]}}]}
{"id": "live_multiple_994-225-0", "ground_truth": [{"EventSettingsApi.restore_mobile_app_alert_config": {"id": ["sl8"], "created": ["2023-03-05T16:45:00Z"]}}]}
{"id": "live_multiple_995-226-0", "ground_truth": [{"events_api.EventsApi.kubernetes_info_events": {"to": [339000], "windowSize": ["", 600000], "_from": ["", null], "excludeTriggeredBefore": ["", false], "filterEventUpdates": [true]}}]}
{"id": "live_multiple_996-227-0", "ground_truth": [{"add_service_config": {"service_name": ["fdsa"], "configurations": [{"timeout": [30], "retry_count": [5], "endpoint": ["https://api.fdsa-service.com"], "auth_required": [true]}]}}]}
{"id": "live_multiple_997-228-0", "ground_truth": [{"usage_api.UsageApi.get_hosts_per_day": {"day": [15], "month": [2], "year": [2022]}}]}
{"id": "live_multiple_998-229-0", "ground_truth": [{"health_api.HealthApi.get_version": {}}]}
{"id": "live_multiple_999-230-0", "ground_truth": [{"health_api.HealthApi.get_version": {}}]}
{"id": "live_multiple_1000-231-0", "ground_truth": [{"MaintenanceConfigurationApi.get_maintenance_config": {"id": ["sandcastle"]}}]}
{"id": "live_multiple_1001-232-0", "ground_truth": [{"api_token_api.APITokenApi.get_api_tokens": {"user_id": ["098-293"], "include_expired": ["", false]}}]}
{"id": "live_multiple_1002-232-1", "ground_truth": [{"api_token_api.APITokenApi.get_api_tokens": {"user_id": ["user123"], "include_expired": ["", false]}}]}
{"id": "live_multiple_1003-232-2", "ground_truth": [{"api_token_api.APITokenApi.get_api_tokens": {"user_id": ["12345"], "include_expired": ["", false]}}]}
{"id": "live_multiple_1004-233-0", "ground_truth": [{"audit_log_api.AuditLogApi.get_access_logs": {"offset": [434], "query": ["llama"], "pageSize": [9]}}]}
{"id": "live_multiple_1005-234-0", "ground_truth": [{"host_agent_api.HostAgentApi.get_agent_snapshot": {"id": ["zzwzeem"], "to": ["", null], "windowSize": ["", 60]}}]}
{"id": "live_multiple_1006-235-0", "ground_truth": [{"get_monitoring_state": {"api_key": ["gorilla-123"], "environment": ["", "production"], "include_inactive": ["", false]}}]}
{"id": "live_multiple_1007-236-0", "ground_truth": [{"software_versions": {"time": ["", "null"], "origin": [""], "type": [""], "name": [""], "version": ["5.5"]}}]}
{"id": "live_multiple_1008-237-0", "ground_truth": [{"ApplicationAnalyzeApi.get_trace_download": {"id": ["grgr"], "retrievalSize": [1024], "offset": ["", 0], "ingestionTime": ["", null]}}]}
{"id": "live_multiple_1009-238-0", "ground_truth": [{"get_website_geo_mapping_rules": {"websiteId": ["123e4567-e89b-12d3-a456-************"]}}]}
{"id": "live_multiple_1010-239-0", "ground_truth": [{"CustomDashboardsApi.get_custom_dashboards": {"query": ["dashing"], "pageSize": ["", 10], "page": ["", 1]}}]}
{"id": "live_multiple_1011-240-0", "ground_truth": [{"get_synthetic_credential_names": {"filter": ["", "active"], "sort_order": ["", "asc"]}}]}
{"id": "live_multiple_1012-241-0", "ground_truth": [{"get_synthetic_locations": {"sort": ["created_at"], "offset": [0], "limit": [50], "filter": ["details:alpha,beta,gamma"]}}]}
{"id": "live_multiple_1013-242-0", "ground_truth": [{"get_synthetic_locations": {"sort": ["asc"], "offset": ["", 0], "limit": ["", 50], "filter": [""]}}]}
{"id": "live_multiple_1014-243-0", "ground_truth": [{"get_identity_provider_patch": {"tenant_id": ["12345-tenant-id"], "include_rules": ["", false]}}]}
{"id": "live_multiple_1015-244-0", "ground_truth": [{"audit_log_api.AuditLogApi.get_access_logs": {"offset": [0], "query": [""], "pageSize": [50]}}]}
{"id": "live_multiple_1016-245-0", "ground_truth": [{"create_global_application_alert_config": {"name": ["UrgentAlert77f7"], "alert_type": ["error_rate"], "threshold": [5.0], "recipients": [["<EMAIL>", "<EMAIL>"]], "enabled": ["", true]}}]}
{"id": "live_multiple_1017-246-0", "ground_truth": [{"events_api.EventsApi.get_event": {"eventId": ["efJG9"]}}]}
{"id": "live_multiple_1018-247-0", "ground_truth": [{"website_configuration_api.get_websites": {"api_key": ["YOUR_API_KEY"], "status_filter": ["", "active"], "include_details": ["", false]}}]}
{"id": "live_multiple_1019-248-0", "ground_truth": [{"releases_api.ReleasesApi.delete_release": {"releaseId": ["RL50"]}}]}
{"id": "live_multiple_1020-249-0", "ground_truth": [{"delete_apdex_configuration": {"id": ["d0404"]}}]}
{"id": "live_multiple_1021-250-0", "ground_truth": [{"restore_mobile_app_alert_config": {"id": ["sl8"], "created": ["2023-04-01T10:00:00Z"]}}]}
{"id": "live_multiple_1022-251-0", "ground_truth": [{"get_hosts_per_month": {"month": [2], "year": [2022]}}]}
{"id": "live_multiple_1023-252-0", "ground_truth": [{"version_api.VersionApi.get_version": {}}]}
{"id": "live_multiple_1024-253-0", "ground_truth": [{"find_infra_alert_config_versions": {"id": ["delta"]}}]}
{"id": "live_multiple_1025-254-0", "ground_truth": [{"MaintenanceConfigurationApi.get_maintenance_config": {"id": ["sandcastle"]}}]}
{"id": "live_multiple_1026-255-0", "ground_truth": [{"CustomDashboardsApi.get_shareable_api_tokens": {"user_id": ["*********"], "include_revoked": ["", false]}}]}
{"id": "live_multiple_1027-255-1", "ground_truth": [{"CustomDashboardsApi.get_shareable_api_tokens": {"user_id": ["12345"], "include_revoked": ["", false]}}]}
{"id": "live_multiple_1028-256-0", "ground_truth": [{"host_agent_api.get_agent_snapshot": {"id": ["zzwzee"], "to": ["", null], "windowSize": ["", 60]}}]}
{"id": "live_multiple_1029-257-0", "ground_truth": [{"InfrastructureResourcesApi.software_versions": {"time": ["2023-04-01 12:00:00"], "origin": [""], "type": [""], "name": [""], "version": ["5.5"]}}]}
{"id": "live_multiple_1030-258-0", "ground_truth": [{"get_website_geo_mapping_rules": {"websiteId": ["larry_rotter"]}}]}
{"id": "live_multiple_1031-259-0", "ground_truth": [{"get_custom_dashboard": {"customDashboardId": ["dashing"]}}]}
{"id": "live_multiple_1032-260-0", "ground_truth": [{"get_synthetic_locations": {"sort": ["", "asc"], "offset": ["", 0], "limit": [5], "filter": [""]}}]}
{"id": "live_multiple_1033-261-0", "ground_truth": [{"SyntheticSettingsApi.get_synthetic_locations": {"limit": [20], "filter": ["name IN ('alpha', 'beta', 'gamma')", "name:alpha OR name:beta OR name:gamma"]}}]}
{"id": "live_multiple_1034-262-0", "ground_truth": [{"audit_log_api.AuditLogApi.get_access_logs": {"offset": [0], "query": [""], "pageSize": [50], "startDate": ["", null], "endDate": ["", null]}}]}
{"id": "live_multiple_1035-263-0", "ground_truth": [{"api_token_api.APITokenApi.get_api_tokens": {"user_id": [12345], "include_expired": ["", false]}}]}
{"id": "live_multiple_1036-263-1", "ground_truth": [{"api_token_api.APITokenApi.get_api_tokens": {"user_id": [12345], "include_expired": ["", false]}}]}
{"id": "live_multiple_1037-264-0", "ground_truth": [{"calendar_event_create": {"start_date": ["2024-12-01"], "start_time": ["19:00"], "duration": [120], "rrule": ["Does not repeat"]}}]}
{"id": "live_multiple_1038-265-0", "ground_truth": [{"detail_project": {"project_name": ["invoice-website"], "include_status": [true], "start_date": [null]}}]}
{"id": "live_multiple_1039-266-0", "ground_truth": [{"detail_experience_and_education": {"experience_or_education_type": ["Education at Universitas Sebelas Maret (UNS)"], "experience_or_education_name": ["Not specified"]}}]}
{"id": "live_multiple_1040-267-0", "ground_truth": [{"detail_experience_and_education": {"experience_or_education_type": ["Education at Universitas Sebelas Maret (UNS)"], "experience_or_education_name": ["Not specified"]}}]}
{"id": "live_multiple_1041-268-0", "ground_truth": [{"get_sensor_readings_history_by_interval": {"perPage": [10], "timespan": [36000], "metrics": [["temperature"]], "t0": [null, ""], "t1": [null, ""], "interval": [86400]}}]}
{"id": "live_multiple_1042-269-0", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Portland"], "date": ["2023-04-05"]}}]}
{"id": "live_multiple_1043-270-0", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Marshall"], "date": ["2023-03-01"]}}]}
{"id": "live_multiple_1044-271-0", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Chicago"], "date": ["2023-01-01"]}}]}
{"id": "live_multiple_1045-272-0", "ground_truth": [{"Services_4_FindProvider": {"city": ["Lafayette"], "type": ["Family Counselor"], "insurance_accepted": [true]}}]}
{"id": "live_multiple_1046-273-0", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Delhi"], "number_of_adults": [2], "rating": [4.6]}}]}
{"id": "live_multiple_1047-274-0", "ground_truth": [{"Hotels_2_BookHouse": {"where_to": ["Cape Town"], "number_of_adults": [2], "check_in_date": ["05/15/2023"], "check_out_date": ["05/22/2023"]}}]}
{"id": "live_multiple_1048-275-0", "ground_truth": [{"Hotels_2_SearchHouse": {"where_to": ["Paris"], "number_of_adults": [2]}}]}
{"id": "live_multiple_1049-276-0", "ground_truth": [{"Trains_1_FindTrains": {"_from": ["Anaheim, CA"], "to": ["Berkeley, CA"], "date_of_journey": ["04/10/2023"]}}]}
{"id": "live_multiple_1050-277-0", "ground_truth": [{"Weather_1_GetWeather": {"city": ["Atlanta"], "date": ["2023-03-07"]}}]}
{"id": "live_multiple_1051-278-0", "ground_truth": [{"set_alarm": {"alarm_time": ["2023-12-01 07:00:00"], "purpose": ["wake up",""]}}]}
{"id": "live_multiple_1052-279-0", "ground_truth": [{"set_volume": {"volume": [50]}}]}