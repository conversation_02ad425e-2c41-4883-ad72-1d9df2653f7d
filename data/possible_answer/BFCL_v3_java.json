{"id": "java_0", "ground_truth": [{"GeometryPresentation.createPresentation": {"controller": ["mapController"], "parent": ["mapArea"]}}]}
{"id": "java_1", "ground_truth": [{"SQLCompletionAnalyzer.makeProposalsFromObject": {"object": ["Customers"], "useShortName": [true], "params": [{"limit": [50], "schemaFilter": ["public"]}]}}]}
{"id": "java_2", "ground_truth": [{"FireBirdUtils.getViewSourceWithHeader": {"monitor": ["dbMonitor"], "view": ["EmployeeView"], "source": ["SELECT * FROM Employee WHERE status = 'active'"]}}]}
{"id": "java_3", "ground_truth": [{"DB2Tablespace.resolveTablespaceReference": {"monitor": ["dbMonitor"], "dataSource": ["db2DataSource"], "reference": ["USERSPACE1"]}}]}
{"id": "java_4", "ground_truth": [{"DB2ViewBaseDepCache.prepareObjectsStatement": {"session": ["jdbcSession"], "db2ViewBase": ["EmployeeView"]}}]}
{"id": "java_5", "ground_truth": [{"PlainTextPresentation.createPresentation": {"controller": ["dataController"], "parent": ["compositeParent"]}}]}
{"id": "java_6", "ground_truth": [{"SpreadsheetPresentation.refreshData": {"refreshMetadata": [true], "append": [true], "keepState": [true]}}]}
{"id": "java_7", "ground_truth": [{"EFSNIOResource.copy": {"destination": ["new Path('/backup/data.txt')"], "force": [true], "monitor": ["progressTracker"]}}]}
{"id": "java_8", "ground_truth": [{"EFSNIOFile.setContents": {"source": ["fileStream"], "force": [true], "keepHistory": [false], "monitor": ["progressMonitor"]}}]}
{"id": "java_9", "ground_truth": [{"writeMultiPoint": {"multiPoint": ["new MultiPoint(new Point[]{new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10)})"], "xyzmMode": ["XyzmMode.XYZ"], "buffer": ["ByteBuffer.allocate(1024)"]}}]}
{"id": "java_10", "ground_truth": [{"JNIBridge.setLauncherInfo": {"launcher": ["/usr/local/bin/dbeaver"], "name": ["DBeaverLauncher"]}}]}
{"id": "java_11", "ground_truth": [{"BasePolicyDataProvider.getRegistryPolicyValue": {"root": ["WinReg.HKEY_LOCAL_MACHINE"], "property": ["EnableExtensions"]}}]}
{"id": "java_12", "ground_truth": [{"ExasolExecutionContext.setCurrentSchema": {"monitor": ["progressMonitor"], "schemaName": ["AnalyticsDB"]}}]}
{"id": "java_13", "ground_truth": [{"AltibaseGrantee.prepareObjectsStatement": {"session": ["JDBC_session"], "owner": ["JohnDoe"]}}]}
{"id": "java_14", "ground_truth": [{"FunGameBase.onFinish": {"layout": ["gameLayout"], "success": [true]}}]}
{"id": "java_15", "ground_truth": [{"Res9patchStreamDecoder.decode": {"input": ["imageInputStream"], "out": ["imageOutputStream"]}}]}
{"id": "java_16", "ground_truth": [{"InsnDecoder.invokePolymorphic": {"insn": ["instructionData"], "isRange": [true]}}]}
{"id": "java_17", "ground_truth": [{"GenericTypesVisitor.attachGenericTypesInfo": {"mth": ["initMethod"], "insn": ["newConstructorInsn"]}}]}
{"id": "java_18", "ground_truth": [{"SysRoleController.queryPageRoleCount": {"pageNo": [3], "pageSize": [20]}}]}
{"id": "java_19", "ground_truth": [{"PersonController.personal": {"model": ["webModel"], "request": ["userRequest"]}}]}
{"id": "java_20", "ground_truth": [{"HbaseAdapter.updateConfig": {"fileName": ["user-mapping.yml"], "config": ["newMappingConfig"]}}]}
{"id": "java_21", "ground_truth": [{"SessionHandler.exceptionCaught": {"ctx": ["nettyChannelContext"], "e": ["ioExceptionEvent"]}}]}
{"id": "java_22", "ground_truth": [{"PmsProductServiceImpl.updateNewStatus": {"ids": [[101, 202, 303]], "newStatus": [2]}}]}
{"id": "java_23", "ground_truth": [{"SmsHomeNewProductServiceImpl.list": {"productName": ["LED TV"], "recommendStatus": [1], "pageSize": [20], "pageNum": [3]}}]}
{"id": "java_24", "ground_truth": [{"PmsProductCategoryController.updateShowStatus": {"ids": [[101, 102, 103]], "showStatus": [0]}}]}
{"id": "java_25", "ground_truth": [{"SmsHomeRecommendSubjectController.updateSort": {"id": [42], "sort": [5]}}]}
{"id": "java_26", "ground_truth": [{"ProxyConnection.prepareCall": {"sql": ["CALL totalSales(?)"], "resultSetType": ["ResultSet.TYPE_SCROLL_INSENSITIVE"], "concurrency": ["ResultSet.CONCUR_READ_ONLY"], "holdability": ["ResultSet.CLOSE_CURSORS_AT_COMMIT"]}}]}
{"id": "java_27", "ground_truth": [{"TwoSum.twoSum": {"nums": [[2, 7, 11, 15]], "target": [9]}}]}
{"id": "java_28", "ground_truth": [{"configStorage.dynamicCredentialsScheduledExecutorService": {"credentialsFile": ["es_credentials.properties"], "credentialsRefreshInterval": [30], "basicCredentials": ["basicAuthCredentials"]}}]}
{"id": "java_29", "ground_truth": [{"propertyTransferredToCollectorBuilder": {"property": ["zipkin.collector.activemq.concurrency"], "value": ["10"], "builderExtractor": ["ActiveMQCollector.Builder::getConcurrency"]}}]}
{"id": "java_30", "ground_truth": [{"RedissonAsyncCache.putIfAbsent": {"key": ["answer"], "value": ["42"]}}]}
{"id": "java_31", "ground_truth": [{"RedissonRx.getQueue": {"name": ["taskQueue"], "codec": ["jsonCodec"]}}]}
{"id": "java_32", "ground_truth": [{"RedissonPermitExpirableSemaphore.tryAcquireAsync": {"waitTime": [5], "leaseTime": [120], "unit": ["SECONDS"]}}]}
{"id": "java_33", "ground_truth": [{"RedissonMapCache.putOperationAsync": {"key": ["employee:1234"], "value": ["John Doe"]}}]}
{"id": "java_34", "ground_truth": [{"ServiceManager.newTimeout": {"task": ["cleanupTask"], "delay": [5], "unit": ["TimeUnit.MINUTES"]}}]}
{"id": "java_35", "ground_truth": [{"RedissonConnection.bitOp": {"op": ["BitOperation.AND"], "destination": ["user:online:both"], "keys": [["user:online:today", "user:online:yesterday"]]}}]}
{"id": "java_36", "ground_truth": [{"ObjectMapEntryReplayDecoder.decode": {"parts": [["userID", 42, "username", "johndoe", "isActive", true]], "state": ["processingState"]}}]}
{"id": "java_37", "ground_truth": [{"ConsoleAnnotator.annotate": {"context": ["jenkinsBuild"], "text": ["buildOutput"]}}]}
{"id": "java_38", "ground_truth": [{"NestedValueFetcher.createSourceMapStub": {"filteredSource": ["docFields"]}}]}
{"id": "java_39", "ground_truth": [{"NodeIdConverter.format": {"event": ["logEvent"], "toAppendTo": ["logBuilder"]}}]}
{"id": "java_40", "ground_truth": [{"RoutingNodesChangedObserver.shardInitialized": {"unassignedShard": ["shardA"], "initializedShard": ["shardB"]}}]}
{"id": "java_41", "ground_truth": [{"SearchHit.declareInnerHitsParseFields": {"parser": ["searchHitParser"]}}]}
{"id": "java_42", "ground_truth": [{"TermQueryBuilderTests.termQuery": {"mapper": ["usernameField"], "value": ["JohnDoe"], "caseInsensitive": [true]}}]}
{"id": "java_43", "ground_truth": [{"SecureMockMaker.createSpy": {"settings": ["mockSettings"], "handler": ["mockHandler"], "object": ["testObject"]}}]}
{"id": "java_44", "ground_truth": [{"DesAPITest.init": {"crypt": ["DESede"], "mode": ["CBC"], "padding": ["PKCS5Padding"]}}]}
{"id": "java_45", "ground_truth": [{"Basic.checkSizes": {"environ": ["envVariables"], "size": [5]}}]}
{"id": "java_46", "ground_truth": [{"MethodInvokeTest.checkInjectedInvoker": {"csm": ["csmInstance"], "expected": ["MyExpectedClass.class"]}}]}
{"id": "java_47", "ground_truth": [{"LargeHandshakeTest.format": {"name": ["CERTIFICATE"], "value": ["MIIFdTCCBF2gAwIBAgISESG"]}}]}
{"id": "java_48", "ground_truth": [{"CookieHeaderTest.create": {"sa": ["new InetSocketAddress(\"************\", 8080)"], "sslContext": ["testSSLContext"]}}]}
{"id": "java_49", "ground_truth": [{"Http2TestExchangeImpl.sendResponseHeaders": {"rCode": [404], "responseLength": [1500]}}]}
{"id": "java_50", "ground_truth": [{"TransformIndexerStateTests.doDeleteByQuery": {"deleteByQueryRequest": ["deleteQueryRequest"], "responseListener": ["testListener"]}}]}
{"id": "java_51", "ground_truth": [{"CCRUsageTransportAction.masterOperation": {"task": ["usageTask"], "request": ["usageRequest"], "state": ["clusterState"], "listener": ["actionListener"]}}]}
{"id": "java_52", "ground_truth": [{"SamlObjectSignerTests.getChildren": {"node": ["SAMLAssertionNode"], "node_type": ["Element.class"]}}]}
{"id": "java_53", "ground_truth": [{"VotingOnlyNodePlugin.fullMasterWithOlderState": {"localAcceptedTerm": [42], "localAcceptedVersion": [7]}}]}
{"id": "java_54", "ground_truth": [{"AbstractTransportSearchableSnapshotsAction.shardOperation": {"request": ["snapshotRequest"], "shardRouting": ["shardRouteInfo"], "task": ["snapshotTask"], "listener": ["operationListener"]}}]}
{"id": "java_55", "ground_truth": [{"SearchableSnapshotDirectory.create": {"repositories": ["repositoriesService"], "cache": ["cacheService"], "indexSettings": ["indexSettingsForLogs"], "shardPath": ["/data/nodes/0/indices/logs/5"], "currentTimeNanosSupplier": ["currentTimeNanos"], "threadPool": ["threadPool"], "blobStoreCacheService": ["blobStoreCacheService"], "sharedBlobCacheService": ["sharedBlobCacheService"]}}]}
{"id": "java_56", "ground_truth": [{"CCSDuelIT.parseEntity": {"entity": ["httpResponseEntity"], "entityParser": ["responseParser"], "parserConfig": ["defaultParserConfig"]}}]}
{"id": "java_57", "ground_truth": [{"Booleans.parseBooleanLenient": {"value": ["yes"], "defaultValue": [false]}}]}
{"id": "java_58", "ground_truth": [{"XContentBuilder.map": {"values": ["userProfile"], "ensureNoSelfReferences": [true], "writeStartAndEndHeaders": [true]}}]}
{"id": "java_59", "ground_truth": [{"TruncateTranslogAction.execute": {"terminal": ["terminalInterface"], "shardPath": ["new ShardPath(Paths.get('/var/data/elasticsearch/nodes/0/indices/1shard'))"], "indexDirectory": ["FSDirectory.open(Paths.get('/var/data/elasticsearch/nodes/0/indices/1shard/index'))"]}}]}
{"id": "java_60", "ground_truth": [{"NestedQueryBuilder.doBuild": {"parentSearchContext": ["mainSearchContext"], "innerHitsContext": ["hitsContext"]}}]}
{"id": "java_61", "ground_truth": [{"ScoreFunctionBuilders.exponentialDecayFunction": {"fieldName": ["timestamp"], "origin": ["now"], "scale": ["10d"], "offset": ["2d"], "decay": [0.5]}}]}
{"id": "java_62", "ground_truth": [{"dvRangeQuery": {"field": ["temperature"], "queryType": ["FLOAT"], "from": [20.5, "20.5"], "to": [30.0, "30.0"], "includeFrom": [true], "includeTo": [false]}}]}
{"id": "java_63", "ground_truth": [{"withinQuery": {"field": ["age"], "from": [30], "to": [40], "includeFrom": [true], "includeTo": [false]}}]}
{"id": "java_64", "ground_truth": [{"DateScriptFieldType.createFieldType": {"name": ["timestamp"], "factory": ["dateFactory"], "script": ["dateScript"], "meta": [{"format": "epoch_millis"}], "onScriptError": ["FAIL"]}}]}
{"id": "java_65", "ground_truth": [{"RootObjectMapper.doXContent": {"builder": ["xContentBuilderInstance"], "params": [["include_defaults", true, "TOXCONTENT_SKIP_RUNTIME", true]]}}]}
{"id": "java_66", "ground_truth": [{"CompositeRuntimeField.createChildRuntimeField": {"parserContext": ["mappingParserContext"], "parent": ["compositeField1"], "parentScriptFactory": ["compositeScriptFactory"], "onScriptError": ["onScriptError.IGNORE"]}}]}
{"id": "java_67", "ground_truth": [{"MacDmgBundler.prepareDMGSetupScript": {"appLocation": ["/Applications/PhotoEditor.app"], "params": [{"APP_NAME": ["PhotoEditor"]}, {"IMAGES_ROOT": ["/path/to/images"]}, {"BACKGROUND_IMAGE_FOLDER": ["background"]}, {"BACKGROUND_IMAGE": ["custom-bg.png"]}]}}]}
{"id": "java_68", "ground_truth": [{"MacBaseInstallerBundler.validateAppImageAndBundeler": {"params": [{"/Applications/MyApp.app": ["MyApp"]}]}}]}
{"id": "java_69", "ground_truth": [{"DurationImpl.alignSigns": {"buf": ["durations"], "start": [2], "end": [5]}}]}
{"id": "java_70", "ground_truth": [{"XMLNamespaceBinder.endElement": {"element": ["new QName('http://www.example.com', 'item', 'ex')"], "augs": ["augmentations"]}}]}
{"id": "java_71", "ground_truth": [{"CoroutineManager.co_exit_to": {"arg_object": ["resultData"], "thisCoroutine": [5], "toCoroutine": [10]}}]}
{"id": "java_72", "ground_truth": [{"ToTextStream.characters": {"ch": ["textBuffer"], "start": [5], "length": [10]}}]}
{"id": "java_73", "ground_truth": [{"Encodings.getEncodingInfo": {"encoding": ["UTF-8"], "allowJavaNames": [true]}}]}
{"id": "java_74", "ground_truth": [{"BaseMarkupSerializer.surrogates": {"high": [55357], "low": [56832], "inContent": [false]}}]}
{"id": "java_75", "ground_truth": [{"JdkXmlFeatures.getSystemProperty": {"feature": ["XML_SECURITY"], "sysPropertyName": ["enableXmlSecurityFeature"]}}]}
{"id": "java_76", "ground_truth": [{"Intro.step": {"w": [800], "h": [600]}}]}
{"id": "java_77", "ground_truth": [{"JndiLoginModule.verifyPassword": {"encryptedPassword": ["e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"], "password": ["P@ssw0rd!"]}}]}
{"id": "java_78", "ground_truth": [{"OptionSpecBuilder.requiredUnless": {"dependent": ["quiet"], "otherDependents": [["verbose"]]}}]}
{"id": "java_79", "ground_truth": [{"SAXFilterFactoryImpl.resolveEntity": {"publicid": ["1234"], "sysId": ["http://astro.com/stylesheets/toptemplate"]}}]}
{"id": "java_80", "ground_truth": [{"RegexConstraint.initIRPattern": {"category": ["failOn"], "ruleIdx": [42]}}]}
{"id": "java_81", "ground_truth": [{"TestObjectGraphAfterGC.doTesting": {"testcaseData": ["humongous-test-case.json"], "doGC": ["customGarbageCollector"], "checker": ["referenceChecker"], "gcLogName": ["gc-analysis.log"], "shouldContain": [["GC pause"]], "shouldNotContain": [["OutOfMemoryError"]]}}]}
{"id": "java_82", "ground_truth": [{"clear001a.runIt": {"args": ["testArgs"], "out": ["System.out"]}}]}
{"id": "java_83", "ground_truth": [{"thrcputime002.runIt": {"argv": [["-waitTime", "2", "-iterations", "500"]], "out": ["System.out"]}}]}
{"id": "java_84", "ground_truth": [{"checkInnerFields": {"redefCls": ["myRedefClass"], "expValue": [100]}}]}
{"id": "java_85", "ground_truth": [{"classfloadhk005.runIt": {"argv": [["/path/to/classes", "60"]], "out": ["logStream"]}}]}
{"id": "java_86", "ground_truth": [{"argumenttypes001.runThis": {"argv": [["-v", "--no-strict"]], "out": ["debugOutput"]}}]}
{"id": "java_87", "ground_truth": [{"suspendpolicy017.settingVMDeathRequest": {"suspendPolicy": ["EVENT_THREAD"], "property": ["deathEvent001"]}}]}
{"id": "java_88", "ground_truth": [{"filter_s002.setting22MethodEntryRequest": {"thread": ["mainThread"], "testedClass": ["com.example.MainClass"], "suspendPolicy": ["EventRequest.SUSPEND_ALL"], "property": ["testProperty"]}}]}
{"id": "java_89", "ground_truth": [{"runThis": {"argv": [["-waitTime", "2", "-debuggeeName", "TestDebuggee"]], "out": ["testLogStream"]}}]}
{"id": "java_90", "ground_truth": [{"sourcepaths002.runIt": {"args": [["-v", "-p"]], "out": ["System.out"]}}]}
{"id": "java_91", "ground_truth": [{"invokemethod007.runIt": {"args": [["suspend", "log"]], "out": ["debugLog"]}}]}
{"id": "java_92", "ground_truth": [{"ClassFileFinder.findClassFile": {"name": ["com.example.MyClass"], "classPath": ["/usr/local/classes:/home/<USER>/java/libs"]}}]}
{"id": "java_93", "ground_truth": [{"AbstractJarAgent.runJarAgent": {"options": ["trace log"], "inst": ["appInstrumentation"]}}]}
{"id": "java_94", "ground_truth": [{"NFILibrary.isMemberReadable": {"symbol": ["getVersion"], "recursive": ["", "null"]}}]}
{"id": "java_95", "ground_truth": [{"ExportNodeTest.doGeneric": {"receiver": ["ExportInlinedObject1Instance"], "argument": ["HelloWorld"], "node": ["InlinableNodeInstance"], "library": ["NodeLibraryInstance"]}}]}
{"id": "java_96", "ground_truth": [{"InstrumentableProcessor.createCallConverter": {"converterMethod": ["convertValue"], "frameParameterName": ["frameVar"], "returnName": ["returnValueCode"]}}]}
{"id": "java_97", "ground_truth": [{"FlatNodeGenFactory.generateIntrospectionInfo": {"clazz": ["NodeClass"], "inlined": [false]}}]}
{"id": "java_98", "ground_truth": [{"LoopConditionProfile.calculateProbability": {"trueCountLocal": [150], "falseCountLocal": [50]}}]}
{"id": "java_99", "ground_truth": [{"LibraryExport.createDelegate": {"factory": ["myFactory"], "delegate": ["existingDelegate"]}}]}