{"id":"exec_parallel_0","ground_truth":["calc_binomial_probability(n=10, k=3, p=0.3)","calc_binomial_probability(n=15, k=5, p=0.3)","calc_binomial_probability(n=20, k=7, p=0.3)"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_1","ground_truth":["calculate_cosine_similarity(vectorA=[0.5, 0.7, 0.2, 0.9, 0.1], vectorB=[0.3, 0.6, 0.2, 0.8, 0.1])","calculate_cosine_similarity(vectorA=[0.2, 0.4, 0.6, 0.8, 1.0], vectorB=[1.0, 0.8, 0.6, 0.4, 0.2])","calculate_cosine_similarity(vectorA=[0.1, 0.2, 0.3, 0.4, 0.5], vectorB=[0.5, 0.4, 0.3, 0.2, 0.1])"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_2","ground_truth":["calculate_density(mass=0.5, volume=0.0001)","calculate_density(mass=0.2, volume=0.00005)","calculate_density(mass=0.3, volume=0.000075)","calculate_density(mass=0.4, volume=0.00008)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_3","ground_truth":["calculate_displacement(initial_velocity=20, acceleration=-9.8, time=5)","calculate_displacement(initial_velocity=30, acceleration=-9.8, time=5)","calculate_displacement(initial_velocity=25, acceleration=-9.8, time=5)"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_4","ground_truth":["calculate_electrostatic_potential_energy(charge=1.6e-19, voltage=500)","calculate_electrostatic_potential_energy(charge=-1.6e-19, voltage=1000)","calculate_electrostatic_potential_energy(charge=0, voltage=2000)"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_5","ground_truth":["calculate_final_velocity(initial_velocity=5, acceleration=2, time=10)","calculate_final_velocity(initial_velocity=2, acceleration=1, time=15)","calculate_final_velocity(initial_velocity=1, acceleration=0.5, time=20)"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_6","ground_truth":["calculate_future_value(present_value=5000, interest_rate=0.05, periods=10)","calculate_future_value(present_value=2000, interest_rate=0.07, periods=15)","calculate_future_value(present_value=1000, interest_rate=0.1, periods=20)"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_7","ground_truth":["calculate_mean(numbers=[35, 40, 45, 50, 55])","calculate_mean(numbers=[72, 75, 78, 80, 82, 85])","calculate_mean(numbers=[1.50, 1.55, 1.60, 1.65, 1.70])"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_8","ground_truth":["calculate_permutations(n=20, k=5)","calculate_permutations(n=12, k=5)","calculate_permutations(n=10, k=3)"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_9","ground_truth":["calculate_standard_deviation(numbers=[23, 34, 45, 56, 67, 78, 89])","calculate_standard_deviation(numbers=[10, 20, 30, 40, 50, 60])","calculate_standard_deviation(numbers=[90, 80, 70, 60, 50, 40])"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_10","ground_truth":["calculate_triangle_area(base=15, height=20)","calculate_triangle_area(base=25, height=30)","calculate_triangle_area(base=35, height=40)"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_11","ground_truth":["convert_currency(amount=5000, from_currency='JPY', to_currency='USD')","convert_currency(amount=5000, from_currency='JPY', to_currency='EUR')","convert_currency(amount=5000, from_currency='JPY', to_currency='AUD')","convert_currency(amount=100, from_currency='CAD', to_currency='CHF')"],"execution_result_type":["real_time_match","real_time_match","real_time_match","real_time_match"]}
{"id":"exec_parallel_12","ground_truth":["estimate_derivative(function='lambda x: 3*x**2 + 2*x - 1', x=4)","estimate_derivative(function='lambda x: 5*x**3 - 3*x**2 + 2*x + 1', x=-2)","estimate_derivative(function='lambda x: 2*x**4 - 3*x**3 + 2*x**2 - x + 1', x=0)","estimate_derivative(function='lambda x: x**5 - 2*x**4 + 3*x**3 - 2*x**2 + x - 1', x=1)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_13","ground_truth":["find_term_on_urban_dictionary(term='Lit')","find_term_on_urban_dictionary(term='Savage')","find_term_on_urban_dictionary(term='YOLO')"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_14","ground_truth":["geometry_area_circle(radius=5)","geometry_area_circle(radius=10)","geometry_area_circle(radius=15)","geometry_area_circle(radius=20)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_15","ground_truth":["get_active_covid_case_by_country(country='France')","get_active_covid_case_by_country(country='Italy')","get_active_covid_case_by_country(country='United States')","get_active_covid_case_by_country(country='China')"],"execution_result_type":["real_time_match","real_time_match","real_time_match","real_time_match"]}
{"id":"exec_parallel_16","ground_truth":["get_company_name_by_stock_name(stock_name='AAPL')","get_company_name_by_stock_name(stock_name='GOOGL')","get_company_name_by_stock_name(stock_name='AMZN')","get_company_name_by_stock_name(stock_name='MSFT')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_17","ground_truth":["get_coordinate_by_ip_address(ip_address='***********')","get_coordinate_by_ip_address(ip_address='************')","get_coordinate_by_ip_address(ip_address='********')","get_coordinate_by_ip_address(ip_address='*********')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_18","ground_truth":["get_coordinates_from_city(city_name='New York')","get_coordinates_from_city(city_name='Los Angeles')","get_coordinates_from_city(city_name='Chicago')","get_coordinates_from_city(city_name='Houston')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_19","ground_truth":["get_covid_death_by_country(country='Brazil')","get_covid_death_by_country(country='India')","get_covid_death_by_country(country='Russia')","get_covid_death_by_country(country='France')"],"execution_result_type":["real_time_match","real_time_match","real_time_match","real_time_match"]}
{"id":"exec_parallel_20","ground_truth":["get_distance(pointA=(3, 4), pointB=(7, 9))","get_distance(pointA=(1, 2), pointB=(5, 6))","get_distance(pointA=(0, 0), pointB=(8, 15))","get_distance(pointA=(10, 12), pointB=(20, 25))"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_21","ground_truth":["get_fibonacci_sequence(n=10)","get_fibonacci_sequence(n=20)","get_fibonacci_sequence(n=5)"],"execution_result_type":["exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_22","ground_truth":["get_price_by_amazon_ASIN(ASIN='B08PPDJWC8')","get_price_by_amazon_ASIN(ASIN='B07ZPKBL9V')","get_price_by_amazon_ASIN(ASIN='B08BHXG144')","get_price_by_amazon_ASIN(ASIN='B075H2B962')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_23","ground_truth":["get_prime_factors(number=456)","get_prime_factors(number=789)","get_prime_factors(number=321)","get_prime_factors(number=654)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_24","ground_truth":["get_product_name_by_amazon_ASIN(ASIN='B075H2B962')","get_product_name_by_amazon_ASIN(ASIN='B08BHXG144')","get_product_name_by_amazon_ASIN(ASIN='B07ZPKBL9V')","get_product_name_by_amazon_ASIN(ASIN='B08PPDJWC8')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_25","ground_truth":["get_rating_by_amazon_ASIN(ASIN='B08PPDJWC8')","get_rating_by_amazon_ASIN(ASIN='B07ZPKBL9V')","get_rating_by_amazon_ASIN(ASIN='B075H2B962')","get_rating_by_amazon_ASIN(ASIN='B08BHXG144')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_26","ground_truth":["get_stock_history(stock_name='AAPL', interval='1d', diffandsplits='false')","get_stock_history(stock_name='MSFT', interval='1wk', diffandsplits='true')","get_stock_history(stock_name='AMZN', interval='1mo', diffandsplits='false')","get_stock_history(stock_name='TSLA', interval='3mo', diffandsplits='false')"],"execution_result_type":["structural_match","structural_match","structural_match","structural_match"]}
{"id":"exec_parallel_27","ground_truth":["get_stock_price_by_stock_name(stock_name='GOOG')","get_stock_price_by_stock_name(stock_name='META')","get_stock_price_by_stock_name(stock_name='NFLX')","get_stock_price_by_stock_name(stock_name='BABA')"],"execution_result_type":["real_time_match","real_time_match","real_time_match","real_time_match"]}
{"id":"exec_parallel_28","ground_truth":["get_time_zone_by_coord(long='77.1025', lat='28.7041')","get_time_zone_by_coord(long='-73.935242', lat='40.730610')","get_time_zone_by_coord(long='151.2093', lat='33.8688')","get_time_zone_by_coord(long='139.6917', lat='35.6895')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_29","ground_truth":["get_weather_data(coordinates=[34.0522, -118.2437])","get_weather_data(coordinates=[51.5074, -0.1278])","get_weather_data(coordinates=[-33.9249, 18.4241])","get_weather_data(coordinates=[48.8566, 2.3522])"],"execution_result_type":["structural_match","structural_match","structural_match","structural_match"]}
{"id":"exec_parallel_30","ground_truth":["get_zipcode_by_ip_address(ip_address='***********')","get_zipcode_by_ip_address(ip_address='************')","get_zipcode_by_ip_address(ip_address='********')","get_zipcode_by_ip_address(ip_address='***********')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_31","ground_truth":["mat_mul(matA=[[1, 2, 3], [4, 5, 6], [7, 8, 9]], matB=[[10, 11, 12], [13, 14, 15], [16, 17, 18]])","mat_mul(matA=[[19, 20], [21, 22]], matB=[[23, 24], [25, 26]])","mat_mul(matA=[[27, 28, 29, 30], [31, 32, 33, 34]], matB=[[35, 36, 37, 38], [39, 40, 41, 42]])","mat_mul(matA=[[43, 44], [45, 46]], matB=[[47, 48], [49, 50]])"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_32","ground_truth":["math_factorial(n=5)","math_factorial(n=7)","math_factorial(n=10)","math_factorial(n=12)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_33","ground_truth":["math_gcd(a=45, b=60)","math_gcd(a=81, b=27)","math_gcd(a=144, b=96)","math_gcd(a=100, b=80)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_34","ground_truth":["math_lcm(a=45, b=35)","math_lcm(a=72, b=108)","math_lcm(a=120, b=180)","math_lcm(a=200, b=300)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_35","ground_truth":["mortgage_calculator(loan_amount=350000, interest_rate=0.035, loan_period=30)","mortgage_calculator(loan_amount=500000, interest_rate=0.04, loan_period=20)","mortgage_calculator(loan_amount=750000, interest_rate=0.025, loan_period=15)","mortgage_calculator(loan_amount=1000000, interest_rate=0.03, loan_period=10)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_36","ground_truth":["quadratic_roots(a=3, b=7, c=2)","quadratic_roots(a=5, b=12, c=4)","quadratic_roots(a=8, b=16, c=6)","quadratic_roots(a=10, b=20, c=8)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_37","ground_truth":["retrieve_city_based_on_zipcode(zipcode='90210')","retrieve_city_based_on_zipcode(zipcode='10001')","retrieve_city_based_on_zipcode(zipcode='60601')","retrieve_city_based_on_zipcode(zipcode='94102')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_38","ground_truth":["retrieve_holiday_by_year(year='2018', country='US')","retrieve_holiday_by_year(year='2020', country='DE')","retrieve_holiday_by_year(year='2019', country='ES')","retrieve_holiday_by_year(year='2021', country='GB')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_39","ground_truth":["sort_array(array=[5, 2, 9, 1, 7])","sort_array(array=[3, 8, 6, 4], reverse=True)","sort_array(array=[10, 20, 30, 40, 50])","sort_array(array=[100, 200, 300, 400, 500], reverse=True)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_40","ground_truth":["add_binary_numbers(a='0011', b='1100')","add_binary_numbers(a='1010', b='0101')","add_binary_numbers(a='1111', b='0000')","add_binary_numbers(a='0001', b='1110')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_41","ground_truth":["linear_regression(x=[1,2,3],y=[4,5,6],point=10)","linear_regression(x=[2,4,6],y=[8,10,12],point=15)","linear_regression(x=[3,6,9],y=[12,15,18],point=20)","linear_regression(x=[4,8,12],y=[16,20,24],point=25)"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_42","ground_truth":["maxPoints(points=[[1,1],[2,2],[3,4],[5,5]])","maxPoints(points=[[1,2],[3,2],[5,2],[4,2]])","maxPoints(points=[[0,0],[1,1],[0,1],[1,0]])","maxPoints(points=[[1,1],[3,2],[5,3],[7,4]])"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_43","ground_truth":["calculate_investment_value(initial_investment=1000000, annual_contribution=1000, years=3, annual_return=0.1, inflation_rate=[0.01, 0.04, 0.04])","calculate_investment_value(initial_investment=500000, annual_contribution=500, years=5, annual_return=0.07, inflation_rate=[0.02, 0.03, 0.02, 0.03, 0.02])","calculate_investment_value(initial_investment=250000, annual_contribution=2000, years=7, annual_return=0.05, inflation_rate=[0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.01])","calculate_investment_value(initial_investment=800000, annual_contribution=1500, years=10, annual_return=0.08, inflation_rate=[0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.01, 0.02])"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_44","ground_truth":["calculate_nutritional_needs(weight=75, height=180, age=25, gender='male', activity_level=3, goal='gain')","calculate_nutritional_needs(weight=65, height=165, age=30, gender='female', activity_level=2, goal='maintain')","calculate_nutritional_needs(weight=85, height=175, age=40, gender='male', activity_level=5, goal='lose')","calculate_nutritional_needs(weight=70, height=160, age=55, gender='female', activity_level=1, goal='lose')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_45","ground_truth":["order_food(item=['burger'], quantity=[10], price=[5])","order_food(item=['ice cream'], quantity=[7], price=[2])","order_food(item=['pizza'], quantity=[3], price=[8])","order_food(item=['donut'], quantity=[12], price=[1])"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_46","ground_truth":["order_food(item=['dumplings'], quantity=[101], price=[0.1])","order_food(item=['rice bowl'], quantity=[20], price=[10])","order_food(item=['spring rolls'], quantity=[50], price=[0.5])","order_food(item=['noodle soup'], quantity=[10], price=[3])"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_47","ground_truth":["get_movie_director(movie_name='Pulp Fiction')","get_movie_director(movie_name='Reservoir Dogs')","get_movie_director(movie_name='Kill Bill')","get_movie_director(movie_name='Django Unchained')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_48","ground_truth":["get_movie_rating(movie_name='Pulp Fiction')","get_movie_rating(movie_name='The Godfather')","get_movie_rating(movie_name=\"Schindler's List\")","get_movie_rating(movie_name='The Dark Knight')"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
{"id":"exec_parallel_49","ground_truth":["polygon_area(vertices=[[1,2],[3,4],[1,4],[3,7]])","polygon_area(vertices=[[5,5],[6,7],[7,5]])","polygon_area(vertices=[[2,1],[4,2],[3,4],[1,3]])","polygon_area(vertices=[[-1,0],[2,3],[0,4],[-2,2]])"],"execution_result_type":["exact_match","exact_match","exact_match","exact_match"]}
