{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 286, "output_token_count": 190, "latency": 3.148951292037964}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"public.Customers\", \"useShortName\": \"true\", \"params\": \"{\\\"schemaFilter\\\": \\\"public\\\", \\\"limit\\\": \\\"50\\\"}\"}"}], "input_token_count": 331, "output_token_count": 402, "latency": 6.6324474811553955}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"view\": \"EmployeeView\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 344, "output_token_count": 701, "latency": 11.624460458755493}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\": \"dbMonitor\", \"dataSource\": \"db2DataSource\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 351, "output_token_count": 330, "latency": 5.440699815750122}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\": \"jdbcSession\", \"db2ViewBase\": \"DB2ViewBase(HR, EmployeeView)\"}"}], "input_token_count": 299, "output_token_count": 338, "latency": 5.551291465759277}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 307, "output_token_count": 252, "latency": 4.1635072231292725}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\": \"true\", \"append\": \"true\", \"keepState\": \"true\"}"}], "input_token_count": 318, "output_token_count": 242, "latency": 3.9999475479125977}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 359, "output_token_count": 294, "latency": 4.854195594787598}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\": \"fileStream\", \"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\"}"}], "input_token_count": 387, "output_token_count": 351, "latency": 5.785643100738525}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\": \"new MultiPoint(Arrays.asList(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10)))\", \"xyzmMode\": \"XyzmMode.XYZ\", \"buffer\": \"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 409, "output_token_count": 489, "latency": 8.09855580329895}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 265, "output_token_count": 192, "latency": 3.163914442062378}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\": \"HKEY_LOCAL_MACHINE\", \"property\": \"EnableExtensions\"}"}], "input_token_count": 300, "output_token_count": 305, "latency": 5.020423650741577}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 288, "output_token_count": 215, "latency": 3.53374981880188}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}], "input_token_count": 296, "output_token_count": 238, "latency": 3.9133994579315186}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 281, "output_token_count": 314, "latency": 5.171236515045166}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 300, "output_token_count": 219, "latency": 3.606337785720825}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 284, "output_token_count": 428, "latency": 7.042438745498657}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 298, "output_token_count": 249, "latency": 4.133065223693848}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 295, "output_token_count": 195, "latency": 3.205204963684082}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest\"}"}], "input_token_count": 297, "output_token_count": 390, "latency": 6.416372776031494}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\": \"user-mapping.yml\", \"config\": \"newMappingConfig\"}"}], "input_token_count": 293, "output_token_count": 735, "latency": 12.007090091705322}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 295, "output_token_count": 550, "latency": 8.969139575958252}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"[101,202,303]\", \"newStatus\": \"2\"}"}], "input_token_count": 299, "output_token_count": 330, "latency": 5.388690710067749}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\": \"LED TV\", \"recommendStatus\": \"1\", \"pageSize\": \"20\", \"pageNum\": \"3\"}"}], "input_token_count": 366, "output_token_count": 412, "latency": 6.735392332077026}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"[101,102,103]\", \"showStatus\": \"0\"}"}], "input_token_count": 307, "output_token_count": 367, "latency": 5.995123386383057}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 274, "output_token_count": 257, "latency": 4.223357439041138}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"ResultSet.TYPE_SCROLL_INSENSITIVE\", \"concurrency\": \"ResultSet.CONCUR_READ_ONLY\", \"holdability\": \"ResultSet.CLOSE_CURSORS_AT_COMMIT\"}"}], "input_token_count": 410, "output_token_count": 292, "latency": 4.838154077529907}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"2,7,11,15\", \"target\": \"9\"}"}], "input_token_count": 289, "output_token_count": 864, "latency": 14.227900743484497}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\", \"basicCredentials\": \"basicAuthCredentials\"}"}], "input_token_count": 335, "output_token_count": 388, "latency": 6.413902997970581}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\", \"builderExtractor\": \"builder -> builder.getConcurrency()\"}"}], "input_token_count": 332, "output_token_count": 672, "latency": 11.104768753051758}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 313, "output_token_count": 286, "latency": 4.7273149490356445}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 268, "output_token_count": 619, "latency": 10.109500885009766}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\": \"5\", \"leaseTime\": \"120\", \"unit\": \"SECONDS\"}"}], "input_token_count": 349, "output_token_count": 300, "latency": 4.928800106048584}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 286, "output_token_count": 294, "latency": 4.808139085769653}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\": \"cleanupTask\", \"delay\": \"300\", \"unit\": \"TimeUnit.SECONDS\"}"}], "input_token_count": 326, "output_token_count": 231, "latency": 3.7929725646972656}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\": \"AND\", \"destination\": \"user:online:both\", \"keys\": [\"user:online:today\", \"user:online:yesterday\"]}"}], "input_token_count": 378, "output_token_count": 1345, "latency": 21.972442150115967}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": \"[userID, 42, username, johndoe, isActive, true]\", \"state\": \"processingState\"}"}], "input_token_count": 312, "output_token_count": 1309, "latency": 21.36277174949646}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 281, "output_token_count": 291, "latency": 4.****************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"docFields\"}"}], "input_token_count": 256, "output_token_count": 1927, "latency": 31.***************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"logEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 284, "output_token_count": 360, "latency": 5.***************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\": \"shardA\", \"initializedShard\": \"shardB\"}"}], "input_token_count": 287, "output_token_count": 241, "latency": 3.**************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser\"}"}], "input_token_count": 232, "output_token_count": 611, "latency": 9.**************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\": \"usernameField.mapper()\", \"value\": \"'JohnDoe'\", \"caseInsensitive\": \"true\"}"}], "input_token_count": 318, "output_token_count": 401, "latency": 6.**************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 330, "output_token_count": 208, "latency": 3.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 325, "output_token_count": 152, "latency": 2.5052683353424072}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": \"5\"}"}], "input_token_count": 266, "output_token_count": 349, "latency": 5.687145948410034}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 312, "output_token_count": 482, "latency": 7.865184783935547}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESG...[remaining 1024-character Base64 string]...\"}"}], "input_token_count": 312, "output_token_count": 899, "latency": 14.663899898529053}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"new InetSocketAddress('************', 8080)\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 349, "output_token_count": 399, "latency": 6.51232385635376}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 309, "output_token_count": 257, "latency": 4.206177473068237}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 321, "output_token_count": 195, "latency": 3.1998848915100098}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\": \"taskVariable\", \"request\": \"usageRequest\", \"state\": \"clusterState\", \"listener\": \"actionListener\"}"}], "input_token_count": 385, "output_token_count": 695, "latency": 11.335429191589355}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"SAMLAssertionNode\", \"node_type\": \"Element.class\"}"}], "input_token_count": 294, "output_token_count": 264, "latency": 4.****************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 297, "output_token_count": 247, "latency": 4.***************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\", \"listener\": \"operationListener\"}"}], "input_token_count": 392, "output_token_count": 465, "latency": 7.**************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\": \"repositoriesService\", \"cache\": \"cacheService\", \"indexSettings\": \"indexSettingsForLogs\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"threadPool\": \"threadPool\", \"blobStoreCacheService\": \"blobStoreCacheService\", \"sharedBlobCacheService\": \"sharedBlobCacheService\"}"}], "input_token_count": 613, "output_token_count": 824, "latency": 13.***************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 343, "output_token_count": 389, "latency": 6.***************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\": \"yes\", \"defaultValue\": \"false\"}"}], "input_token_count": 298, "output_token_count": 408, "latency": 6.65687108039856}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\": \"{'name'='John Doe', 'age'=30, 'email'='<EMAIL>'}\", \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 369, "output_token_count": 1140, "latency": 18.607458353042603}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\": \"Terminal.systemTerminal()\", \"shardPath\": \"/var/data/elasticsearch/nodes/0/indices/1shard\", \"indexDirectory\": \"/var/data/elasticsearch/nodes/0/indices/1shard/index\"}"}], "input_token_count": 413, "output_token_count": 921, "latency": 15.049493551254272}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\"}"}], "input_token_count": 319, "output_token_count": 607, "latency": 9.901152849197388}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\": \"timestamp\", \"origin\": \"now\", \"scale\": \"10d\", \"offset\": \"2d\", \"decay\": \"0.5\"}"}], "input_token_count": 424, "output_token_count": 360, "latency": 5.90636682510376}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"queryType\": \"FLOAT\", \"from\": \"20.5\", \"to\": \"30.0\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 455, "output_token_count": 394, "latency": 6.451014518737793}
{"id": "java_63", "result": [{"withinQuery": "{\"field\": \"age\", \"from\": \"30\", \"to\": \"40\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 401, "output_token_count": 311, "latency": 5.0973780155181885}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": \"{\\\"format\\\": \\\"epoch_millis\\\"}\", \"onScriptError\": \"FAIL\"}"}], "input_token_count": 412, "output_token_count": 1334, "latency": 21.81001901626587}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\": \"xContentBuilderInstance\", \"params\": \"[true, true]\"}"}], "input_token_count": 311, "output_token_count": 1074, "latency": 17.521371841430664}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"onScriptError.IGNORE\"}"}], "input_token_count": 387, "output_token_count": 249, "latency": 4.081701278686523}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\": \"/Applications/PhotoEditor.app\", \"params\": \"{\\n  \\\"appName\\\": \\\"PhotoEditor\\\",\\n  \\\"imagesRoot\\\": \\\"src/main/resources/dmg-assets\\\",\\n  \\\"background\\\": \\\"background.png\\\",\\n  \\\"installDir\\\": \\\"/Applications\\\",\\n  \\\"volumeUrl\\\": \\\"vol:/Applications\\\",\\n  \\\"windowSize\\\": \\\"480\\\",\\n  \\\"iconSize\\\": \\\"128\\\"\\n}\"}"}], "input_token_count": 317, "output_token_count": 1233, "latency": 20.118852853775024}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": \"new java.util.HashMap<String, Object>() {{ put(\\\"appImagePath\\\", \\\"/Applications/MyApp.app\\\"); put(\\\"appName\\\", \\\"MyApp\\\"); }}\"}"}], "input_token_count": 272, "output_token_count": 466, "latency": 7.5860841274261475}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"start\": \"2\", \"end\": \"5\"}"}], "input_token_count": 354, "output_token_count": 335, "latency": 5.477488994598389}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\": \"new QName('http://www.example.com', 'item', 'ex')\", \"augs\": \"augmentations\"}"}], "input_token_count": 323, "output_token_count": 438, "latency": 7.152678966522217}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 350, "output_token_count": 162, "latency": 2.665386199951172}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}], "input_token_count": 353, "output_token_count": 1960, "latency": 32.12089920043945}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\": \"UTF-8\", \"allowJavaNames\": \"true\"}"}], "input_token_count": 271, "output_token_count": 269, "latency": 4.389201879501343}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"false\"}"}], "input_token_count": 335, "output_token_count": 4304, "latency": 71.17273664474487}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 287, "output_token_count": 389, "latency": 6.344830274581909}
{"id": "java_76", "result": [{"Intro_step": "{\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 260, "output_token_count": 146, "latency": 2.401489019393921}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 324, "output_token_count": 595, "latency": 9.723140478134155}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"quiet\", \"otherDependents\": [\"verbose\"]}"}], "input_token_count": 289, "output_token_count": 1715, "latency": 28.244653701782227}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 315, "output_token_count": 413, "latency": 6.864185094833374}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 283, "output_token_count": 459, "latency": 7.571081638336182}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\": \"humongous-test-case.json\", \"doGC\": \"customGCRunnable\", \"checker\": \"referenceChecker\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": \"[\\\"GC pause\\\"]\", \"shouldNotContain\": \"[\\\"OutOfMemoryError\\\"]\"}"}], "input_token_count": 536, "output_token_count": 1918, "latency": 31.970696449279785}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"testArgs\", \"out\": \"System.out\"}"}], "input_token_count": 326, "output_token_count": 773, "latency": 12.70155644416809}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": \"-waitTime,120000,-iterations,500\", \"out\": \"System.out\"}"}], "input_token_count": 356, "output_token_count": 1583, "latency": 25.92011046409607}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 303, "output_token_count": 300, "latency": 4.906155109405518}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"[\\\"/path/to/classes\\\", \\\"60\\\"]\", \"out\": \"logStream\"}"}], "input_token_count": 353, "output_token_count": 1590, "latency": 26.350268363952637}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": [\"-v\", \"--no-strict\"], \"out\": \"debugOutput\"}"}], "input_token_count": 321, "output_token_count": 1609, "latency": 26.69145631790161}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"1\", \"property\": \"testProperty=deathEvent001\"}"}], "input_token_count": 290, "output_token_count": 714, "latency": 11.678220510482788}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\": \"mainThread\", \"testedClass\": \"com.example.MainClass\", \"suspendPolicy\": \"1\", \"property\": \"testProperty\"}"}], "input_token_count": 387, "output_token_count": 610, "latency": 9.998666524887085}
{"id": "java_89", "result": [{"runThis": "{\"argv\": \"[-waitTime, 2, -debuggeeName, TestDebuggee]\", \"out\": \"testLogStream\"}"}], "input_token_count": 337, "output_token_count": 3620, "latency": 59.770912170410156}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": \"[-v, -p]\", \"out\": \"System.out\"}"}], "input_token_count": 313, "output_token_count": 2887, "latency": 47.508647203445435}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": \"[\\\"suspend\\\", \\\"log\\\"]\", \"out\": \"debugLog\"}"}], "input_token_count": 309, "output_token_count": 379, "latency": 6.198416233062744}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\"}"}], "input_token_count": 292, "output_token_count": 364, "latency": 5.944812297821045}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 301, "output_token_count": 270, "latency": 4.409572124481201}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\"}"}], "input_token_count": 270, "output_token_count": 198, "latency": 3.244410991668701}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\": \"ExportInlinedObject1Instance\", \"argument\": \"HelloWorld\", \"node\": \"InlinableNodeInstance\", \"library\": \"NodeLibraryInstance\"}"}], "input_token_count": 391, "output_token_count": 323, "latency": 5.293628215789795}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 352, "output_token_count": 492, "latency": 8.034375190734863}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 288, "output_token_count": 213, "latency": 3.4828953742980957}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 283, "output_token_count": 344, "latency": 5.623467206954956}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 297, "output_token_count": 295, "latency": 4.831490755081177}
