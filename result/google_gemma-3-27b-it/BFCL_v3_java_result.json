{"id": "java_0", "result": "[GeometryPresentation.createPresentation(controller='mapController', parent='mapArea')]", "input_token_count": 434, "output_token_count": 18, "latency": 0.45117759704589844}
{"id": "java_1", "result": "[SQLCompletionAnalyzer.makeProposalsFromObject(object='Customers', useShortName='true', params='{limit=50, schemaFilter=public}')]", "input_token_count": 479, "output_token_count": 36, "latency": 0.8704156875610352}
{"id": "java_2", "result": "[FireBirdUtils.getViewSourceWithHeader(monitor='dbMonitor', view='EmployeeView', source=\"SELECT * FROM Employee WHERE status = 'active'\")]", "input_token_count": 491, "output_token_count": 34, "latency": 0.8260593414306641}
{"id": "java_3", "result": "[DB2Tablespace.resolveTablespaceReference(monitor='dbMonitor', dataSource='db2DataSource', reference='USERSPACE1')]", "input_token_count": 498, "output_token_count": 29, "latency": 0.7184903621673584}
{"id": "java_4", "result": "[DB2ViewBaseDepCache.prepareObjectsStatement(session='jdbcSession', db2ViewBase='EmployeeView')]", "input_token_count": 446, "output_token_count": 26, "latency": 0.6364467144012451}
{"id": "java_5", "result": "[PlainTextPresentation.createPresentation(controller='dataController', parent='compositeParent')]", "input_token_count": 457, "output_token_count": 18, "latency": 0.45081496238708496}
{"id": "java_6", "result": "[SpreadsheetPresentation.refreshData(refreshMetadata='true', append='true', keepState='true')]", "input_token_count": 467, "output_token_count": 23, "latency": 0.5665960311889648}
{"id": "java_7", "result": "[EFSNIOResource.copy(destination='/backup/data.txt', force='true', monitor='progressTracker')]", "input_token_count": 506, "output_token_count": 28, "latency": 0.6965923309326172}
{"id": "java_8", "result": "[EFSNIOFile.setContents(source='fileStream', force='true', keepHistory='false', monitor='progressMonitor')]", "input_token_count": 536, "output_token_count": 30, "latency": 0.7477824687957764}
{"id": "java_9", "result": "[writeMultiPoint(multiPoint='[[1, 2], [3, 4], [5, 6], [7, 8], [9, 10]]', xyzmMode='XyzmMode.XYZ', buffer='ByteBuffer.allocate(1024)')]", "input_token_count": 556, "output_token_count": 64, "latency": 1.5457158088684082}
{"id": "java_10", "result": "[JNIBridge.setLauncherInfo(launcher=\"/usr/local/bin/dbeaver\", name=\"DBeaverLauncher\")]", "input_token_count": 417, "output_token_count": 29, "latency": 0.7049555778503418}
{"id": "java_11", "result": "[BasePolicyDataProvider.getRegistryPolicyValue(root='HKEY_LOCAL_MACHINE', property='EnableExtensions')]", "input_token_count": 452, "output_token_count": 25, "latency": 0.614363431930542}
{"id": "java_12", "result": "[ExasolExecutionContext.setCurrentSchema(monitor='progressMonitor', schemaName='AnalyticsDB')]", "input_token_count": 433, "output_token_count": 21, "latency": 0.5200467109680176}
{"id": "java_13", "result": "[AltibaseGrantee.prepareObjectsStatement(session='JDBC_session', owner='JohnDoe')]", "input_token_count": 438, "output_token_count": 22, "latency": 0.5430262088775635}
{"id": "java_14", "result": "[FunGameBase.onFinish(layout='gameLayout', success='true')]", "input_token_count": 430, "output_token_count": 18, "latency": 0.4484214782714844}
{"id": "java_15", "result": "[Res9patchStreamDecoder.decode(input='imageInputStream', out='imageOutputStream')]", "input_token_count": 448, "output_token_count": 20, "latency": 0.4958064556121826}
{"id": "java_16", "result": "[InsnDecoder.invokePolymorphic(insn='instructionData', isRange='true')]", "input_token_count": 435, "output_token_count": 22, "latency": 0.5420634746551514}
{"id": "java_17", "result": "[GenericTypesVisitor.attachGenericTypesInfo(mth=\"initMethod\", insn=\"newConstructorInsn\")]", "input_token_count": 451, "output_token_count": 25, "latency": 0.6124732494354248}
{"id": "java_18", "result": "[SysRoleController.queryPageRoleCount(pageNo='3', pageSize='20')]", "input_token_count": 445, "output_token_count": 21, "latency": 0.5194907188415527}
{"id": "java_19", "result": "[PersonController.personal(model='webModel', request='userRequest')]", "input_token_count": 447, "output_token_count": 17, "latency": 0.4265291690826416}
{"id": "java_20", "result": "[HbaseAdapter.updateConfig(fileName='user-mapping.yml', config='newMappingConfig')]", "input_token_count": 440, "output_token_count": 23, "latency": 0.5667343139648438}
{"id": "java_21", "result": "[SessionHandler.exceptionCaught(ctx='nettyChannelContext', e='ioExceptionEvent')]", "input_token_count": 444, "output_token_count": 20, "latency": 0.4967620372772217}
{"id": "java_22", "result": "[PmsProductServiceImpl.updateNewStatus(ids='[101, 202, 303]', newStatus='2')]", "input_token_count": 447, "output_token_count": 33, "latency": 0.7991962432861328}
{"id": "java_23", "result": "[SmsHomeNewProductServiceImpl.list(productName='LED TV', recommendStatus='1', pageSize='20', pageNum='3')]", "input_token_count": 514, "output_token_count": 32, "latency": 0.792454719543457}
{"id": "java_24", "result": "[PmsProductCategoryController.updateShowStatus(ids=\"[101, 102, 103]\", showStatus=\"0\")]", "input_token_count": 458, "output_token_count": 34, "latency": 0.8241653442382812}
{"id": "java_25", "result": "[SmsHomeRecommendSubjectController.updateSort(id='42', sort='5')]", "input_token_count": 422, "output_token_count": 21, "latency": 0.5184762477874756}
{"id": "java_26", "result": "[ProxyConnection.prepareCall(sql='CALL totalSales(?)', resultSetType='ResultSet.TYPE_SCROLL_INSENSITIVE', concurrency='ResultSet.CONCUR_READ_ONLY', holdability='ResultSet.CLOSE_CURSORS_AT_COMMIT')]", "input_token_count": 580, "output_token_count": 55, "latency": 1.3359100818634033}
{"id": "java_27", "result": "[TwoSum.twoSum(nums=\"[2, 7, 11, 15]\", target=\"9\")]", "input_token_count": 437, "output_token_count": 28, "latency": 0.6835513114929199}
{"id": "java_28", "result": "[configStorage.dynamicCredentialsScheduledExecutorService(credentialsFile='es_credentials.properties', credentialsRefreshInterval='30', basicCredentials=basicAuthCredentials)]", "input_token_count": 485, "output_token_count": 34, "latency": 0.8250250816345215}
{"id": "java_29", "result": "[propertyTransferredToCollectorBuilder(property='zipkin.collector.activemq.concurrency', value='10', builderExtractor='builder.concurrency')]", "input_token_count": 483, "output_token_count": 35, "latency": 0.848280668258667}
{"id": "java_30", "result": "[RedissonAsyncCache.putIfAbsent(key='answer', value='42')]", "input_token_count": 463, "output_token_count": 20, "latency": 0.49726343154907227}
{"id": "java_31", "result": "[RedissonRx.getQueue(name='taskQueue', codec='jsonCodec')]", "input_token_count": 415, "output_token_count": 19, "latency": 0.47120141983032227}
{"id": "java_32", "result": "[RedissonPermitExpirableSemaphore.tryAcquireAsync(waitTime='5', leaseTime='120', unit='SECONDS')]", "input_token_count": 496, "output_token_count": 31, "latency": 0.7555196285247803}
{"id": "java_33", "result": "[RedissonMapCache.putOperationAsync(key='employee:1234', value='John Doe')]", "input_token_count": 436, "output_token_count": 25, "latency": 0.6123044490814209}
{"id": "java_34", "result": "[ServiceManager.newTimeout(task='cleanupTask', delay='300', unit='TimeUnit.SECONDS')]", "input_token_count": 475, "output_token_count": 25, "latency": 0.6132400035858154}
{"id": "java_35", "result": "[RedissonConnection.bitOp(op='BitOperation.AND', destination='[\"user:online:both\"]', keys='[\"user:online:today\", \"user:online:yesterday\"]')]", "input_token_count": 533, "output_token_count": 44, "latency": 1.0749506950378418}
{"id": "java_36", "result": "[ObjectMapEntryReplayDecoder.decode(parts=\"['userID', 42, 'username', 'johndoe', 'isActive', true]\", state=\"processingState\")]", "input_token_count": 460, "output_token_count": 39, "latency": 0.940213680267334}
{"id": "java_37", "result": "[ConsoleAnnotator.annotate(context='jenkinsBuild', text='buildOutput')]", "input_token_count": 425, "output_token_count": 18, "latency": 0.****************}
{"id": "java_38", "result": "[NestedValueFetcher.createSourceMapStub(filteredSource='{\"name\":{},\"address\":{}}')]", "input_token_count": 404, "output_token_count": 24, "latency": 0.****************}
{"id": "java_39", "result": "[NodeIdConverter.format(event='logEvent', toAppendTo='logBuilder')]", "input_token_count": 433, "output_token_count": 20, "latency": 0.****************}
{"id": "java_40", "result": "[RoutingNodesChangedObserver.shardInitialized(unassignedShard='shardA', initializedShard='shardB')]", "input_token_count": 428, "output_token_count": 23, "latency": 0.**************}
{"id": "java_41", "result": "[SearchHit.declareInnerHitsParseFields(parser='searchHitParser')]", "input_token_count": 379, "output_token_count": 17, "latency": 0.*****************}
{"id": "java_42", "result": "[TermQueryBuilderTests.termQuery(mapper='usernameField', value='JohnDoe', caseInsensitive='true')]", "input_token_count": 469, "output_token_count": 26, "latency": 0.****************}
{"id": "java_43", "result": "[SecureMockMaker.createSpy(settings='mockSettings', handler='mockHandler', object='testObject')]", "input_token_count": 478, "output_token_count": 24, "latency": 0.****************}
{"id": "java_44", "result": "[DesAPITest.init(crypt='DESede', mode='CBC', padding='PKCS5Padding')]", "input_token_count": 470, "output_token_count": 24, "latency": 0.****************}
{"id": "java_45", "result": "[Basic.checkSizes(environ='envVariables', size='5')]", "input_token_count": 413, "output_token_count": 16, "latency": 0.4020860195159912}
{"id": "java_46", "result": "[MethodInvokeTest.checkInjectedInvoker(csm='csmInstance', expected='MyExpectedClass.class')]", "input_token_count": 462, "output_token_count": 27, "latency": 0.6598851680755615}
{"id": "java_47", "result": "[LargeHandshakeTest.format(name='CERTIFICATE', value='MIIFdTCCBF2gAwIBAgISESG... (990 more characters) ...')]\n", "input_token_count": 464, "output_token_count": 41, "latency": 0.9857745170593262}
{"id": "java_48", "result": "[CookieHeaderTest.create(sa='192.168.1.10:8080', sslContext='testSSLContext')]", "input_token_count": 498, "output_token_count": 35, "latency": 0.8597502708435059}
{"id": "java_49", "result": "[Http2TestExchangeImpl.sendResponseHeaders(rCode='404', responseLength='1500')]", "input_token_count": 456, "output_token_count": 27, "latency": 0.6600167751312256}
{"id": "java_50", "result": "[TransformIndexerStateTests.doDeleteByQuery(deleteByQueryRequest='deleteQueryRequest', responseListener='testListener')]", "input_token_count": 467, "output_token_count": 27, "latency": 0.6600790023803711}
{"id": "java_51", "result": "[CCRUsageTransportAction.masterOperation(task='gather_ccr_usage_stats', request='usageRequest', state='clusterState', listener='actionListener')]", "input_token_count": 532, "output_token_count": 36, "latency": 0.8884828090667725}
{"id": "java_52", "result": "[SamlObjectSignerTests.getChildren(node='SAMLAssertionNode', node_type='Element.class')]", "input_token_count": 442, "output_token_count": 26, "latency": 0.****************}
{"id": "java_53", "result": "[VotingOnlyNodePlugin.fullMasterWithOlderState(localAcceptedTerm='42', localAcceptedVersion='7')]", "input_token_count": 445, "output_token_count": 26, "latency": 0.***************}
{"id": "java_54", "result": "[AbstractTransportSearchableSnapshotsAction.shardOperation(request='snapshotRequest', shardRouting='shardRouteInfo', task='snapshotTask', listener='operationListener')]", "input_token_count": 539, "output_token_count": 35, "latency": 0.****************}
{"id": "java_55", "result": "[SearchableSnapshotDirectory.create(repositories='repositoriesService', cache='cacheService', indexSettings='indexSettingsForLogs', shardPath='/data/nodes/0/indices/logs/5', currentTimeNanosSupplier='currentTimeNanos', threadPool='threadPool', blobStoreCacheService='blobStoreCacheService', sharedBlobCacheService='sharedBlobCacheService')]", "input_token_count": 759, "output_token_count": 77, "latency": 1.****************}
{"id": "java_56", "result": "[CCSDuelIT.parseEntity(entity='httpResponseEntity', entityParser='responseParser', parserConfig='defaultParserConfig')]", "input_token_count": 489, "output_token_count": 27, "latency": 0.****************}
{"id": "java_57", "result": "[Booleans.parseBooleanLenient(value='yes', defaultValue='false')]", "input_token_count": 444, "output_token_count": 18, "latency": 0.44988203048706055}
{"id": "java_58", "result": "[XContentBuilder.map(values='{\\\\\"name\\\\\": \\\\\"John Doe\\\\\", \\\\\"age\\\\\": 30, \\\\\"email\\\\\": \\\\\"<EMAIL>\\\\\" }', ensureNoSelfReferences='true', writeStartAndEndHeaders='true')]", "input_token_count": 517, "output_token_count": 62, "latency": 1.495506763458252}
{"id": "java_59", "result": "[TruncateTranslogAction.execute(terminal='terminal', shardPath='/var/data/elasticsearch/nodes/0/indices/1shard', indexDirectory='/var/data/elasticsearch/nodes/0/indices/1shard/index')]", "input_token_count": 565, "output_token_count": 52, "latency": 1.264453649520874}
{"id": "java_60", "result": "[NestedQueryBuilder.doBuild(parentSearchContext='mainSearchContext', innerHitsContext='hitsContext')]", "input_token_count": 469, "output_token_count": 24, "latency": 0.5911190509796143}
{"id": "java_61", "result": "[ScoreFunctionBuilders.exponentialDecayFunction(fieldName='timestamp', origin='now', scale='10d', offset='2d', decay='0.5')]", "input_token_count": 569, "output_token_count": 36, "latency": 0.8891294002532959}
{"id": "java_62", "result": "[dvRangeQuery(field='temperature', queryType='FLOAT', from='20.5', to='30.0', includeFrom='true', includeTo='false')]", "input_token_count": 600, "output_token_count": 39, "latency": 0.9613726139068604}
{"id": "java_63", "result": "[withinQuery(field=\"age\", from=\"30\", to=\"40\", includeFrom=\"true\", includeTo=\"false\")]", "input_token_count": 547, "output_token_count": 29, "latency": 0.7246155738830566}
{"id": "java_64", "result": "[DateScriptFieldType.createFieldType(name='timestamp', factory='dateFactory', script='dateScript', meta='{\"format\": \"epoch_millis\"}', onScriptError='FAIL')]", "input_token_count": 562, "output_token_count": 42, "latency": 1.0307633876800537}
{"id": "java_65", "result": "[RootObjectMapper.doXContent(builder='xContentBuilderInstance', params='[include_defaults=true, skip_runtime_fields=true]')]", "input_token_count": 456, "output_token_count": 34, "latency": 0.8233349323272705}
{"id": "java_66", "result": "[CompositeRuntimeField.createChildRuntimeField(parserContext='mappingParserContext', parent='compositeField1', parentScriptFactory='compositeScriptFactory', onScriptError='onScriptError.IGNORE')]", "input_token_count": 537, "output_token_count": 42, "latency": 1.029498815536499}
{"id": "java_67", "result": "[MacDmgBundler.prepareDMGSetupScript(appLocation=\"/Applications/PhotoEditor.app\", params='{\"appName\": \"PhotoEditor\", \"imagesRoot\": \"/path/to/images\", \"backgroundImageFolder\": \"/path/to/background/images\"}')]", "input_token_count": 463, "output_token_count": 58, "latency": 1.3837449550628662}
{"id": "java_68", "result": "[MacBaseInstallerBundler.validateAppImageAndBundeler(params='{\"appImagePath\": \"/Applications/MyApp.app\", \"appName\": \"MyApp\"}')]", "input_token_count": 419, "output_token_count": 35, "latency": 0.8455324172973633}
{"id": "java_69", "result": "[DurationImpl.alignSigns(buf='durations', start='2', end='5')]", "input_token_count": 498, "output_token_count": 21, "latency": 0.53216552734375}
{"id": "java_70", "result": "[XMLNamespaceBinder.endElement(element='ex:item', augs='augmentations')]", "input_token_count": 474, "output_token_count": 21, "latency": 0.52048659324646}
{"id": "java_71", "result": "[CoroutineManager.co_exit_to(arg_object='resultData', thisCoroutine='5', toCoroutine='10')]", "input_token_count": 513, "output_token_count": 29, "latency": 0.7214224338531494}
{"id": "java_72", "result": "[ToTextStream.characters(ch='textBuffer', start='5', length='10')]", "input_token_count": 500, "output_token_count": 22, "latency": 0.555980920791626}
{"id": "java_73", "result": "[Encodings.getEncodingInfo(encoding='UTF-8', allowJavaNames='true')]", "input_token_count": 418, "output_token_count": 21, "latency": 0.5182521343231201}
{"id": "java_74", "result": "[BaseMarkupSerializer.surrogates(high='55357', low='56832', inContent='false')]", "input_token_count": 483, "output_token_count": 31, "latency": 0.7540597915649414}
{"id": "java_75", "result": "[JdkXmlFeatures.getSystemProperty(feature='XML_SECURITY', sysPropertyName='enableXmlSecurityFeature')]", "input_token_count": 436, "output_token_count": 24, "latency": 0.5896506309509277}
{"id": "java_76", "result": "[Intro.step(w=\"800\", h=\"600\")]", "input_token_count": 408, "output_token_count": 18, "latency": 0.44760704040527344}
{"id": "java_77", "result": "[JndiLoginModule.verifyPassword(encryptedPassword='e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', password='P@ssw0rd!')]", "input_token_count": 471, "output_token_count": 82, "latency": 1.9475524425506592}
{"id": "java_78", "result": "[OptionSpecBuilder.requiredUnless(dependent='output-format', otherDependents='quiet,verbose')]", "input_token_count": 439, "output_token_count": 23, "latency": 0.5679893493652344}
{"id": "java_79", "result": "[SAXFilterFactoryImpl.resolveEntity(publicid='1234', sysId='http://astro.com/stylesheets/toptemplate')]", "input_token_count": 466, "output_token_count": 34, "latency": 0.8239469528198242}
{"id": "java_80", "result": "[RegexConstraint.initIRPattern(category='failOn', ruleIdx='42')]", "input_token_count": 430, "output_token_count": 20, "latency": 0.4953632354736328}
{"id": "java_81", "result": "[TestObjectGraphAfterGC.doTesting(testcaseData='humongous-test-case.json', doGC='customGarbageCollector', checker='referenceChecker', gcLogName='gc-analysis.log', shouldContain='[\\\"GC pause\\\"]', shouldNotContain='[\\\"OutOfMemoryError\\\"]')]", "input_token_count": 688, "output_token_count": 68, "latency": 1.6689302921295166}
{"id": "java_82", "result": "[clear001a.runIt(args='testArgs', out='System.out')]", "input_token_count": 475, "output_token_count": 22, "latency": 0.5446569919586182}
{"id": "java_83", "result": "[thrcputime002.runIt(argv='-waitTime, 120000, -iterations, 500', out='System.out')]", "input_token_count": 505, "output_token_count": 40, "latency": 0.9786531925201416}
{"id": "java_84", "result": "[checkInnerFields(redefCls='myRedefClass', expValue='100'), checkInnerFields(redefCls='myRedefClass', expValue='100'), checkInnerFields(redefCls='myRedefClass', expValue='100')]", "input_token_count": 453, "output_token_count": 65, "latency": 1.5459880828857422}
{"id": "java_85", "result": "[classfloadhk005.runIt(argv='[\"/path/to/classes\", \"60\"]', out='logStream')]", "input_token_count": 505, "output_token_count": 33, "latency": 0.8147876262664795}
{"id": "java_86", "result": "[argumenttypes001.runThis(argv='[\"-v\", \"--no-strict\"]', out='debugOutput')]", "input_token_count": 471, "output_token_count": 28, "latency": 0.6838774681091309}
{"id": "java_87", "result": "[suspendpolicy017.settingVMDeathRequest(suspendPolicy='EVENT_THREAD', property='deathEvent001')]", "input_token_count": 442, "output_token_count": 30, "latency": 0.7292289733886719}
{"id": "java_88", "result": "[filter_s002.setting22MethodEntryRequest(thread='mainThread', testedClass='com.example.MainClass', suspendPolicy='EventRequest.SUSPEND_ALL', property='testProperty')]", "input_token_count": 541, "output_token_count": 47, "latency": 1.1477437019348145}
{"id": "java_89", "result": "[runThis(argv='-waitTime, 120, -debuggeeName, TestDebuggee', out='testLogStream')]", "input_token_count": 484, "output_token_count": 30, "latency": 0.7313172817230225}
{"id": "java_90", "result": "[sourcepaths002.runIt(args='[\"-v\", \"-p\"]', out='System.out')]", "input_token_count": 462, "output_token_count": 27, "latency": 0.6602365970611572}
{"id": "java_91", "result": "[invokemethod007.runIt(args='[\"suspend\", \"log\"]', out='debugLog')]", "input_token_count": 461, "output_token_count": 28, "latency": 0.6831471920013428}
{"id": "java_92", "result": "[ClassFileFinder.findClassFile(name='com.example.MyClass', classPath='/usr/local/classes:/home/<USER>/java/libs')]", "input_token_count": 447, "output_token_count": 36, "latency": 0.8687357902526855}
{"id": "java_93", "result": "[AbstractJarAgent.runJarAgent(options='trace log', inst='appInstrumentation')]", "input_token_count": 448, "output_token_count": 20, "latency": 0.49626636505126953}
{"id": "java_94", "result": "[NFILibrary.isMemberReadable(symbol='getVersion')]", "input_token_count": 416, "output_token_count": 14, "latency": 0.3541264533996582}
{"id": "java_95", "result": "[ExportNodeTest.doGeneric(receiver='ExportInlinedObject1Instance', argument='HelloWorld', node='InlinableNodeInstance', library='NodeLibraryInstance')]", "input_token_count": 538, "output_token_count": 36, "latency": 0.8871467113494873}
{"id": "java_96", "result": "[InstrumentableProcessor.createCallConverter(converterMethod='convertValue', frameParameterName='frameVar', returnName='returnValueCode')]", "input_token_count": 501, "output_token_count": 29, "latency": 0.7210903167724609}
{"id": "java_97", "result": "[FlatNodeGenFactory.generateIntrospectionInfo(clazz='NodeClass', inlined='false')]", "input_token_count": 430, "output_token_count": 22, "latency": 0.5431618690490723}
{"id": "java_98", "result": "[LoopConditionProfile.calculateProbability(trueCountLocal='150', falseCountLocal='50')]", "input_token_count": 430, "output_token_count": 24, "latency": 0.5889544486999512}
{"id": "java_99", "result": "[LibraryExport.createDelegate(factory='myFactory', delegate='existingDelegate')]", "input_token_count": 445, "output_token_count": 18, "latency": 0.44971394538879395}
