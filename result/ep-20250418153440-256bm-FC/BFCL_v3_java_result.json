{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\":\"mapController\",\"parent\":\"mapArea\"}"}], "input_token_count": 258, "output_token_count": 152, "latency": 4.762237310409546}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\":\"Customers\",\"useShortName\":\"true\",\"params\":\"{limit=50, schemaFilter=public}\"}"}], "input_token_count": 305, "output_token_count": 265, "latency": 9.066275358200073}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": " {\"monitor\": \"dbMonitor\", \"view\": \"EmployeeView\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 316, "output_token_count": 197, "latency": 5.707367897033691}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\":\"dbMonitor\",\"dataSource\":\"db2DataSource\",\"reference\":\"USERSPACE1\"}"}], "input_token_count": 325, "output_token_count": 357, "latency": 10.981565237045288}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": " {\"session\": \"jdbcSession\", \"db2ViewBase\": \"new DB2ViewBase(\\\"HR\\\", \\\"EmployeeView\\\")\"}"}], "input_token_count": 276, "output_token_count": 487, "latency": 18.310933828353882}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": " {\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 281, "output_token_count": 213, "latency": 6.883435249328613}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\":\"true\",\"append\":\"true\",\"keepState\":\"true\"}"}], "input_token_count": 292, "output_token_count": 218, "latency": 5.6883909702301025}
{"id": "java_7", "result": [{"EFSNIOResource_copy": " {\"destination\": \"java.nio.file.Paths.get('/backup/data.txt')\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 333, "output_token_count": 331, "latency": 11.615709781646729}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\":\"fileStream\",\"force\":\"true\",\"keepHistory\":\"false\",\"monitor\":\"progressMonitor\"}"}], "input_token_count": 360, "output_token_count": 302, "latency": 7.8243138790130615}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\":\"new MultiPoint(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10))\",\"xyzmMode\":\"XyzmMode.XYZ\",\"buffer\":\"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 384, "output_token_count": 363, "latency": 11.643703699111938}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": " {\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 243, "output_token_count": 153, "latency": 5.57524561882019}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": " {\"root\": \"HKEY_LOCAL_MACHINE\", \"property\": \"EnableExtensions\"}"}], "input_token_count": 275, "output_token_count": 121, "latency": 5.6619415283203125}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": " {\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 262, "output_token_count": 170, "latency": 5.129140853881836}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\":\"JDBC_session\",\"owner\":\"JohnDoe\"}"}], "input_token_count": 273, "output_token_count": 171, "latency": 4.743708372116089}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\":\"gameLayout\",\"success\":\"true\"}"}], "input_token_count": 255, "output_token_count": 190, "latency": 5.984113693237305}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\":\"imageInputStream\",\"out\":\"imageOutputStream\"}"}], "input_token_count": 272, "output_token_count": 186, "latency": 9.735722541809082}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": " {\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 264, "output_token_count": 274, "latency": 13.108557224273682}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": " {\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 276, "output_token_count": 197, "latency": 5.940246820449829}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\":\"3\",\"pageSize\":\"20\"}"}], "input_token_count": 270, "output_token_count": 157, "latency": 5.496369361877441}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\":\"webModel\",\"request\":\"userRequest\"}"}], "input_token_count": 273, "output_token_count": 179, "latency": 8.750216484069824}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\":\"user-mapping.yml\",\"config\":\"newMappingConfig\"}"}], "input_token_count": 265, "output_token_count": 147, "latency": 6.780938386917114}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": " {\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 271, "output_token_count": 286, "latency": 10.174934387207031}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\":\"[101, 202, 303]\",\"newStatus\":\"2\"}"}], "input_token_count": 271, "output_token_count": 368, "latency": 13.250438928604126}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": " {\"productName\": \"%LED TV%\", \"recommendStatus\": \"1\", \"pageSize\": \"20\", \"pageNum\": \"3\"}"}], "input_token_count": 347, "output_token_count": 233, "latency": 8.9855375289917}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\":\"[101, 102, 103]\",\"showStatus\":\"0\"}"}], "input_token_count": 279, "output_token_count": 208, "latency": 6.6496522426605225}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": " {\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 246, "output_token_count": 163, "latency": 5.160208702087402}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": " {\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"1004\", \"concurrency\": \"1007\", \"holdability\": \"2\"}"}], "input_token_count": 412, "output_token_count": 376, "latency": 12.832116603851318}
{"id": "java_27", "result": [{"TwoSum_twoSum": " {\"nums\": \"[2, 7, 11, 15]\", \"target\": \"9\"}"}], "input_token_count": 261, "output_token_count": 144, "latency": 8.27412486076355}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\":\"es_credentials.properties\",\"credentialsRefreshInterval\":\"30\",\"basicCredentials\":\"basicAuthCredentials\"}"}], "input_token_count": 314, "output_token_count": 220, "latency": 10.052289485931396}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\":\"zipkin.collector.activemq.concurrency\",\"value\":\"10\",\"builderExtractor\":\"builder -> builder.getConcurrency()\"}"}], "input_token_count": 304, "output_token_count": 286, "latency": 10.78732681274414}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": " {\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 295, "output_token_count": 199, "latency": 6.205195188522339}
{"id": "java_31", "result": [{"RedissonRx_getQueue": " {\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 242, "output_token_count": 135, "latency": 3.8842508792877197}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\":\"5\",\"leaseTime\":\"120\",\"unit\":\"SECONDS\"}"}], "input_token_count": 330, "output_token_count": 399, "latency": 11.616811752319336}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\":\"employee:1234\",\"value\":\"John Doe\"}"}], "input_token_count": 261, "output_token_count": 164, "latency": 5.987239122390747}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": " {\"task\": \"cleanupTask\", \"delay\": \"5\", \"unit\": \"TimeUnit.MINUTES\"}"}], "input_token_count": 301, "output_token_count": 590, "latency": 17.88208532333374}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": " {\"op\": \"BitOperation.AND\", \"destination\": \"[user:online:both]\", \"keys\": \"[user:online:today, user:online:yesterday]\"}"}], "input_token_count": 357, "output_token_count": 485, "latency": 15.750673770904541}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": " {\"parts\": \"['userID', 42, 'username', 'johndoe', 'isActive', true]\", \"state\": \"processingState\"}"}], "input_token_count": 286, "output_token_count": 223, "latency": 6.904091119766235}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\":\"jenkinsBuild\",\"text\":\"buildOutput\"}"}], "input_token_count": 256, "output_token_count": 113, "latency": 4.***************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": " {\"filteredSource\": \"{name=, address=}\"}"}], "input_token_count": 234, "output_token_count": 191, "latency": 6.***************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\":\"logEvent\",\"toAppendTo\":\"logBuilder\"}"}], "input_token_count": 258, "output_token_count": 181, "latency": 4.****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\":\"shardA\",\"initializedShard\":\"shardB\"}"}], "input_token_count": 265, "output_token_count": 263, "latency": 9.***************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": " {\"parser\": \"searchHitParser\"}"}], "input_token_count": 206, "output_token_count": 146, "latency": 4.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\":\"usernameField\",\"value\":\"JohnDoe\",\"caseInsensitive\":\"true\"}"}], "input_token_count": 298, "output_token_count": 323, "latency": 12.***************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": " {\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 305, "output_token_count": 251, "latency": 7.***************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\":\"DESede\",\"mode\":\"CBC\",\"padding\":\"PKCS5Padding\"}"}], "input_token_count": 299, "output_token_count": 143, "latency": 5.167034864425659}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\":\"envVariables\",\"size\":\"5\"}"}], "input_token_count": 239, "output_token_count": 153, "latency": 5.186254978179932}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": " {\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 287, "output_token_count": 256, "latency": 7.252756595611572}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\":\"CERTIFICATE\",\"value\":\"MIIFdTCCBF2gAwIBAgISESG\uff08\u6b64\u5904\u4e3a\u5b8c\u6574\u76841024\u5b57\u7b26Base64\u5b57\u7b26\u4e32\uff09\"}"}], "input_token_count": 292, "output_token_count": 194, "latency": 8.117772340774536}
{"id": "java_48", "result": [{"CookieHeaderTest_create": " {\"sa\": \"new java.net.InetSocketAddress(\\\"************\\\", 8080)\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 326, "output_token_count": 228, "latency": 7.93781590461731}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\":\"404\",\"responseLength\":\"1500\"}"}], "input_token_count": 281, "output_token_count": 187, "latency": 6.413327693939209}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": " {\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 295, "output_token_count": 210, "latency": 6.691343307495117}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": " {\"task\": \"CCRUsageTask\", \"request\": \"usageRequest\", \"state\": \"clusterState\", \"listener\": \"actionListener\"}"}], "input_token_count": 364, "output_token_count": 249, "latency": 8.306419372558594}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\":\"SAMLAssertionNode\",\"node_type\":\"Element.class\"}"}], "input_token_count": 267, "output_token_count": 145, "latency": 4.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": " {\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 269, "output_token_count": 198, "latency": 5.***************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": " {\"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\", \"listener\": \"operationListener\"}"}], "input_token_count": 378, "output_token_count": 191, "latency": 6.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\":\"repositoriesService\",\"cache\":\"cacheService\",\"indexSettings\":\"indexSettingsForLogs\",\"shardPath\":\"/data/nodes/0/indices/logs/5\",\"currentTimeNanosSupplier\":\"currentTimeNanos\",\"threadPool\":\"threadPool\",\"blobStoreCacheService\":\"blobStoreCacheService\",\"sharedBlobCacheService\":\"sharedBlobCacheService\"}"}], "input_token_count": 603, "output_token_count": 433, "latency": 13.***************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\":\"httpResponseEntity\",\"entityParser\":\"responseParser\",\"parserConfig\":\"defaultParserConfig\"}"}], "input_token_count": 316, "output_token_count": 229, "latency": 7.***************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\":\"yes\",\"defaultValue\":\"false\"}"}], "input_token_count": 273, "output_token_count": 168, "latency": 5.63920521736145}
{"id": "java_58", "result": [{"XContentBuilder_map": " {\"values\": \"{name=John Doe, age=30, email=<EMAIL>}\", \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 341, "output_token_count": 340, "latency": 9.364236831665039}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": " {\"terminal\": \"System.console()\", \"shardPath\": \"new ShardPath(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard\\\"))\", \"indexDirectory\": \"FSDirectory.open(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard/index\\\"))\"}"}], "input_token_count": 391, "output_token_count": 415, "latency": 15.185888767242432}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\":\"mainSearchContext\",\"innerHitsContext\":\"hitsContext\"}"}], "input_token_count": 297, "output_token_count": 394, "latency": 12.442390441894531}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\":\"timestamp\",\"origin\":\"now\",\"scale\":\"10d\",\"offset\":\"2d\",\"decay\":\"0.5\"}"}], "input_token_count": 404, "output_token_count": 253, "latency": 7.1055824756622314}
{"id": "java_62", "result": [{"dvRangeQuery": " {\"field\": \"temperature\", \"queryType\": \"FLOAT\", \"from\": \"20.5\", \"to\": \"30.0\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 429, "output_token_count": 362, "latency": 11.240153551101685}
{"id": "java_63", "result": [{"withinQuery": " {\"field\": \"age\", \"from\": \"30\", \"to\": \"40\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 375, "output_token_count": 375, "latency": 12.054986953735352}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": " {\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": \"{\\\"format\\\": \\\"epoch_millis\\\"}\", \"onScriptError\": \"FAIL\"}"}], "input_token_count": 389, "output_token_count": 225, "latency": 7.417025089263916}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": " {\"builder\": \"xContentBuilderInstance\", \"params\": \"[true, true]\"}"}], "input_token_count": 283, "output_token_count": 498, "latency": 14.652719736099243}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": " {\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"onScriptError.IGNORE\"}"}], "input_token_count": 366, "output_token_count": 357, "latency": 11.43585205078125}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\":\"/Applications/PhotoEditor.app\",\"params\":\"{appName=PhotoEditor, imagesRoot=images, backgroundImageFolder=backgrounds}\"}"}], "input_token_count": 290, "output_token_count": 309, "latency": 10.134371280670166}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": " {\"params\": \"{\\\"appImagePath\\\":\\\"/Applications/MyApp.app\\\", \\\"appName\\\":\\\"MyApp\\\"}\"}"}], "input_token_count": 248, "output_token_count": 170, "latency": 5.045917749404907}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\":\"durations\",\"start\":\"2\",\"end\":\"5\"}"}], "input_token_count": 327, "output_token_count": 192, "latency": 6.984136581420898}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": " {\"element\": \"new QName('http://www.example.com', 'item', 'ex')\", \"augs\": \"augmentations\"}"}], "input_token_count": 300, "output_token_count": 258, "latency": 9.559412002563477}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\":\"resultData\",\"thisCoroutine\":\"5\",\"toCoroutine\":\"10\"}"}], "input_token_count": 338, "output_token_count": 325, "latency": 9.406574249267578}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\":\"textBuffer\",\"start\":\"5\",\"length\":\"10\"}"}], "input_token_count": 327, "output_token_count": 179, "latency": 5.201622247695923}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\":\"UTF-8\",\"allowJavaNames\":\"true\"}"}], "input_token_count": 244, "output_token_count": 236, "latency": 7.414700746536255}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": " {\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"true\"}"}], "input_token_count": 310, "output_token_count": 496, "latency": 15.873524904251099}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": " {\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 265, "output_token_count": 144, "latency": 4.090976715087891}
{"id": "java_76", "result": [{"Intro_step": " {\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 232, "output_token_count": 162, "latency": 5.723365545272827}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": " {\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 298, "output_token_count": 251, "latency": 8.937054634094238}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\":\"quiet\",\"otherDependents\":\"[\\\"verbose\\\"]\"}"}], "input_token_count": 263, "output_token_count": 369, "latency": 11.037706851959229}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": " {\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 290, "output_token_count": 172, "latency": 4.953134775161743}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\":\"failOn\",\"ruleIdx\":\"42\"}"}], "input_token_count": 255, "output_token_count": 122, "latency": 4.739685535430908}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\":\"humongous-test-case.json\",\"doGC\":\"() -> { /* Custom garbage collection execution logic here */ }\",\"checker\":\"referenceChecker\",\"gcLogName\":\"gc-analysis.log\",\"shouldContain\":\"[\\\"GC pause\\\"]\",\"shouldNotContain\":\"[\\\"OutOfMemoryError\\\"]\"}"}], "input_token_count": 516, "output_token_count": 272, "latency": 10.224108695983887}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\":\"testArgs\",\"out\":\"System.out\"}"}], "input_token_count": 298, "output_token_count": 187, "latency": 7.4143967628479}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\":\"-waitTime, 2, -iterations, 500\",\"out\":\"System.out\"}"}], "input_token_count": 330, "output_token_count": 357, "latency": 11.224901676177979}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\":\"myRedefClass\",\"expValue\":\"100\"}"}], "input_token_count": 279, "output_token_count": 306, "latency": 12.996783971786499}
{"id": "java_85", "result": [{"classfloadhk005_runIt": " {\"argv\": \"[\\\"/path/to/classes\\\", \\\"60\\\"]\", \"out\": \"logStream\"}"}], "input_token_count": 326, "output_token_count": 762, "latency": 23.105034112930298}
{"id": "java_86", "result": [{"argumenttypes001_runThis": " {\"argv\": \"[\\\"-v\\\", \\\"--no-strict\\\"]\", \"out\": \"debugOutput\"}"}], "input_token_count": 293, "output_token_count": 254, "latency": 7.960528135299683}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": " {\"suspendPolicy\": \"1\", \"property\": \"testProperty=deathEvent001\"}"}], "input_token_count": 269, "output_token_count": 193, "latency": 7.148902416229248}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": " {\"thread\": \"mainThread\", \"testedClass\": \"com.example.MainClass\", \"suspendPolicy\": \"2\", \"property\": \"testProperty\"}"}], "input_token_count": 365, "output_token_count": 473, "latency": 15.4465913772583}
{"id": "java_89", "result": [{"runThis": "{\"argv\":\"-waitTime, 120000, -debuggeeName, TestDebuggee\",\"out\":\"testLogStream\"}"}], "input_token_count": 312, "output_token_count": 522, "latency": 20.993629932403564}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\":\"[-v, -p]\",\"out\":\"System.out\"}"}], "input_token_count": 285, "output_token_count": 241, "latency": 7.314133644104004}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\":\"[\\\"suspend\\\", \\\"log\\\"]\",\"out\":\"debugLog\"}"}], "input_token_count": 284, "output_token_count": 168, "latency": 7.266803026199341}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": " {\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\"}"}], "input_token_count": 266, "output_token_count": 347, "latency": 12.189483880996704}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": " {\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 276, "output_token_count": 148, "latency": 4.656602144241333}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": " {\"symbol\": \"getVersion\", \"recursive\": \"null\"}"}], "input_token_count": 246, "output_token_count": 162, "latency": 6.9082818031311035}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\":\"ExportInlinedObject1Instance\",\"argument\":\"HelloWorld\",\"node\":\"InlinableNodeInstance\",\"library\":\"NodeLibraryInstance\"}"}], "input_token_count": 365, "output_token_count": 163, "latency": 4.913144826889038}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": " {\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 329, "output_token_count": 179, "latency": 5.22646164894104}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": " {\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 264, "output_token_count": 186, "latency": 6.494822263717651}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": " {\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 256, "output_token_count": 174, "latency": 4.7919111251831055}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": " {\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 272, "output_token_count": 241, "latency": 7.461437702178955}
