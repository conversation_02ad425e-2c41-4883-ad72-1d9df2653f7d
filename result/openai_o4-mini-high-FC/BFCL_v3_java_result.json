{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\":\"mapController\",\"parent\":\"mapArea\"}"}], "input_token_count": 161, "output_token_count": 668, "latency": 7.497340202331543}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\":\"Customers\",\"params\":\"new java.util.HashMap<String, Object>() {{ put(\\\"limit\\\", 50); put(\\\"schema\\\", \\\"public\\\"); }}\",\"useShortName\":\"true\"}"}], "input_token_count": 189, "output_token_count": 509, "latency": 6.630231618881226}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\":\"dbMonitor\",\"source\":\"SELECT * FROM Employee WHERE status = 'active'\",\"view\":\"EmployeeView\"}"}], "input_token_count": 205, "output_token_count": 813, "latency": 10.101385116577148}
{"id": "java_3", "result": "", "input_token_count": 230, "output_token_count": 0, "latency": 0.9744102954864502}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"db2ViewBase\":\"new DB2ViewBase(\\\"HR\\\",\\\"EmployeeView\\\")\",\"session\":\"jdbcSession\"}"}], "input_token_count": 170, "output_token_count": 1453, "latency": 14.967667579650879}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\":\"dataController\",\"parent\":\"compositeParent\"}"}], "input_token_count": 184, "output_token_count": 543, "latency": 7.668891191482544}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"append\":\"true\",\"keepState\":\"true\",\"refreshMetadata\":\"true\"}"}], "input_token_count": 174, "output_token_count": 162, "latency": 2.776512622833252}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\":\"java.nio.file.Paths.get(\\\"/backup/data.txt\\\")\",\"force\":\"true\",\"monitor\":\"progressTracker\"}"}], "input_token_count": 219, "output_token_count": 2411, "latency": 17.513175010681152}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"force\":\"true\",\"keepHistory\":\"false\",\"monitor\":\"progressMonitor\",\"source\":\"fileStream\"}"}], "input_token_count": 237, "output_token_count": 680, "latency": 8.99668550491333}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\":\"/usr/local/bin/dbeaver\",\"name\":\"DBeaverLauncher\"}"}], "input_token_count": 140, "output_token_count": 550, "latency": 8.540518522262573}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"property\":\"EnableExtensions\",\"root\":\"HKEY_LOCAL_MACHINE\"}"}], "input_token_count": 174, "output_token_count": 418, "latency": 6.457577466964722}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\":\"progressMonitor\",\"schemaName\":\"AnalyticsDB\"}"}], "input_token_count": 160, "output_token_count": 672, "latency": 8.341029644012451}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"owner\":\"JohnDoe\",\"session\":\"JDBC_session\"}"}], "input_token_count": 169, "output_token_count": 1441, "latency": 14.575819969177246}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\":\"gameLayout\",\"success\":\"true\"}"}], "input_token_count": 156, "output_token_count": 925, "latency": 9.190107107162476}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\":\"imageInputStream\",\"out\":\"imageOutputStream\"}"}], "input_token_count": 177, "output_token_count": 1248, "latency": 12.840206146240234}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\":\"instructionData\",\"isRange\":\"true\"}"}], "input_token_count": 160, "output_token_count": 609, "latency": 8.15811276435852}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"insn\":\"newConstructorInsn\",\"mth\":\"initMethod\"}"}], "input_token_count": 173, "output_token_count": 610, "latency": 7.756721496582031}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\":\"3\",\"pageSize\":\"20\"}"}], "input_token_count": 168, "output_token_count": 159, "latency": 2.8975772857666016}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\":\"webModel\",\"request\":\"userRequest\"}"}], "input_token_count": 175, "output_token_count": 1820, "latency": 19.114908695220947}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"config\":\"newMappingConfig\",\"fileName\":\"user-mapping.yml\"}"}], "input_token_count": 169, "output_token_count": 929, "latency": 9.468734502792358}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\":\"nettyChannelContext\",\"e\":\"ioExceptionEvent\"}"}], "input_token_count": 172, "output_token_count": 991, "latency": 9.855443477630615}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\":\"[101,202,303]\",\"newStatus\":\"2\"}"}], "input_token_count": 169, "output_token_count": 869, "latency": 6.702945232391357}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"pageNum\":\"3\",\"pageSize\":\"20\",\"productName\":\"LED TV\",\"recommendStatus\":\"1\"}"}], "input_token_count": 214, "output_token_count": 235, "latency": 3.204052448272705}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\":\"[101, 102, 103]\",\"showStatus\":\"0\"}"}], "input_token_count": 176, "output_token_count": 743, "latency": 8.585024118423462}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\":\"42\",\"sort\":\"5\"}"}], "input_token_count": 147, "output_token_count": 606, "latency": 7.237957239151001}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"concurrency\":\"ResultSet.CONCUR_READ_ONLY\",\"holdability\":\"ResultSet.CLOSE_CURSORS_AT_COMMIT\",\"resultSetType\":\"ResultSet.TYPE_SCROLL_INSENSITIVE\",\"sql\":\"\\\"CALL totalSales(?)\\\"\"}"}], "input_token_count": 264, "output_token_count": 2750, "latency": 31.91371512413025}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\":\"[2, 7, 11, 15]\",\"target\":\"9\"}"}], "input_token_count": 161, "output_token_count": 550, "latency": 7.791793346405029}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"basicCredentials\":\"basicAuthCredentials\",\"credentialsFile\":\"es_credentials.properties\",\"credentialsRefreshInterval\":\"30\"}"}], "input_token_count": 192, "output_token_count": 1449, "latency": 17.436429023742676}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"builderExtractor\":\"builder -> builder.concurrency\",\"property\":\"zipkin.collector.activemq.concurrency\",\"value\":\"10\"}"}], "input_token_count": 190, "output_token_count": 1771, "latency": 15.59389877319336}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\":\"answer\",\"value\":\"42\"}"}], "input_token_count": 189, "output_token_count": 2077, "latency": 23.928012371063232}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"codec\":\"jsonCodec\",\"name\":\"taskQueue\"}"}], "input_token_count": 141, "output_token_count": 285, "latency": 4.090653896331787}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"leaseTime\":\"120\",\"unit\":\"SECONDS\",\"waitTime\":\"5\"}"}], "input_token_count": 207, "output_token_count": 1637, "latency": 19.316292762756348}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\":\"employee:1234\",\"value\":\"John Doe\"}"}], "input_token_count": 158, "output_token_count": 1313, "latency": 13.365164756774902}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"delay\":\"300\",\"task\":\"cleanupTask\",\"unit\":\"TimeUnit.SECONDS\"}"}], "input_token_count": 189, "output_token_count": 1121, "latency": 11.501115322113037}
{"id": "java_35", "result": "Here\u2019s how you can issue a Redis BITOP AND using Redisson\u2019s low-level command executor:\n\n```java\nimport org.redisson.Redisson;\nimport org.redisson.RedissonClient;\nimport org.redisson.client.codec.StringCodec;\nimport org.redisson.client.protocol.BitOperation;\nimport org.redisson.client.protocol.RedisCommands;\nimport org.redisson.api.RFuture;\n\npublic class RedisBitOpExample {\n    public static void main(String[] args) {\n        // 1. Create/configure your RedissonClient\n        RedissonClient redisson = Redisson.create();  // or use your existing config\n        \n        // 2. Prepare the keys\n        String destKey = \"user:online:both\";\n        String[] srcKeys = { \"user:online:today\", \"user:online:yesterday\" };\n        \n        // 3. Execute the BITOP AND command\n        RFuture<Long> future = ((org.redisson.Redisson)redisson)\n            .getCommandExecutor()\n            .writeAsync(\n                null,\n                StringCodec.INSTANCE,\n                RedisCommands.BITOP,\n                BitOperation.AND,\n                destKey,\n                srcKeys\n            );\n        \n        // 4. Wait for completion (blocks until the command returns)\n        long length = future.toCompletableFuture().join();\n        System.out.println(\"BITOP AND stored \" + length + \" bytes into key '\" + destKey + \"'\");\n        \n        // 5. Clean up\n        redisson.shutdown();\n    }\n}\n```\n\nExplanation:\n- We cast to the `org.redisson.Redisson` implementation so we can grab its internal `CommandExecutor`.\n- We call `writeAsync(...)` with  \n  \u2022 `RedisCommands.BITOP`  \n  \u2022 the enum `BitOperation.AND`  \n  \u2022 the destination key  \n  \u2022 the source keys array  \n- The returned future resolves to the length (in bytes) of the string stored at the destination key.", "input_token_count": 243, "output_token_count": 5460, "latency": 46.576645374298096}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\":\"[\\\"userID\\\", 42, \\\"username\\\", \\\"johndoe\\\", \\\"isActive\\\", true]\",\"state\":\"processingState\"}"}], "input_token_count": 188, "output_token_count": 1843, "latency": 19.614105701446533}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\":\"jenkinsBuild\",\"text\":\"buildOutput\"}"}], "input_token_count": 158, "output_token_count": 351, "latency": 8.79169**********}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\":\"docFields\"}"}], "input_token_count": 145, "output_token_count": 5596, "latency": 51.**************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\":\"logEvent\",\"toAppendTo\":\"logBuilder\"}"}], "input_token_count": 161, "output_token_count": 1246, "latency": 16.***************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"initializedShard\":\"shardB\",\"unassignedShard\":\"shardA\"}"}], "input_token_count": 155, "output_token_count": 741, "latency": 7.***************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\":\"searchHitParser\"}"}], "input_token_count": 120, "output_token_count": 2140, "latency": 34.**************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"caseInsensitive\":\"true\",\"mapper\":\"usernameField\",\"value\":\"\\\"JohnDoe\\\"\"}"}], "input_token_count": 179, "output_token_count": 1509, "latency": 16.***************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"handler\":\"mockHandler\",\"object\":\"testObject\",\"settings\":\"mockSettings\"}"}], "input_token_count": 192, "output_token_count": 1187, "latency": 9.**************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\":\"DESede\",\"mode\":\"CBC\",\"padding\":\"PKCS5Padding\"}"}], "input_token_count": 184, "output_token_count": 674, "latency": 7.76277494430542}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\":\"envVariables\",\"size\":\"5\"}"}], "input_token_count": 141, "output_token_count": 1307, "latency": 12.091759443283081}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\":\"csmInstance\",\"expected\":\"MyExpectedClass.class\"}"}], "input_token_count": 186, "output_token_count": 482, "latency": 6.0853729248046875}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\":\"CERTIFICATE\",\"value\":\"MIIFdTCCBF2gAwIBAgISESGABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmno\"}"}], "input_token_count": 181, "output_token_count": 16783, "latency": 245.2951967716217}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\":\"new InetSocketAddress(\\\"************\\\", 8080)\",\"sslContext\":\"testSSLContext\"}"}], "input_token_count": 211, "output_token_count": 877, "latency": 10.314481973648071}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\":\"404\",\"responseLength\":\"1500\"}"}], "input_token_count": 177, "output_token_count": 353, "latency": 6.028744459152222}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\":\"deleteQueryRequest\",\"responseListener\":\"testListener\"}"}], "input_token_count": 191, "output_token_count": 677, "latency": 6.994263410568237}
{"id": "java_51", "result": "Here\u2019s an example in Java of how you can wire up and invoke the master\u2010only operation on `CCRUsageTransportAction`, using your existing `usageRequest`, `clusterState`, and a `task`, and handle the result in an `ActionListener`:\n\n```java\n// 1) First, implement an ActionListener to handle the async response\nActionListener<XPackUsageResponse> actionListener = new ActionListener<XPackUsageResponse>() {\n    @Override\n    public void onResponse(XPackUsageResponse response) {\n        // Extract the CCR usage facet out of the overall XPackUsageResponse\n        Usage ccrUsageGeneric = response.getFeatureSet(CcrFeatureSet.NAME);\n        if (ccrUsageGeneric instanceof CcrFeatureSet.Usage) {\n            CcrFeatureSet.Usage ccrUsage = (CcrFeatureSet.Usage) ccrUsageGeneric;\n            int followerIndicesCount    = ccrUsage.getFollowerIndicesCount();\n            int autoFollowPatternsCount = ccrUsage.getAutoFollowPatternsCount();\n            System.out.println(\"CCR follower indices:      \" + followerIndicesCount);\n            System.out.println(\"CCR auto-follow patterns: \" + autoFollowPatternsCount);\n        } else {\n            System.err.println(\"Unexpected CCR usage type: \" + ccrUsageGeneric);\n        }\n    }\n\n    @Override\n    public void onFailure(Exception e) {\n        // Handle failures (e.g. cluster not ready, serialization errors, etc.)\n        System.err.println(\"Failed to fetch CCR usage stats\");\n        e.printStackTrace();\n    }\n};\n\n// 2) Obtain or inject an instance of the transport action (usually via DI in Elasticsearch itself)\nCCRUsageTransportAction ccrUsageTransportAction = /* injected by the node\u2019s cluster services */;\n\n// 3) Invoke the masterOperation to fetch your CCR usage\nccrUsageTransportAction.masterOperation(\n    actionListener,\n    usageRequest,    // your XPackUsageRequest\n    clusterState,    // the current ClusterState\n    task             // the Task that represents this master\u2010node action\n);\n```\n\n\u2013 When you run the above, your `actionListener.onResponse(...)` will be called once the master node has assembled the CCR usage statistics, allowing you to log, return or otherwise consume the number of follower indices and auto-follow patterns.", "input_token_count": 235, "output_token_count": 5409, "latency": 54.36595106124878}
{"id": "java_52", "result": "Here\u2019s a plain\u2010Java (DOM) approach you can drop in anywhere you have your `org.w3c.dom.Node` (e.g. your `SAMLAssertionNode`):\n\n```java\nimport org.w3c.dom.Node;\nimport org.w3c.dom.NodeList;\nimport org.w3c.dom.Element;\nimport java.util.List;\nimport java.util.ArrayList;\n\npublic List<Element> getChildElements(Node assertionNode) {\n    List<Element> elements = new ArrayList<>();\n    NodeList children = assertionNode.getChildNodes();\n    for (int i = 0; i < children.getLength(); i++) {\n        Node child = children.item(i);\n        if (child.getNodeType() == Node.ELEMENT_NODE) {\n            elements.add((Element) child);\n        }\n    }\n    return elements;\n}\n```\n\nIf you prefer Java 8 streams:\n\n```java\nimport java.util.stream.IntStream;\nimport java.util.stream.Collectors;\n\npublic List<Element> getChildElements(Node assertionNode) {\n    NodeList children = assertionNode.getChildNodes();\n    return IntStream.range(0, children.getLength())\n        .mapToObj(children::item)\n        .filter(n -> n.getNodeType() == Node.ELEMENT_NODE)\n        .map(n -> (Element) n)\n        .collect(Collectors.toList());\n}\n```\n\n\u2014\u200aOr, if you have access to your test\u2010helper method already (e.g. in `SamlObjectSignerTests`):\n\n```java\n// returns List<Element>\n@SuppressWarnings(\"unchecked\")\nList<Element> elementChildren =\n    (List<Element>) SamlObjectSignerTests.getChildren(SAMLAssertionNode, Element.class);\n```\n\nAny of these will give you a `List<Element>` containing exactly the child elements of your SAML assertion node.", "input_token_count": 165, "output_token_count": 1844, "latency": 16.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\":\"42\",\"localAcceptedVersion\":\"7\"}"}], "input_token_count": 164, "output_token_count": 2724, "latency": 19.**************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"listener\":\"operationListener\",\"request\":\"snapshotRequest\",\"shardRouting\":\"shardRouteInfo\",\"task\":\"snapshotTask\"}"}], "input_token_count": 240, "output_token_count": 1967, "latency": 19.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"blobStoreCacheService\":\"blobStoreCacheService\",\"cache\":\"cacheService\",\"currentTimeNanosSupplier\":\"currentTimeNanos\",\"indexSettings\":\"indexSettingsForLogs\",\"repositories\":\"repositoriesService\",\"shardPath\":\"/data/nodes/0/indices/logs/5\",\"sharedBlobCacheService\":\"sharedBlobCacheService\",\"threadPool\":\"threadPool\"}"}], "input_token_count": 400, "output_token_count": 1371, "latency": 16.***************}
{"id": "java_56", "result": "In your CCS\u2010duel IT you can pull the body straight out of the raw HttpEntity by calling the shared parseEntity helper.  Assuming:\n\n\u2022 httpResponseEntity is your org.apache.http.HttpEntity  \n\u2022 responseParser is a CheckedFunction<XContentParser,\u2009SearchResponse,\u2009IOException> (or method-reference)  \n\u2022 defaultParserConfig is your XContentParserConfiguration  \n\nyou\u2019d do:\n\n```java\nimport static org.elasticsearch.xpack.ccs.CCSDuelIT.parseEntity;\nimport org.elasticsearch.client.ccs.MultiClusterSearchResponse;  // or whatever your Resp type is\n\n// \u2026\n\n// 1. Parse the response body into your typed response object:\nSearchResponse searchResponse = parseEntity(\n    httpResponseEntity,\n    responseParser,\n    defaultParserConfig\n);\n\n// 2. Now you can inspect/assert on your SearchResponse just like any normal ES response:\nassertThat(searchResponse.getHits().getTotalHits().value, greaterThan(0L));\nassertThat(searchResponse.getHits().getAt(0).getIndex(), containsString(\"remote:\"));\n```\n\nIf you don\u2019t want the static import:\n\n```java\nSearchResponse searchResponse = CCSDuelIT.parseEntity(\n    httpResponseEntity,\n    responseParser,\n    defaultParserConfig\n);\n```\n\nUnder the covers parseEntity will:\n\n1. wrap your HttpEntity in an XContentParser using the supplied config  \n2. hand that parser to your responseParser.apply(...)  \n3. return whatever object your parser function builds  \n\nThat\u2019s all there is to it \u2013 you never have to manually shove the stream into a parser or worry about closing it.", "input_token_count": 202, "output_token_count": 2583, "latency": 19.637082815170288}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"defaultValue\":\"false\",\"value\":\"yes\"}"}], "input_token_count": 173, "output_token_count": 927, "latency": 7.885130882263184}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"ensureNoSelfReferences\":\"true\",\"values\":\"userProfile\",\"writeStartAndEndHeaders\":\"true\"}"}], "input_token_count": 225, "output_token_count": 4070, "latency": 44.0554416179657}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"indexDirectory\":\"/var/data/elasticsearch/nodes/0/indices/1shard/index\",\"shardPath\":\"/var/data/elasticsearch/nodes/0/indices/1shard\",\"terminal\":\"terminal\"}"}], "input_token_count": 273, "output_token_count": 1216, "latency": 12.253686666488647}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"innerHitsContext\":\"hitsContext\",\"parentSearchContext\":\"mainSearchContext\"}"}], "input_token_count": 190, "output_token_count": 2467, "latency": 25.889706134796143}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"decay\":\"0.5\",\"fieldName\":\"timestamp\",\"origin\":\"now\",\"scale\":\"10d                                                                                                               \"}"}], "input_token_count": 262, "output_token_count": 1177, "latency": 13.604448318481445}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\":\"temperature\",\"from\":\"20.5\",\"includeFrom\":\"true\",\"includeTo\":\"false\",\"queryType\":\"FLOAT\",\"to\":\"30.0\"}"}], "input_token_count": 273, "output_token_count": 1648, "latency": 18.987823724746704}
{"id": "java_63", "result": [{"withinQuery": "{\"field\":\"age\",\"from\":\"30\",\"includeFrom\":\"true\",\"includeTo\":\"false\",\"to\":\"40\"}"}], "input_token_count": 231, "output_token_count": 807, "latency": 12.461507320404053}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"factory\":\"dateFactory\",\"meta\":\"new java.util.HashMap<String, String>() {{ put(\\\"format\\\", \\\"epoch_millis\\\"); }}\",\"name\":\"timestamp\",\"onScriptError\":\"FAIL\",\"script\":\"dateScript\"}"}], "input_token_count": 249, "output_token_count": 833, "latency": 8.059726476669312}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\":\"xContentBuilderInstance\",\"params\":\"Arrays.asList(true, true)\"}"}], "input_token_count": 187, "output_token_count": 5477, "latency": 53.5260112285614}
{"id": "java_66", "result": "In Elasticsearch 8.x composite runtime fields are by design leaf\u2010only: you cannot attach child runtime fields under a composite field. Any attempt to call  \nCompositeRuntimeField.createChildRuntimeField(\u2026)  \nwill immediately throw an IllegalArgumentException (\u201ccomposite fields cannot have children\u201d).\n\nWhat you can do instead:\n\n1. Define additional top-level runtime fields  \n   Rather than nesting under the composite, give each \u201csub-field\u201d its own runtime field in your mapping. In your script you can still reference the same underlying values that you would have used inside the composite.\n\n2. Use multi-fields on an existing runtime field  \n   If your goal is simply to index the same value with different analyzers or datatypes, you can attach index-time multi-fields (but note runtime fields aren\u2019t index-analyzed, so this only applies to mappings at index-time).\n\n3. Flatten your data model  \n   If you really need nesting, consider indexing your documents with actual object (non-runtime) fields and then use nested or object mappings. You can still back those with runtime scripts if needed, but the nesting will be an index time construct rather than a pure runtime construct.\n\nExample of defining two separate runtime fields instead of a \u201cchild\u201d:\n\nPUT my_index/_mapping\n{\n  \"runtime\": {\n    \"compositeField1\": {\n      \"type\": \"composite\",\n      \"script\": {\n        \"source\": \"\"\"\n          // your composite script here\n        \"\"\"\n      }\n    },\n    \"compositeField1_subA\": {\n      \"type\": \"long\",\n      \"script\": {\n        \"source\": \"\"\"\n          // derive subA out of compositeField1\n          def obj = compositeField1; \n          return obj != null ? obj.get('subA') : null;\n        \"\"\",\n        \"on_error\": \"ignore\"\n      }\n    },\n    \"compositeField1_subB\": {\n      \"type\": \"keyword\",\n      \"script\": {\n        \"source\": \"\"\"\n          def obj = compositeField1; \n          return obj != null ? obj.get('subB') : null;\n        \"\"\",\n        \"on_error\": \"ignore\"\n      }\n    }\n  }\n}\n\nEach \u201csub-field\u201d lives at the top level of your runtime mapping, but can still source its data from the composite.", "input_token_count": 236, "output_token_count": 803, "latency": 9.678014993667603}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\":\"/Applications/PhotoEditor.app\",\"params\":\"new java.util.HashMap<String, String>() {{ put(\\\"volumeName\\\",\\\"PhotoEditor\\\"); put(\\\"background\\\",\\\"/absolute/path/to/custom/background.png\\\"); put(\\\"format\\\",\\\"UDZO\\\"); put(\\\"size\\\",\\\"200m\\\"); put(\\\"windowPosition\\\",\\\"100,100\\\"); put(\\\"windowSize\\\",\\\"600,400\\\"); put(\\\"iconSize\\\",\\\"80\\\"); put(\\\"position-PhotoEditor.app\\\",\\\"150,200\\\"); put(\\\"position-Applications\\\",\\\"450,200\\\"); put(\\\"volumeURL\\\",\\\"file:///Volumes/PhotoEditor\\\"); put(\\\"installDir\\\",\\\"/Applications\\\"); }}\"}"}], "input_token_count": 193, "output_token_count": 6561, "latency": 95.48274874687195}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\":\"new HashMap<String,Object>() {{ put(\\\"appImageDir\\\", new File(\\\"/Applications/MyApp.app\\\")); put(\\\"appName\\\", \\\"MyApp\\\"); }}\"}"}], "input_token_count": 159, "output_token_count": 2175, "latency": 21.565322160720825}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\":\"durations\",\"end\":\"5\",\"start\":\"2\"}"}], "input_token_count": 218, "output_token_count": 1055, "latency": 10.755511283874512}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"augs\":\"augmentations\",\"element\":\"new QName(\\\"http://www.example.com\\\",\\\"item\\\",\\\"ex\\\")\"}"}], "input_token_count": 196, "output_token_count": 1257, "latency": 13.1762855052948}
{"id": "java_71", "result": "Here\u2019s a minimal Java snippet showing how you can guard against an unavailable target coroutine, then hand off control (and your `resultData` object) from coroutine 5 to coroutine 10. You can drop this into whatever XML\u2010processing callback or handler you\u2019re using:\n\n```java\nimport com.example.xml.CoroutineManager;\n\npublic class XmlProcessor {\n    // Assuming this method is running inside coroutine #5\n    public void finishCurrentCoroutine(Object resultData) {\n        CoroutineManager manager = CoroutineManager.getInstance();\n        int currentId = 5;\n        int targetId  = 10;\n\n        // 1. Make sure coroutine 10 is registered/available\n        if (!manager.isCoroutineAvailable(targetId)) {\n            throw new IllegalStateException(\n                \"Cannot switch: coroutine \" + targetId + \" is not available\");\n        }\n\n        // 2. Switch execution, passing along your resultData\n        manager.co_exit_to(resultData, currentId, targetId);\n\n        // Note: co_exit_to will never return here\u2014\n        // it transfers control directly to coroutine #10.\n    }\n}\n```\n\nHow it works:\n\n\u2022 `isCoroutineAvailable(10)` checks that the target coroutine exists.  \n\u2022 `co_exit_to(resultData, 5, 10)` atomically hands off the `resultData` object from coroutine 5 into the entry point of coroutine 10.  \n\u2022 After that call, execution continues in coroutine 10, not here.\n\nYou\u2019ll just need to adapt the package names (and the exact type of `resultData`) to your own XML\u2010processing framework.", "input_token_count": 207, "output_token_count": 1935, "latency": 14.71666169166565}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\":\"textBuffer\",\"length\":\"10\",\"start\":\"5\"}"}], "input_token_count": 215, "output_token_count": 2272, "latency": 18.204726934432983}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"allowJavaNames\":\"true\",\"encoding\":\"UTF-8\"}"}], "input_token_count": 142, "output_token_count": 672, "latency": 11.188902139663696}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\":\"55357\",\"inContent\":\"true\",\"low\":\"56832\"}"}], "input_token_count": 190, "output_token_count": 995, "latency": 13.967434883117676}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\":\"XML_SECURITY\",\"sysPropertyName\":\"enableXmlSecurityFeature\"}"}], "input_token_count": 163, "output_token_count": 291, "latency": 3.809720039367676}
{"id": "java_76", "result": [{"Intro_step": "{\"h\":\"600\",\"w\":\"800\"}"}], "input_token_count": 131, "output_token_count": 345, "latency": 9.92350697517395}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"password\":\"P@ssw0rd!\"}"}], "input_token_count": 174, "output_token_count": 325, "latency": 4.028818607330322}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\":\"quiet\",\"otherDependents\":\"new String[]{\\\"verbose\\\"}\"}"}], "input_token_count": 166, "output_token_count": 2403, "latency": 26.185465574264526}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\":\"1234\",\"sysId\":\"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 185, "output_token_count": 680, "latency": 9.145742177963257}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\":\"failOn\",\"ruleIdx\":\"42\"}"}], "input_token_count": 155, "output_token_count": 541, "latency": 8.408031702041626}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"checker\":\"referenceChecker\",\"doGC\":\"customGarbageCollector\",\"gcLogName\":\"gc-analysis.log\",\"shouldContain\":\"new java.util.ArrayList<>(java.util.Arrays.asList(\\\"GC pause\\\"))\",\"shouldNotContain\":\"new java.util.ArrayList<>(java.util.Arrays.asList(\\\"OutOfMemoryError\\\"))\",\"testcaseData\":\"humongous-test-case.json\"}"}], "input_token_count": 353, "output_token_count": 1632, "latency": 15.753311395645142}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\":\"testArgs\",\"out\":\"System.out\"}"}], "input_token_count": 198, "output_token_count": 541, "latency": 5.042735576629639}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": \"-waitTime 120000 -iterations 500 -waitTime 120000 -iterations 500\", \"out\": \"System.out\"}"}], "input_token_count": 226, "output_token_count": 691, "latency": 9.59654712677002}
{"id": "java_84", "result": [{"checkInnerFields": "{\"expValue\":\"100\",\"redefCls\":\"myRedefClass\"}"}], "input_token_count": 173, "output_token_count": 479, "latency": 6.500965356826782}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\":\"/path/to/classes,60\",\"out\":\"logStream\"}"}], "input_token_count": 218, "output_token_count": 1635, "latency": 14.287744045257568}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"-v,--no-strict\", \"out\": \"debugOutput\"}"}], "input_token_count": 193, "output_token_count": 1763, "latency": 24.14039659500122}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"property\":\"testProperty=deathEvent001\",\"suspendPolicy\":\"EventRequest.SUSPEND_EVENT_THREAD\"}"}], "input_token_count": 162, "output_token_count": 6571, "latency": 58.73062753677368}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"property\":\"testProperty\",\"suspendPolicy\":\"EventRequest.SUSPEND_ALL\",\"testedClass\":\"com.example.MainClass\",\"thread\":\"mainThread\"}"}], "input_token_count": 233, "output_token_count": 1651, "latency": 23.427801370620728}
{"id": "java_89", "result": [{"runThis": "{\"argv\":  \"-waitTime,2,-debuggeeName,TestDebuggee\", \"out\": \"testLogStream\"}"}], "input_token_count": 212, "output_token_count": 1065, "latency": 457.48154306411743}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\":\":[\"   \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \n  \r \r \n  \r \r \r \r \r \r \r \r \r \r \r \r \r \n  \r \r \r  \r \r \r  \r \r \r \r \r \r \r \r \r \r \r \r \r \r \r \r  \r \r \r \r \r \r \r \r \r \r \r  \r \r \r \r \r \r \r \r \r \r \r \r \r \r  \r \n  \r \r \r  \r \r \r \r  \r \r\r  \n  \r \r \r  \r \r  \r \r \r \r \r \r \r \r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r \r \r\r  \n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r \r\n\n  \r \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r\n\n  \r \r\n\n  \r \n\n,\"out\":\"System.out\"}"}], "input_token_count": 185, "output_token_count": 4164, "latency": 36.25602173805237}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\":\"new String[]{\\\"suspend\\\",\\\"log\\\"}\",\"out\":\"debugLog\"}"}], "input_token_count": 183, "output_token_count": 2535, "latency": 31.96171545982361}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"classPath\":\"/usr/local/classes:/home/<USER>/java/libs\",\"name\":\"com.example.MyClass\"}"}], "input_token_count": 165, "output_token_count": 424, "latency": 5.750159740447998}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"inst\":\"appInstrumentation\",\"options\":\"trace log\"}"}], "input_token_count": 175, "output_token_count": 478, "latency": 6.655758619308472}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"recursive\":\", symbol: getVersion\",\"symbol\":\"getVersion\"}"}], "input_token_count": 148, "output_token_count": 481, "latency": 8.788963794708252}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"argument\":\"HelloWorld\",\"library\":\"NodeLibraryInstance\",\"node\":\"InlinableNodeInstance\",\"receiver\":\"ExportInlinedObject1Instance\"}"}], "input_token_count": 239, "output_token_count": 879, "latency": 14.4162437915802}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\":\"convertValueMethodElement\",\"frameParameterName\":\"frameVar\",\"returnName\":\"returnValueCode\"}"}], "input_token_count": 210, "output_token_count": 2603, "latency": 27.303694248199463}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\":\"NodeClass\",\"inlined\":\"false\"}"}], "input_token_count": 159, "output_token_count": 672, "latency": 7.443786859512329}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"falseCountLocal\":\"50\",\"trueCountLocal\":\"150\"}"}], "input_token_count": 150, "output_token_count": 225, "latency": 10.755793333053589}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"delegate\":\"existingDelegate\",\"factory\":\"myFactory\"}"}], "input_token_count": 172, "output_token_count": 668, "latency": 11.496228694915771}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"buffer\":\"ByteBuffer.allocate(1024)\",\"multiPoint\":\"new MultiPoint(Arrays.asList(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10)))\",\"xyzmMode\":\"XyzmMode.XYZ\"}"}], "input_token_count": 269, "output_token_count": 1688, "latency": 29.862436532974243}
