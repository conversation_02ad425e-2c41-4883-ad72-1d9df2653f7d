{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\":\"mapController\",\"parent\":\"mapArea\"}"}], "input_token_count": 161, "output_token_count": 348, "latency": 7.257037401199341}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\":\"Customers\",\"params\":\"new java.util.HashMap<String,String>(){{put(\\\"limit\\\",\\\"50\\\");put(\\\"schema\\\",\\\"public\\\");}}\",\"useShortName\":\"true\"}"}], "input_token_count": 189, "output_token_count": 1851, "latency": 17.42781162261963}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\":\"dbMonitor\",\"source\":\"SELECT * FROM Employee WHERE status = 'active'\",\"view\":\"EmployeeView\"}"}], "input_token_count": 205, "output_token_count": 365, "latency": 6.407725095748901}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"dataSource\":\"db2DataSource\",\"monitor\":\"dbMonitor\",\"reference\":\"USERSPACE1\"}"}], "input_token_count": 216, "output_token_count": 298, "latency": 4.937717914581299}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"db2ViewBase\":\"new DB2ViewBase(jdbcSession, \\\"HR\\\", \\\"EmployeeView\\\")\",\"session\":\"jdbcSession\"}"}], "input_token_count": 170, "output_token_count": 1138, "latency": 17.479865074157715}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\":\"dataController\",\"parent\":\"compositeParent\"}"}], "input_token_count": 184, "output_token_count": 1119, "latency": 13.818172454833984}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"append\":\"true\",\"keepState\":\"true\",\"refreshMetadata\":\"true\"}"}], "input_token_count": 174, "output_token_count": 418, "latency": 6.071533918380737}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\":\"Paths.get(\\\"/backup/data.txt\\\")\",\"force\":\"true\",\"monitor\":\"progressTracker\"}"}], "input_token_count": 219, "output_token_count": 488, "latency": 6.391650915145874}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"force\":\"true\",\"keepHistory\":\"false\",\"monitor\":\"progressMonitor\",\"source\":\"fileStream\"}"}], "input_token_count": 237, "output_token_count": 488, "latency": 7.463785886764526}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"buffer\":\"ByteBuffer.allocate(1024)\",\"multiPoint\":\"new MultiPoint(Arrays.asList(new Point(1,2), new Point(3,4), new Point(5,6), new Point(7,8), new Point(9,10)))\",\"xyzmMode\":\"XyzmMode.XYZ\"}"}], "input_token_count": 269, "output_token_count": 531, "latency": 9.759665250778198}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\":\"/usr/local/bin/dbeaver\",\"name\":\"DBeaverLauncher\"}"}], "input_token_count": 140, "output_token_count": 294, "latency": 4.575151681900024}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"property\":\"EnableExtensions\",\"root\":\"HKEY_LOCAL_MACHINE\"}"}], "input_token_count": 174, "output_token_count": 290, "latency": 4.984621524810791}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\":\"progressMonitor\",\"schemaName\":\"AnalyticsDB\"}"}], "input_token_count": 160, "output_token_count": 96, "latency": 4.287797451019287}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"owner\":\"JohnDoe\",\"session\":\"JDBC_session\"}"}], "input_token_count": 169, "output_token_count": 225, "latency": 10.331836700439453}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\":\"gameLayout\",\"success\":\"true\"}"}], "input_token_count": 156, "output_token_count": 861, "latency": 12.871566772460938}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\":\"imageInputStream\",\"out\":\"imageOutputStream\"}"}], "input_token_count": 177, "output_token_count": 992, "latency": 14.439565420150757}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\":\"instructionData\",\"isRange\":\"true\"}"}], "input_token_count": 160, "output_token_count": 353, "latency": 4.3106465339660645}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"insn\":\"newConstructorInsn\",\"mth\":\"initMethod\"}"}], "input_token_count": 173, "output_token_count": 482, "latency": 11.361049890518188}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\":\"3\",\"pageSize\":\"20\"}"}], "input_token_count": 168, "output_token_count": 351, "latency": 6.244971990585327}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\":\"webModel\",\"request\":\"userRequest\"}"}], "input_token_count": 175, "output_token_count": 604, "latency": 9.955252647399902}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"config\":\"newMappingConfig\",\"fileName\":\"user-mapping.yml\"}"}], "input_token_count": 169, "output_token_count": 417, "latency": 5.530909299850464}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\":\"nettyChannelContext\",\"e\":\"ioExceptionEvent\"}"}], "input_token_count": 172, "output_token_count": 799, "latency": 11.996443271636963}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\":\"[101, 202, 303]\",\"newStatus\":\"2\"}"}], "input_token_count": 169, "output_token_count": 551, "latency": 10.327315092086792}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"pageNum\":\"3\",\"pageSize\":\"20\",\"productName\":\"LED TV\",\"recommendStatus\":\"1\"}"}], "input_token_count": 214, "output_token_count": 171, "latency": 3.4560842514038086}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\":\"[101,102,103]\",\"showStatus\":\"0\"}"}], "input_token_count": 176, "output_token_count": 933, "latency": 10.499393463134766}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\":\"42\",\"sort\":\"5\"}"}], "input_token_count": 147, "output_token_count": 286, "latency": 4.572070837020874}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"concurrency\":\"ResultSet.CONCUR_READ_ONLY\",\"holdability\":\"ResultSet.CLOSE_CURSORS_AT_COMMIT\",\"resultSetType\":\"ResultSet.TYPE_SCROLL_INSENSITIVE\",\"sql\":\"\\\"{CALL totalSales(?)}\\\"\"}"}], "input_token_count": 264, "output_token_count": 704, "latency": 9.324110746383667}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\":\"[2,7,11,15]\",\"target\":\"9\"}"}], "input_token_count": 161, "output_token_count": 419, "latency": 7.135315179824829}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"basicCredentials\":\"basicAuthCredentials\",\"credentialsFile\":\"\\\"es_credentials.properties\\\"\",\"credentialsRefreshInterval\":\"30\"}"}], "input_token_count": 192, "output_token_count": 1003, "latency": 18.084706783294678}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"builderExtractor\":\"builder -> builder.concurrency()\",\"property\":\"zipkin.collector.activemq.concurrency\",\"value\":\"10\"}"}], "input_token_count": 190, "output_token_count": 1132, "latency": 14.838645458221436}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\":\"answer\",\"value\":\"42\"}"}], "input_token_count": 189, "output_token_count": 541, "latency": 8.439770221710205}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"codec\":\"jsonCodec\",\"name\":\"taskQueue\"}"}], "input_token_count": 141, "output_token_count": 413, "latency": 5.838840007781982}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"leaseTime\":\"120\",\"unit\":\"SECONDS\",\"waitTime\":\"5\"}"}], "input_token_count": 207, "output_token_count": 549, "latency": 7.392735004425049}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\":\"employee:1234\",\"value\":\"John Doe\"}"}], "input_token_count": 158, "output_token_count": 545, "latency": 8.055644035339355}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"delay\":\"5\",\"task\":\"cleanupTask\",\"unit\":\"TimeUnit.MINUTES\"}"}], "input_token_count": 189, "output_token_count": 482, "latency": 8.211479902267456}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"destination\":\"user:online:both\",\"keys\":\":[\"  ,\"op\":\"BitOperation.AND\"}"}], "input_token_count": 243, "output_token_count": 1191, "latency": 15.256046772003174}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\":\"[\\\"userID\\\", 42, \\\"username\\\", \\\"johndoe\\\", \\\"isActive\\\", true]\",\"state\":\"processingState\"}"}], "input_token_count": 188, "output_token_count": 435, "latency": 10.799724817276001}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\":\"jenkinsBuild\",\"text\":\"buildOutput\"}"}], "input_token_count": 158, "output_token_count": 415, "latency": 6.207855224609375}
{"id": "java_38", "result": "Here\u2019s a quick Java example showing how you can take your filtered\u2010source map (which only contains \u201cname\u201d and \u201caddress\u201d) and turn it into a fully\u2010stubbed source map, with all nested paths in place:\n\n```java\nimport java.util.HashMap;\nimport java.util.Map;\n\npublic class NestedSourceStubExample {\n    public static void main(String[] args) {\n        // 1) Build your filtered\u2010source map (only the fields you pulled back from _source)\n        Map<String, Object> docFields = new HashMap<>();\n        docFields.put(\"name\", null);\n        // If you know \"address\" is a nested object, put an empty Map there\n        docFields.put(\"address\", new HashMap<>());\n\n        // 2) Call the helper to create the stubbed version\n        Map<String, Object> sourceStub = NestedValueFetcher.createSourceMapStub(docFields);\n\n        // 3) Inspect the result\n        System.out.println(sourceStub);\n        // \u2192 { name=null, address={} }\n        //\n        // If you had deeper nested fields\u2014e.g.\n        //   docFields.put(\"address\", Map.of(\"street\", null, \"city\", null));\n        // then createSourceMapStub would give you:\n        //   { name=null, address={ street=null, city=null } }\n    }\n}\n```\n\nExplanation of what\u2019s happening:\n\n\u2022 You start with a `Map<String,Object>` containing only the keys you actually fetched from `_source` (\u201cname\u201d and \u201caddress\u201d).  \n\u2022 You explicitly put an empty `Map` in for any field you know is itself an object (in this case \u201caddress\u201d).  \n\u2022 `createSourceMapStub(...)` will then walk through your map, detect the nested\u2010object slots, and ensure every nested path is present (with `null` leaves) so that downstream code that expects those paths won\u2019t blow up on a missing map entry.", "input_token_count": 145, "output_token_count": 3025, "latency": 36.**************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\":\"logEvent\",\"toAppendTo\":\"logBuilder\"}"}], "input_token_count": 161, "output_token_count": 734, "latency": 8.**************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"initializedShard\":\"shardB\",\"unassignedShard\":\"shardA\"}"}], "input_token_count": 155, "output_token_count": 805, "latency": 9.***************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\":\"searchHitParser\"}"}], "input_token_count": 120, "output_token_count": 348, "latency": 11.**************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"caseInsensitive\":\"true\",\"mapper\":\"usernameField\",\"value\":\"\\\"JohnDoe\\\"\"}"}], "input_token_count": 179, "output_token_count": 677, "latency": 8.***************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"handler\":\"mockHandler\",\"object\":\"testObject\",\"settings\":\"mockSettings\"}"}], "input_token_count": 192, "output_token_count": 931, "latency": 22.***************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\":\"DESede\",\"mode\":\"CBC\",\"padding\":\"PKCS5Padding\"}"}], "input_token_count": 184, "output_token_count": 482, "latency": 5.477022647857666}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\":\"envVariables\",\"size\":\"5\"}"}], "input_token_count": 141, "output_token_count": 283, "latency": 8.908458232879639}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\":\"csmInstance\",\"expected\":\"MyExpectedClass.class\"}"}], "input_token_count": 186, "output_token_count": 354, "latency": 9.42196249961853}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\":\"CERTIFICATE\",\"value\":\"MIIFdTCCBF2gAwIBAgISESGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\"}"}], "input_token_count": 181, "output_token_count": 1476, "latency": 20.725173950195312}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\":\"new InetSocketAddress(\\\"************\\\", 8080)\",\"sslContext\":\"testSSLContext\"}"}], "input_token_count": 211, "output_token_count": 493, "latency": 10.784910678863525}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\":\"404\",\"responseLength\":\"1500\"}"}], "input_token_count": 177, "output_token_count": 609, "latency": 7.638387203216553}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\":\"deleteQueryRequest\",\"responseListener\":\"testListener\"}"}], "input_token_count": 191, "output_token_count": 677, "latency": 7.7583208084106445}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"listener\":\"actionListener\",\"request\":\"usageRequest\",\"state\":\"clusterState\",\"task\":\"task\"}"}], "input_token_count": 235, "output_token_count": 1064, "latency": 19.37195062637329}
{"id": "java_52", "result": "Here\u2019s a simple utility method that will walk the DOM children of your `SAMLAssertionNode` and collect only those that are `Element` nodes:\n\n```java\nimport org.w3c.dom.Node;\nimport org.w3c.dom.NodeList;\nimport org.w3c.dom.Element;\nimport java.util.List;\nimport java.util.ArrayList;\n\npublic static List<Element> getChildElements(Node parent) {\n    List<Element> elements = new ArrayList<>();\n    NodeList children = parent.getChildNodes();\n    for (int i = 0; i < children.getLength(); i++) {\n        Node child = children.item(i);\n        if (child.getNodeType() == Node.ELEMENT_NODE) {\n            elements.add((Element) child);\n        }\n    }\n    return elements;\n}\n```\n\nUsage:\n\n```java\nNode SAMLAssertionNode = \u2026;  // however you obtained it\nList<Element> assertionChildren = getChildElements(SAMLAssertionNode);\nfor (Element e : assertionChildren) {\n    System.out.println(\"Found child element <\" + e.getTagName() + \">\");\n}\n```\n\nIf you\u2019re using Java 8 or higher you can also write it with streams:\n\n```java\nimport java.util.stream.IntStream;\nimport java.util.stream.Collectors;\n\npublic static List<Element> getChildElements(Node parent) {\n    NodeList nl = parent.getChildNodes();\n    return IntStream.range(0, nl.getLength())\n        .mapToObj(nl::item)\n        .filter(n -> n.getNodeType() == Node.ELEMENT_NODE)\n        .map(n -> (Element) n)\n        .collect(Collectors.toList());\n}\n```\n\n---\n\nIf you already have the helper in your test suite (the one you referred to in `SamlObjectSignerTests`), you can simply call:\n\n```java\nList<Element> assertionChildren =\n    SamlObjectSignerTests.getChildren(SAMLAssertionNode, Element.class);\n```\n\nUnder the hood it\u2019s doing exactly the same filter for `Node.ELEMENT_NODE`.", "input_token_count": 165, "output_token_count": 1509, "latency": 33.*************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\":\"42\",\"localAcceptedVersion\":\"7\"}"}], "input_token_count": 164, "output_token_count": 804, "latency": 8.***************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"listener\":\"operationListener\",\"request\":\"snapshotRequest\",\"shardRouting\":\"shardRouteInfo\",\"task\":\"snapshotTask\"}"}], "input_token_count": 240, "output_token_count": 879, "latency": 12.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"blobStoreCacheService\":\"blobStoreCacheService\",\"cache\":\"cacheService\",\"currentTimeNanosSupplier\":\"currentTimeNanos\",\"indexSettings\":\"indexSettingsForLogs\",\"repositories\":\"repositoriesService\",\"shardPath\":\"/data/nodes/0/indices/logs/5\",\"sharedBlobCacheService\":\"sharedBlobCacheService\",\"threadPool\":\"threadPool\"}"}], "input_token_count": 400, "output_token_count": 475, "latency": 5.***************}
{"id": "java_56", "result": "In your CCSDuelIT (or similar) test you can simply call the `parseEntity` helper, passing in your raw HTTP entity, a lambda (or method\u2010reference) to your `responseParser`, and your parser config. For example:\n\n```java\n// import static org.elasticsearch.client.cca.duel.CCSDuelIT.parseEntity;\n\n// Say responseParser has signature:\n//   static SearchResponse responseParser(XContentParser parser) throws IOException\n\nSearchResponse searchResponse = parseEntity(\n    httpResponseEntity,                 // the raw HttpEntity you got back\n    xContentParser -> responseParser(xContentParser),  // your parser function\n    defaultParserConfig                 // your XContentParser configuration\n);\n\n// now you can assert against searchResponse\nassertThat(searchResponse.getHits().getTotalHits().value, equalTo(42L));\n```\n\nOr, if you prefer a method reference:\n\n```java\nSearchResponse searchResponse = parseEntity(\n    httpResponseEntity,\n    YourTestClass::responseParser,\n    defaultParserConfig\n);\n```\n\nThis will:\n\n1.  Wrap the entity\u2019s content in an `XContentParser` configured by `defaultParserConfig`.  \n2.  Hand that parser to your `responseParser(...)`, which reads the JSON/SMILE/etc. and builds the `SearchResponse`.  \n3.  Return the fully-populated `SearchResponse` for your assertions.", "input_token_count": 202, "output_token_count": 1638, "latency": 20.963767051696777}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"defaultValue\":\"false\",\"value\":\"yes\"}"}], "input_token_count": 173, "output_token_count": 415, "latency": 4.826346158981323}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"ensureNoSelfReferences\":\"true\",\"values\":\"userProfile\",\"writeStartAndEndHeaders\":\"true\"}"}], "input_token_count": 225, "output_token_count": 934, "latency": 9.856444597244263}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"indexDirectory\":\"FSDirectory.open(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard/index\\\"))\",\"shardPath\":\"new org.elasticsearch.index.shard.ShardPath(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard\\\"))\",\"terminal\":\"new org.elasticsearch.cli.SystemTerminal()\"}"}], "input_token_count": 273, "output_token_count": 1308, "latency": 14.099600076675415}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"innerHitsContext\":\"hitsContext\",\"parentSearchContext\":\"mainSearchContext\"}"}], "input_token_count": 190, "output_token_count": 931, "latency": 14.396632671356201}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"decay\":\"0.5\",\"fieldName\":\"timestamp\",\"origin\":\"now\",\"scale\":\"10d  \u200e ; \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \u200e  \"}"}], "input_token_count": 262, "output_token_count": 1207, "latency": 15.403743267059326}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\":\"temperature\",\"from\":\"20.5\",\"includeFrom\":\"true\",\"includeTo\":\"false\",\"queryType\":\"FLOAT\",\"to\":\"30.0\"}"}], "input_token_count": 273, "output_token_count": 688, "latency": 6.560823440551758}
{"id": "java_63", "result": [{"withinQuery": "{\"field\":\"age\",\"from\":\"30\",\"includeFrom\":\"true\",\"includeTo\":\"false\",\"to\":\"40\"}"}], "input_token_count": 231, "output_token_count": 487, "latency": 7.506784915924072}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"factory\":\"dateFactory\",\"meta\":\"new java.util.HashMap<String, Object>() {{ put(\\\"format\\\", \\\"epoch_millis\\\"); }}\",\"name\":\"timestamp\",\"onScriptError\":\"FAIL\",\"script\":\"dateScript\"}"}], "input_token_count": 249, "output_token_count": 577, "latency": 12.069112539291382}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\":\"xContentBuilderInstance\",\"params\":\"new ArrayList<>(Arrays.asList(IncludeDefaults.YES, SkipRuntimeFields.YES))\"}"}], "input_token_count": 187, "output_token_count": 687, "latency": 10.554431915283203}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"onScriptError\":\"onScriptError.IGNORE\",\"parent\":\"compositeField1\",\"parentScriptFactory\":\"compositeScriptFactory\",\"parserContext\":\"mappingParserContext\"}"}], "input_token_count": 236, "output_token_count": 822, "latency": 10.298532247543335}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\":\"/Applications/PhotoEditor.app\",\"params\":\"new HashMap<String,String>() {{ put(\\\"volumeName\\\",\\\"PhotoEditor Installer\\\"); put(\\\"volumeUrl\\\",\\\"file:///Volumes/PhotoEditor Installer\\\"); put(\\\"backgroundImageFolder\\\",\\\"resources\\\"); put(\\\"backgroundImageFile\\\",\\\"background.png\\\"); put(\\\"applicationsLink\\\",\\\"/Applications\\\"); }}\"}"}], "input_token_count": 193, "output_token_count": 930, "latency": 11.911961555480957}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\":\"{\\\"appImageDirectory\\\":\\\"/Applications/MyApp.app\\\",\\\"applicationName\\\":\\\"MyApp\\\"}\"}"}], "input_token_count": 159, "output_token_count": 943, "latency": 12.898464679718018}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\":\"durations\",\"end\":\"5\",\"start\":\"2\"}"}], "input_token_count": 218, "output_token_count": 415, "latency": 5.181596755981445}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"augs\":\"augmentations\",\"element\":\"new QName(\\\"http://www.example.com\\\",\\\"item\\\",\\\"ex\\\")\"}"}], "input_token_count": 196, "output_token_count": 681, "latency": 10.728801727294922}
{"id": "java_71", "result": "Here\u2019s a small Java/SAX-handler example showing how you can hand off control from coroutine 5 to coroutine 10, passing along a String `resultData`. The built-in `co_exit_to(...)` call will itself check availability of the target coroutine and throw an exception if it isn\u2019t there, so you can catch and handle that in your XML processing code:\n\n```java\nimport org.xml.sax.*;\nimport org.xml.sax.helpers.DefaultHandler;\n\npublic class CoroutineSwitchHandler extends DefaultHandler {\n    private final CoroutineManager coroutineManager;\n    private final int thisCoroutineId = 5;\n    private final int targetCoroutineId = 10;\n\n    public CoroutineSwitchHandler(CoroutineManager manager) {\n        this.coroutineManager = manager;\n    }\n\n    @Override\n    public void endDocument() throws SAXException {\n        // Prepare the data you want to pass\n        String resultData = computeResultData();\n\n        try {\n            // This will throw IllegalStateException if coroutine 10 isn't available\n            coroutineManager.co_exit_to(resultData, thisCoroutineId, targetCoroutineId);\n        } catch (IllegalStateException e) {\n            // Handle the case where coroutine 10 is not registered/available\n            throw new SAXException(\n                \"Cannot switch to coroutine \" + targetCoroutineId + \": not available\", e);\n        }\n    }\n\n    private String computeResultData() {\n        // ... your logic to build the resultData string ...\n        return \"resultData\";\n    }\n}\n```\n\nKey points:\n\n\u2022 We keep track of the \u201cfrom\u201d ID (5) and the \u201cto\u201d ID (10).  \n\u2022 `co_exit_to(argObject, fromId, toId)` will itself verify that `toId` is registered; if not, it throws an IllegalStateException.  \n\u2022 In an XML handler you can catch that and re-throw it as a SAXException (or handle it however makes sense in your pipeline).  \n\nIf your `CoroutineManager` implementation also offers an explicit \u201cisRegistered(int id)\u201d or \u201chasCoroutine(int id)\u201d method, you can pre-check before calling `co_exit_to`, but it isn\u2019t strictly necessary since the call already does the availability check.", "input_token_count": 207, "output_token_count": 1864, "latency": 21.343286752700806}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\":\"textBuffer\",\"length\":\"10\",\"start\":\"5\"}"}], "input_token_count": 215, "output_token_count": 1056, "latency": 9.468557357788086}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"allowJavaNames\":\"true\",\"encoding\":\"UTF-8\"}"}], "input_token_count": 142, "output_token_count": 480, "latency": 5.240499973297119}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\":\"55357\",\"inContent\":\"true\",\"low\":\"56832\"}"}], "input_token_count": 190, "output_token_count": 1187, "latency": 12.66216516494751}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\":\"XML_SECURITY\",\"sysPropertyName\":\"enableXmlSecurityFeature\"}"}], "input_token_count": 163, "output_token_count": 227, "latency": 3.6232059001922607}
{"id": "java_76", "result": [{"Intro_step": "{\"h\":\"600\",\"w\":\"800\"}"}], "input_token_count": 131, "output_token_count": 217, "latency": 5.056358337402344}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"password\":\"P@ssw0rd!\"}"}], "input_token_count": 174, "output_token_count": 197, "latency": 3.8459057807922363}
{"id": "java_78", "result": "Here\u2019s how you can do it in Java (using an OptionParser/OptionSpecBuilder style API that supports `requiredUnless`):\n\n```java\nimport joptsimple.OptionParser;\nimport joptsimple.OptionSpec;\n\npublic class MyApp {\n    public static void main(String[] args) {\n        OptionParser parser = new OptionParser();\n\n        // Define the \u201cquiet\u201d and \u201cverbose\u201d flags\n        OptionSpec<Void> quietSpec   = parser.accepts(\"quiet\",   \"Suppress non-error output\");\n        OptionSpec<Void> verboseSpec = parser.accepts(\"verbose\", \"Increase verbosity\");\n\n        // Define the \u201coutput-format\u201d option, but make it required unless\n        // either --quiet or --verbose is present\n        OptionSpec<String> outputFormatSpec = parser\n            .accepts(\"output-format\", \"Specify output format (e.g. json, xml)\")\n            .withRequiredArg()\n            .ofType(String.class)\n            .requiredUnless(\"quiet\", \"verbose\");\n\n        // Parse the arguments\n        var opts = parser.parse(args);\n\n        // At this point, if neither --quiet nor --verbose was passed,\n        // the parser will complain if --output-format is missing.\n        String format = opts.valueOf(outputFormatSpec);\n\n        // \u2026 your application logic \u2026\n        System.out.println(\"Using output format: \" + format);\n    }\n}\n```\n\nExplanation:\n\n1. We declare the two \u201cswitch\u201d options, `quiet` and `verbose`, neither of which takes an argument.\n2. We declare `output-format` with a required argument (`withRequiredArg().ofType(String.class)`).\n3. By calling `.requiredUnless(\"quiet\", \"verbose\")`, the parser will enforce that `--output-format` **must** be provided unless **at least one** of `--quiet` or `--verbose` is present on the command line.\n4. When you run your app:\n   - `java MyApp --quiet` - OK (no output-format needed)\n   - `java MyApp --verbose` - OK\n   - `java MyApp --output-format json` - OK\n   - `java MyApp` - error: `Required option missing: --output-format`\n   - `java MyApp --quiet --verbose` - OK\n   - `java MyApp --quiet --output-format xml` - OK\n\nThis gives you exactly the dependency you asked for: \u201coutput-format\u201d is required only if neither \u201cquiet\u201d nor \u201cverbose\u201d is used.", "input_token_count": 166, "output_token_count": 1484, "latency": 18.64974546432495}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\":\"1234\",\"sysId\":\"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 185, "output_token_count": 232, "latency": 7.1677796840667725}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\":\"failOn\",\"ruleIdx\":\"42\"}"}], "input_token_count": 155, "output_token_count": 157, "latency": 3.632887125015259}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"checker\":\"referenceChecker\",\"doGC\":\"customGarbageCollector\",\"gcLogName\":\"\\\"gc-analysis.log\\\"\",\"shouldContain\":\"new ArrayList<>(Arrays.asList(\\\"GC pause\\\"))\",\"shouldNotContain\":\"new ArrayList<>(Arrays.asList(\\\"OutOfMemoryError\\\"))\",\"testcaseData\":\"new String(Files.readAllBytes(Paths.get(\\\"humongous-test-case.json\\\")), StandardCharsets.UTF_8)\"}"}], "input_token_count": 353, "output_token_count": 1708, "latency": 25.509042024612427}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\":\"testArgs\",\"out\":\"System.out\"}"}], "input_token_count": 198, "output_token_count": 413, "latency": 8.61718201637268}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": \"-waitTime,2,-iterations,500\", \"out\": \"System.out\"}"}], "input_token_count": 226, "output_token_count": 360, "latency": 5.828844308853149}
{"id": "java_84", "result": [{"checkInnerFields": "{\"expValue\":\"100\",\"redefCls\":\"myRedefClass\"}"}], "input_token_count": 173, "output_token_count": 287, "latency": 5.403037071228027}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\":\"/path/to/classes,60\",\"out\":\"logStream\"}"}], "input_token_count": 218, "output_token_count": 931, "latency": 9.327652215957642}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\":\"new String[]{\\\"-v\\\",\\\"--no-strict\\\"}\",\"out\":\"debugOutput\"}"}], "input_token_count": 193, "output_token_count": 552, "latency": 8.06257176399231}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"property\":\"testProperty=deathEvent001\",\"suspendPolicy\":\"EVENT_THREAD\"}"}], "input_token_count": 162, "output_token_count": 1062, "latency": 14.573322534561157}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"property\":\"testProperty\",\"suspendPolicy\":\"EventRequest.SUSPEND_ALL\",\"testedClass\":\"com.example.MainClass\",\"thread\":\"mainThread\"}"}], "input_token_count": 233, "output_token_count": 947, "latency": 13.568943977355957}
{"id": "java_89", "result": [{"runThis": "{\"argv\": \"-waitTime\",\"out\":\"testLogStream\"}"}], "input_token_count": 212, "output_token_count": 284, "latency": 6.457966089248657}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": \"[\\\"-v\\\", \\\"-p\\\"]\", \"out\": \"System.out\"}"}], "input_token_count": 185, "output_token_count": 230, "latency": 3.871011972427368}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\":\"new String[]{\\\"suspend\\\",\\\"log\\\"}\",\"out\":\"debugLog\"}"}], "input_token_count": 183, "output_token_count": 679, "latency": 8.437933921813965}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"classPath\":\"/usr/local/classes:/home/<USER>/java/libs\",\"name\":\"com.example.MyClass\"}"}], "input_token_count": 165, "output_token_count": 232, "latency": 4.220516920089722}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"inst\":\"appInstrumentation\",\"options\":\"trace log\"}"}], "input_token_count": 175, "output_token_count": 414, "latency": 6.069937229156494}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\":\"getVersion\"}"}], "input_token_count": 148, "output_token_count": 409, "latency": 5.663874626159668}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"argument\":\"HelloWorld\",\"library\":\"NodeLibraryInstance\",\"node\":\"InlinableNodeInstance\",\"receiver\":\"ExportInlinedObject1Instance\"}"}], "input_token_count": 239, "output_token_count": 303, "latency": 4.415652513504028}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\":\"convertValue\",\"frameParameterName\":\"frameVar\",\"returnName\":\"returnValueCode\"}"}], "input_token_count": 210, "output_token_count": 489, "latency": 6.034954309463501}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\":\"NodeClass\",\"inlined\":\"false\"}"}], "input_token_count": 159, "output_token_count": 672, "latency": 8.364520788192749}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"falseCountLocal\":\"50\",\"trueCountLocal\":\"150\"}"}], "input_token_count": 150, "output_token_count": 353, "latency": 4.794439792633057}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"delegate\":\"existingDelegate\",\"factory\":\"myFactory\"}"}], "input_token_count": 172, "output_token_count": 540, "latency": 8.421395540237427}
