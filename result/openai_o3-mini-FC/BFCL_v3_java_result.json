{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 161, "output_token_count": 90, "latency": 9.944957971572876}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"Customers\", \"params\": \"{limit=50, schemaFilter=public}\", \"useShortName\": \"true\"}"}], "input_token_count": 189, "output_token_count": 363, "latency": 9.975320816040039}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\", \"view\": \"EmployeeView\"}"}], "input_token_count": 205, "output_token_count": 108, "latency": 6.7355499267578125}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"dataSource\": \"db2DataSource\", \"monitor\": \"dbMonitor\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 216, "output_token_count": 106, "latency": 6.247209310531616}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"db2ViewBase\": \"DB2ViewBase{name='EmployeeView', schema='HR'}\", \"session\": \"jdbcSession\"}"}], "input_token_count": 170, "output_token_count": 429, "latency": 9.852249145507812}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 184, "output_token_count": 349, "latency": 6.141037702560425}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"append\": \"true\", \"keepState\": \"true\", \"refreshMetadata\": \"true\"}"}], "input_token_count": 174, "output_token_count": 98, "latency": 9.024346351623535}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 219, "output_token_count": 35, "latency": 8.739421129226685}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\", \"source\": \"fileStream\"}"}], "input_token_count": 237, "output_token_count": 106, "latency": 8.006585121154785}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"buffer\": \"ByteBuffer.allocate(1024)\", \"multiPoint\": \"new MultiPoint(Arrays.asList(new Point(1,2), new Point(3,4), new Point(5,6), new Point(7,8), new Point(9,10)))\", \"xyzmMode\": \"XyzmMode.XYZ\"}"}], "input_token_count": 269, "output_token_count": 274, "latency": 8.762934923171997}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 140, "output_token_count": 99, "latency": 14.248299598693848}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"property\": \"EnableExtensions\", \"root\": \"HKEY_LOCAL_MACHINE\"}"}], "input_token_count": 174, "output_token_count": 96, "latency": 7.042315244674683}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 160, "output_token_count": 30, "latency": 5.681790351867676}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"owner\": \"JohnDoe\", \"session\": \"JDBC_session\"}"}], "input_token_count": 169, "output_token_count": 95, "latency": 7.527864933013916}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 156, "output_token_count": 347, "latency": 8.239372968673706}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 177, "output_token_count": 94, "latency": 5.230692148208618}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 160, "output_token_count": 95, "latency": 7.2203569412231445}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"insn\": \"newConstructorInsn\", \"mth\": \"initMethod\"}"}], "input_token_count": 173, "output_token_count": 96, "latency": 5.658499479293823}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 168, "output_token_count": 93, "latency": 9.609773635864258}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest\"}"}], "input_token_count": 175, "output_token_count": 154, "latency": 8.02057147026062}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"config\": \"newMappingConfig\", \"fileName\": \"user-mapping.yml\"}"}], "input_token_count": 169, "output_token_count": 95, "latency": 14.50664472579956}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"something goes wrong with channel - Exception details: ioExceptionEvent\"}"}], "input_token_count": 172, "output_token_count": 294, "latency": 9.02242136001587}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"[101, 202, 303]\", \"newStatus\": \"2\"}"}], "input_token_count": 169, "output_token_count": 355, "latency": 8.19330382347107}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"pageNum\": \"3\", \"pageSize\": \"20\", \"productName\": \"LED TV\", \"recommendStatus\": \"1\"}"}], "input_token_count": 214, "output_token_count": 45, "latency": 8.497588396072388}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"[101,102,103]\", \"showStatus\": \"0\"}"}], "input_token_count": 176, "output_token_count": 97, "latency": 7.789929389953613}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 147, "output_token_count": 28, "latency": 4.497711658477783}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"concurrency\": \"ResultSet.CONCUR_READ_ONLY\", \"holdability\": \"ResultSet.CLOSE_CURSORS_AT_COMMIT\", \"resultSetType\": \"ResultSet.TYPE_SCROLL_INSENSITIVE\", \"sql\": \"CALL totalSales(?)\"}"}], "input_token_count": 264, "output_token_count": 446, "latency": 9.543988704681396}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"[2,7,11,15]\", \"target\": \"9\"}"}], "input_token_count": 161, "output_token_count": 95, "latency": 6.490890264511108}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"basicCredentials\": \"basicAuthCredentials\", \"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\"}"}], "input_token_count": 192, "output_token_count": 105, "latency": 7.9412682056427}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"builderExtractor\": \"ActiveMQCollector.Builder::getConcurrency\", \"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\"}"}], "input_token_count": 190, "output_token_count": 429, "latency": 11.996739625930786}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 189, "output_token_count": 91, "latency": 2.723966121673584}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"codec\": \"jsonCodec\", \"name\": \"taskQueue\"}"}], "input_token_count": 141, "output_token_count": 27, "latency": 3.878106117248535}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"leaseTime\": \"120\", \"unit\": \"SECONDS\", \"waitTime\": \"5\"}"}], "input_token_count": 207, "output_token_count": 165, "latency": 5.922130346298218}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 158, "output_token_count": 31, "latency": 2.814981698989868}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"delay\": \"300\", \"task\": \"cleanupTask\", \"unit\": \"TimeUnit.SECONDS\"}"}], "input_token_count": 189, "output_token_count": 609, "latency": 11.871724128723145}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"destination\": \"user:online:both\", \"keys\": \"user:online:today,user:online:yesterday\", \"op\": \"BitOperation.AND\"}"}], "input_token_count": 243, "output_token_count": 175, "latency": 8.09447431564331}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": \"['userID', 42, 'username', 'johndoe', 'isActive', true]\", \"state\": \"processingState\"}"}], "input_token_count": 188, "output_token_count": 175, "latency": 11.083349704742432}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 158, "output_token_count": 29, "latency": 2.****************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"docFields\"}"}], "input_token_count": 145, "output_token_count": 88, "latency": 4.***************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"logEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 161, "output_token_count": 92, "latency": 6.***************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"initializedShard\": \"shardB\", \"unassignedShard\": \"shardA\"}"}], "input_token_count": 155, "output_token_count": 227, "latency": 8.***************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser.declareObject((parser, context) -> {\\n    // Create a map to hold the parsed inner hits\\n    Map<String, SearchHit> innerHitsMap = new HashMap<>();\\n    \\n    // The ObjectParser for inner hits fields. This parser will iterate over each inner hit field and parse the associated SearchHit data.\\n    // The field name (key) typically corresponds to the inner hit name configured on the query.\\n    \\n    // For each token in the JSON, the parser should advance and add the parsed SearchHit objects to the map.\\n    // Example parsing logic (simplified):\\n    while (parser.nextToken() != XContentParser.Token.END_OBJECT) {\\n        String innerHitName = parser.currentName();\\n        // Advance to the inner hit object\\n        parser.nextToken();\\n        // Parse the inner hit using a dedicated SearchHit parser method (assumed to exist)\\n        SearchHit hit = SearchHit.fromXContent(parser);\\n        innerHitsMap.put(innerHitName, hit);\\n    }\\n    \\n    return innerHitsMap;\\n}, new ParseField(\\\"inner_hits\\\"))\"}"}], "input_token_count": 120, "output_token_count": 460, "latency": 6.540045738220215}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"caseInsensitive\": \"true\", \"mapper\": \"usernameField\", \"value\": \"JohnDoe\"}"}], "input_token_count": 179, "output_token_count": 163, "latency": 6.757687568664551}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"handler\": \"mockHandler\", \"object\": \"testObject\", \"settings\": \"mockSettings\"}"}], "input_token_count": 192, "output_token_count": 163, "latency": 28.191590070724487}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 184, "output_token_count": 162, "latency": 7.478388071060181}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": \"5\"}"}], "input_token_count": 141, "output_token_count": 89, "latency": 5.849930763244629}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 186, "output_token_count": 224, "latency": 8.38541579246521}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\"}"}], "input_token_count": 181, "output_token_count": 723, "latency": 7.570470094680786}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"new InetSocketAddress(\\\"************\\\", 8080)\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 211, "output_token_count": 490, "latency": 10.25759482383728}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 177, "output_token_count": 95, "latency": 10.704763650894165}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 191, "output_token_count": 291, "latency": 7.990368127822876}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"listener\": \"actionListener\", \"request\": \"usageRequest\", \"state\": \"clusterState\", \"task\": \"currentTask\"}"}], "input_token_count": 235, "output_token_count": 235, "latency": 9.719681978225708}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"SAMLAssertionNode\", \"node_type\": \"org.w3c.dom.Element.class\"}"}], "input_token_count": 165, "output_token_count": 229, "latency": 6.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 164, "output_token_count": 226, "latency": 28.***************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"listener\": \"operationListener\", \"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\"}"}], "input_token_count": 240, "output_token_count": 177, "latency": 8.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"blobStoreCacheService\": \"blobStoreCacheService\", \"cache\": \"cacheService\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"indexSettings\": \"indexSettingsForLogs\", \"repositories\": \"repositoriesService\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"sharedBlobCacheService\": \"sharedBlobCacheService\", \"threadPool\": \"threadPool\"}"}], "input_token_count": 400, "output_token_count": 228, "latency": 9.***************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 202, "output_token_count": 232, "latency": 6.***************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"defaultValue\": \"false\", \"value\": \"yes\"}"}], "input_token_count": 173, "output_token_count": 157, "latency": 8.2111496925354}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"ensureNoSelfReferences\": \"true\", \"values\": \"{\\\"name\\\": \\\"John Doe\\\", \\\"age\\\": 30, \\\"email\\\": \\\"<EMAIL>\\\"}\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 225, "output_token_count": 378, "latency": 10.83418893814087}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"indexDirectory\": \"/var/data/elasticsearch/nodes/0/indices/1shard/index\", \"shardPath\": \"/var/data/elasticsearch/nodes/0/indices/1shard\", \"terminal\": \"standard\"}"}], "input_token_count": 273, "output_token_count": 62, "latency": 13.911324262619019}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"innerHitsContext\": \"hitsContext\", \"parentSearchContext\": \"mainSearchContext\"}"}], "input_token_count": 190, "output_token_count": 161, "latency": 8.498788595199585}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"decay\": \"0.5\", \"fieldName\": \"timestamp\", \"origin\": \"now\", \"scale\": \"10d\" }"}], "input_token_count": 262, "output_token_count": 45, "latency": 9.573593854904175}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"from\": \"20.5\", \"includeFrom\": \"true\", \"includeTo\": \"false\", \"queryType\": \"FLOAT\", \"to\": \"30.0\"}"}], "input_token_count": 273, "output_token_count": 118, "latency": 10.423605680465698}
{"id": "java_63", "result": [{"withinQuery": "{\"field\": \"age\", \"from\": \"30\", \"includeFrom\": \"true\", \"includeTo\": \"false\", \"to\": \"40\"}"}], "input_token_count": 231, "output_token_count": 107, "latency": 9.874603509902954}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"factory\": \"dateFactory\", \"meta\": \"java.util.Collections.singletonMap(\\\"format\\\", \\\"epoch_millis\\\")\", \"name\": \"timestamp\", \"onScriptError\": \"FAIL\", \"script\": \"dateScript\"}"}], "input_token_count": 249, "output_token_count": 445, "latency": 10.923032522201538}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\": \"xContentBuilderInstance\", \"params\": \"[include_defaults:true,skip_runtime_fields:true,defaults:{dynamic_date_formats:true,dynamic_templates:true,date_detection:true,numeric_detection:true}]\" }"}], "input_token_count": 187, "output_token_count": 824, "latency": 18.218127965927124}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"onScriptError\": \"onScriptError.IGNORE\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"parserContext\": \"mappingParserContext\"}"}], "input_token_count": 236, "output_token_count": 312, "latency": 9.860629796981812}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\": \"/Applications/PhotoEditor.app\", \"params\": \"{\\\"applicationName\\\": \\\"PhotoEditor\\\", \\\"backgroundImage\\\": \\\"customBackground.jpg\\\", \\\"volumeURL\\\": \\\"file:///Volumes/PhotoEditor\\\", \\\"installDir\\\": \\\"/Applications\\\"}\"}"}], "input_token_count": 193, "output_token_count": 648, "latency": 12.460327386856079}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": \"{\\\"appImagePath\\\": \\\"/Applications/MyApp.app\\\", \\\"appName\\\": \\\"MyApp\\\"}\"}"}], "input_token_count": 159, "output_token_count": 429, "latency": 6.413768529891968}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"end\": \"5\", \"start\": \"2\"}"}], "input_token_count": 218, "output_token_count": 95, "latency": 6.764840364456177}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"augs\": \"augmentations\", \"element\": \"{namespaceURI='http://www.example.com', localPart='item', prefix='ex'}\"}"}], "input_token_count": 196, "output_token_count": 428, "latency": 9.294937372207642}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 207, "output_token_count": 293, "latency": 8.318567752838135}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"length\": \"10\", \"start\": \"5\"}"}], "input_token_count": 215, "output_token_count": 224, "latency": 8.127645254135132}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"allowJavaNames\": \"true\", \"encoding\": \"UTF-8\"}"}], "input_token_count": 142, "output_token_count": 30, "latency": 5.245684862136841}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"inContent\": \"true\", \"low\": \"56832\"}"}], "input_token_count": 190, "output_token_count": 419, "latency": 8.462295293807983}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 163, "output_token_count": 97, "latency": 7.363732576370239}
{"id": "java_76", "result": [{"Intro_step": "{\"h\": \"600\", \"w\": \"800\"}"}], "input_token_count": 131, "output_token_count": 23, "latency": 5.694444417953491}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 174, "output_token_count": 131, "latency": 7.219555377960205}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"quiet\", \"otherDependents\": \"verbose\"}"}], "input_token_count": 166, "output_token_count": 603, "latency": 19.232544422149658}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 185, "output_token_count": 166, "latency": 6.599782228469849}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 155, "output_token_count": 347, "latency": 9.096553802490234}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"checker\": \"referenceChecker\", \"doGC\": \"customGarbageCollector\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": \"GC pause\", \"shouldNotContain\": \"OutOfMemoryError\", \"testcaseData\": \"humongous-test-case.json\"}"}], "input_token_count": 353, "output_token_count": 330, "latency": 10.322396755218506}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"testArgs\", \"out\": \"System.out\"}"}], "input_token_count": 198, "output_token_count": 155, "latency": 6.303585052490234}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": \"[\\\"-waitTime\\\", \\\"2min\\\", \\\"-iterations\\\", \\\"500\\\"]\", \"out\": \"System.out\"}"}], "input_token_count": 226, "output_token_count": 427, "latency": 16.532782554626465}
{"id": "java_84", "result": [{"checkInnerFields": "{\"expValue\": \"100\", \"redefCls\": \"myRedefClass\"}"}], "input_token_count": 173, "output_token_count": 285, "latency": 15.710234880447388}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"['/path/to/classes', '60']\", \"out\": \"logStream\"}"}], "input_token_count": 218, "output_token_count": 483, "latency": 5.355598449707031}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"[\\\"-v\\\", \\\"--no-strict\\\"]\", \"out\": \"debugOutput\"}"}], "input_token_count": 193, "output_token_count": 163, "latency": 3.025050401687622}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"property\": \"testProperty=deathEvent001\", \"suspendPolicy\": \"EVENT_THREAD\"}"}], "input_token_count": 162, "output_token_count": 996, "latency": 14.709343910217285}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"property\": \"testProperty\", \"suspendPolicy\": \"EventRequest.SUSPEND_ALL\", \"testedClass\": \"com.example.MainClass\", \"thread\": \"mainThread\"}"}], "input_token_count": 233, "output_token_count": 181, "latency": 7.266886234283447}
{"id": "java_89", "result": [{"runThis": "{\"argv\": \"[\\\"-waitTime\\\", \\\"120\\\", \\\"-debuggeeName\\\", \\\"TestDebuggee\\\"]\", \"out\": \"testLogStream\"}"}], "input_token_count": 212, "output_token_count": 1514, "latency": 16.403392791748047}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": \"['-v', '-p']\", \"out\": \"System.out\"}"}], "input_token_count": 185, "output_token_count": 159, "latency": 4.83582878112793}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": \"[\\\"suspend\\\", \\\"log\\\"]\", \"out\": \"debugLog\"}"}], "input_token_count": 183, "output_token_count": 482, "latency": 10.141010522842407}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\", \"name\": \"com.example.MyClass\"}"}], "input_token_count": 165, "output_token_count": 165, "latency": 5.19344162940979}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"inst\": \"appInstrumentation\", \"options\": \"trace log\"}"}], "input_token_count": 175, "output_token_count": 92, "latency": 13.407691955566406}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\"}"}], "input_token_count": 148, "output_token_count": 85, "latency": 7.5394673347473145}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"argument\": \"HelloWorld\", \"library\": \"NodeLibraryInstance\", \"node\": \"InlinableNodeInstance\", \"receiver\": \"ExportInlinedObject1Instance\"}"}], "input_token_count": 239, "output_token_count": 177, "latency": 8.602035999298096}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 210, "output_token_count": 233, "latency": 11.768221855163574}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 159, "output_token_count": 30, "latency": 6.410109519958496}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"falseCountLocal\": \"50\", \"trueCountLocal\": \"150\"}"}], "input_token_count": 150, "output_token_count": 95, "latency": 5.670238733291626}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"delegate\": \"existingDelegate\", \"factory\": \"myFactory\"}"}], "input_token_count": 172, "output_token_count": 218, "latency": 7.59576678276062}
