{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 161, "output_token_count": 154, "latency": 3.358821392059326}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"Customers\", \"params\": \"{\\\"limit\\\": \\\"50\\\", \\\"schema\\\": \\\"public\\\"}\", \"useShortName\": \"true\"}"}], "input_token_count": 189, "output_token_count": 622, "latency": 9.769992589950562}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\", \"view\": \"EmployeeView\"}"}], "input_token_count": 205, "output_token_count": 492, "latency": 6.785664796829224}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"dataSource\": \"db2DataSource\", \"monitor\": \"dbMonitor\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 216, "output_token_count": 106, "latency": 2.7299623489379883}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"db2ViewBase\": \"new DB2ViewBase(\\\"HR\\\", \\\"EmployeeView\\\")\", \"session\": \"jdbcSession\"}"}], "input_token_count": 170, "output_token_count": 1836, "latency": 20.750860452651978}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 184, "output_token_count": 1437, "latency": 15.159953355789185}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"append\": \"true\", \"keepState\": \"true\", \"refreshMetadata\": \"true\"}"}], "input_token_count": 174, "output_token_count": 226, "latency": 3.6093082427978516}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 219, "output_token_count": 483, "latency": 5.26255989074707}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\", \"source\": \"fileStream\"}"}], "input_token_count": 237, "output_token_count": 362, "latency": 6.193329095840454}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"buffer\": \"ByteBuffer.allocate(1024)\", \"multiPoint\": \"new MultiPoint(Arrays.asList(new Point(1,2), new Point(3,4), new Point(5,6), new Point(7,8), new Point(9,10)))\", \"xyzmMode\": \"XyzmMode.XYZ\"}"}], "input_token_count": 269, "output_token_count": 1234, "latency": 12.08573865890503}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 140, "output_token_count": 99, "latency": 3.14572811126709}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"property\": \"EnableExtensions\", \"root\": \"HKEY_LOCAL_MACHINE\"}"}], "input_token_count": 174, "output_token_count": 416, "latency": 4.714268684387207}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 160, "output_token_count": 158, "latency": 11.78685188293457}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"owner\": \"JohnDoe\", \"session\": \"JDBC_session\"}"}], "input_token_count": 169, "output_token_count": 223, "latency": 3.775468111038208}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 156, "output_token_count": 731, "latency": 9.048784255981445}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 177, "output_token_count": 350, "latency": 4.896860361099243}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 160, "output_token_count": 287, "latency": 4.245938777923584}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"insn\": \"newConstructorInsn\", \"mth\": \"initMethod\"}"}], "input_token_count": 173, "output_token_count": 672, "latency": 11.374322414398193}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 168, "output_token_count": 221, "latency": 3.768120050430298}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest\"}"}], "input_token_count": 175, "output_token_count": 474, "latency": 6.911229848861694}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"config\": \"newMappingConfig\", \"fileName\": \"user-mapping.yml\"}"}], "input_token_count": 169, "output_token_count": 479, "latency": 6.621366739273071}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 172, "output_token_count": 1309, "latency": 14.62090539932251}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"new ArrayList<>(Arrays.asList(101L, 202L, 303L))\", \"newStatus\": \"2\"}"}], "input_token_count": 169, "output_token_count": 1903, "latency": 19.042465686798096}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"pageNum\": \"3\", \"pageSize\": \"20\", \"productName\": \"LED TV\", \"recommendStatus\": \"1\"}"}], "input_token_count": 214, "output_token_count": 237, "latency": 4.603408575057983}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"[101,102,103]\", \"showStatus\": \"0\"}"}], "input_token_count": 176, "output_token_count": 481, "latency": 5.47460675239563}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 147, "output_token_count": 412, "latency": 4.941657543182373}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"concurrency\": \"ResultSet.CONCUR_READ_ONLY\", \"holdability\": \"ResultSet.CLOSE_CURSORS_AT_COMMIT\", \"resultSetType\": \"ResultSet.TYPE_SCROLL_INSENSITIVE\", \"sql\": \"CALL totalSales(?)\"}"}], "input_token_count": 264, "output_token_count": 1086, "latency": 13.515236377716064}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"[2, 7, 11, 15]\", \"target\": \"9\"}"}], "input_token_count": 161, "output_token_count": 930, "latency": 9.091550588607788}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"basicCredentials\": \"basicAuthCredentials\", \"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\"}"}], "input_token_count": 192, "output_token_count": 1065, "latency": 10.706769227981567}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"builderExtractor\": \"builder.getConcurrency()\", \"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\"}"}], "input_token_count": 190, "output_token_count": 553, "latency": 8.660200357437134}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 189, "output_token_count": 411, "latency": 4.783816337585449}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"codec\": \"jsonCodec\", \"name\": \"taskQueue\"}"}], "input_token_count": 141, "output_token_count": 155, "latency": 2.6353530883789062}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"leaseTime\": \"120\", \"unit\": \"SECONDS\", \"waitTime\": \"5\"}"}], "input_token_count": 207, "output_token_count": 677, "latency": 8.409740924835205}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 158, "output_token_count": 287, "latency": 5.4376115798950195}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"delay\": \"300\", \"task\": \"cleanupTask\", \"unit\": \"TimeUnit.SECONDS\"}"}], "input_token_count": 189, "output_token_count": 929, "latency": 14.44611120223999}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"destination\": \"user:online:both\", \"keys\": \"[user:online:today, user:online:yesterday]\", \"op\": \"BitOperation.AND\"}"}], "input_token_count": 243, "output_token_count": 624, "latency": 9.340836763381958}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": \"['userID', 42, 'username', 'johndoe', 'isActive', true]\", \"state\": \"processingState\"}"}], "input_token_count": 188, "output_token_count": 815, "latency": 7.879695653915405}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 158, "output_token_count": 733, "latency": 11.***************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"{name=John Doe, address=123 Main Street}\"}"}], "input_token_count": 145, "output_token_count": 4065, "latency": 43.***************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"logEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 161, "output_token_count": 796, "latency": 11.***************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"initializedShard\": \"shardB\", \"unassignedShard\": \"shardA\"}"}], "input_token_count": 155, "output_token_count": 291, "latency": 4.***************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser\"}"}], "input_token_count": 120, "output_token_count": 1752, "latency": 18.***************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"caseInsensitive\": \"true\", \"mapper\": \"usernameField\", \"value\": \"JohnDoe\"}"}], "input_token_count": 179, "output_token_count": 419, "latency": 5.***************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"handler\": \"mockHandler\", \"object\": \"testObject\", \"settings\": \"mockSettings\"}"}], "input_token_count": 192, "output_token_count": 739, "latency": 9.***************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 184, "output_token_count": 418, "latency": 5.925719738006592}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": \"5\"}"}], "input_token_count": 141, "output_token_count": 409, "latency": 6.190610408782959}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 186, "output_token_count": 992, "latency": 11.507795095443726}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\" \t       \n            \t}"}], "input_token_count": 181, "output_token_count": 11762, "latency": 109.23233938217163}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"192.168.1.10:8080\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 211, "output_token_count": 996, "latency": 11.034722566604614}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 177, "output_token_count": 287, "latency": 4.499901056289673}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 191, "output_token_count": 611, "latency": 6.260985851287842}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"listener\": \"actionListener\", \"request\": \"usageRequest\", \"state\": \"clusterState\", \"task\": \"task (or null if no task instance is required)\"}"}], "input_token_count": 235, "output_token_count": 1268, "latency": 17.6950945854187}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"SAMLAssertionNode\", \"node_type\": \"org.w3c.dom.Element.class\"}"}], "input_token_count": 165, "output_token_count": 1829, "latency": 18.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 164, "output_token_count": 1058, "latency": 16.**************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"listener\": \"operationListener\", \"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\"}"}], "input_token_count": 240, "output_token_count": 305, "latency": 4.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"blobStoreCacheService\": \"blobStoreCacheService\", \"cache\": \"cacheService\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"indexSettings\": \"indexSettingsForLogs\", \"repositories\": \"repositoriesService\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"sharedBlobCacheService\": \"sharedBlobCacheService\", \"threadPool\": \"threadPool\"}"}], "input_token_count": 400, "output_token_count": 1124, "latency": 13.**************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 202, "output_token_count": 488, "latency": 7.***************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"defaultValue\": \"false\", \"value\": \"yes\"}"}], "input_token_count": 173, "output_token_count": 605, "latency": 7.765474319458008}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"ensureNoSelfReferences\": \"true\", \"values\": \"{\\\"name\\\":\\\"Alice\\\",\\\"age\\\":30,\\\"email\\\":\\\"<EMAIL>\\\"}\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 225, "output_token_count": 1202, "latency": 11.759135723114014}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"indexDirectory\": \"/var/data/elasticsearch/nodes/0/indices/1shard/index\", \"shardPath\": \"/var/data/elasticsearch/nodes/0/indices/1shard\", \"terminal\": \"terminal\"}"}], "input_token_count": 273, "output_token_count": 510, "latency": 8.24042797088623}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"innerHitsContext\": \"hitsContext\", \"parentSearchContext\": \"mainSearchContext\"}"}], "input_token_count": 190, "output_token_count": 1249, "latency": 12.523780107498169}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"decay\": \"0.5\", \"fieldName\": \"timestamp\", \"offset\": \"2d\", \"origin\": \"now\", \"scale\": \"10d\"}"}], "input_token_count": 262, "output_token_count": 499, "latency": 6.741901159286499}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"from\": \"20.5\", \"includeFrom\": \"true\", \"includeTo\": \"false\", \"queryType\": \"FLOAT\", \"to\": \"30.0\"}"}], "input_token_count": 273, "output_token_count": 310, "latency": 7.247467517852783}
{"id": "java_63", "result": [{"withinQuery": "{\"field\": \"age\", \"from\": \"30\", \"includeFrom\": \"true\", \"includeTo\": \"false\", \"to\": \"40\"}"}], "input_token_count": 231, "output_token_count": 427, "latency": 6.742422103881836}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"factory\": \"dateFactory\", \"meta\": \"{\\\"format\\\": \\\"epoch_millis\\\"}\", \"name\": \"timestamp\", \"onScriptError\": \"FAIL\", \"script\": \"dateScript\"}"}], "input_token_count": 249, "output_token_count": 759, "latency": 9.987623929977417}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\": \"xContentBuilderInstance\", \"params\": \"[includeDefaults=true, skipRuntimeFields=true]\"}"}], "input_token_count": 187, "output_token_count": 1637, "latency": 21.7595272064209}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"onScriptError\": \"onScriptError.IGNORE\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"parserContext\": \"mappingParserContext\"}"}], "input_token_count": 236, "output_token_count": 1144, "latency": 12.010015726089478}
{"id": "java_67", "result": "", "input_token_count": 206, "output_token_count": 0, "latency": 1.7748267650604248}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": \"{\\\"appImageDirectory\\\": \\\"/Applications/MyApp.app\\\", \\\"appName\\\": \\\"MyApp\\\"}\"}"}], "input_token_count": 159, "output_token_count": 1005, "latency": 12.259881734848022}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"end\": \"5\", \"start\": \"2\"}"}], "input_token_count": 218, "output_token_count": 351, "latency": 5.708600759506226}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"augs\": \"augmentations\", \"element\": \"{namespaceURI='http://www.example.com', localPart='item', prefix='ex'}\"}"}], "input_token_count": 196, "output_token_count": 748, "latency": 9.0082266330719}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 207, "output_token_count": 869, "latency": 10.259668827056885}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"length\": \"10\", \"start\": \"5\"}"}], "input_token_count": 215, "output_token_count": 928, "latency": 11.304717540740967}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"allowJavaNames\": \"true\", \"encoding\": \"UTF-8\"}"}], "input_token_count": 142, "output_token_count": 222, "latency": 5.938060522079468}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"inContent\": \"false\", \"low\": \"56832\"}"}], "input_token_count": 190, "output_token_count": 547, "latency": 7.997117280960083}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 163, "output_token_count": 353, "latency": 5.021914005279541}
{"id": "java_76", "result": [{"Intro_step": "{\"h\": \"600\", \"w\": \"800\"}"}], "input_token_count": 131, "output_token_count": 343, "latency": 6.979294776916504}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 174, "output_token_count": 771, "latency": 9.604945659637451}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"quiet\", \"otherDependents\": \"verbose\"}"}], "input_token_count": 166, "output_token_count": 1563, "latency": 17.23731017112732}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 185, "output_token_count": 550, "latency": 7.406614065170288}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 155, "output_token_count": 4251, "latency": 45.26798415184021}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"checker\": \"referenceChecker\", \"doGC\": \"customGarbageCollector\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": \"[\\\"GC pause\\\"]\", \"shouldNotContain\": \"[\\\"OutOfMemoryError\\\"]\", \"testcaseData\": \"humongous-test-case.json\"}"}], "input_token_count": 353, "output_token_count": 1550, "latency": 14.749972820281982}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"testArgs\", \"out\": \"System.out\"}"}], "input_token_count": 198, "output_token_count": 923, "latency": 10.002439737319946}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": \"[\\\"-waitTime\\\", \\\"120000\\\", \\\"-iterations\\\", \\\"500\\\"]\", \"out\": \"System.out\"}"}], "input_token_count": 226, "output_token_count": 1707, "latency": 19.43864154815674}
{"id": "java_84", "result": [{"checkInnerFields": "{\"expValue\": \"100\", \"redefCls\": \"myRedefClass\"}"}], "input_token_count": 173, "output_token_count": 541, "latency": 7.207288026809692}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"['/path/to/classes', '60']\", \"out\": \"logStream\"}"}], "input_token_count": 218, "output_token_count": 1955, "latency": 25.115962505340576}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"['-v', '--no-strict']\", \"out\": \"debugOutput\"}"}], "input_token_count": 193, "output_token_count": 1121, "latency": 14.743000030517578}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"property\": \"testProperty=deathEvent001\", \"suspendPolicy\": \"EVENT_THREAD\"}"}], "input_token_count": 162, "output_token_count": 3044, "latency": 35.00081157684326}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"property\": \"testProperty\", \"suspendPolicy\": \"EventRequest.SUSPEND_ALL\", \"testedClass\": \"com.example.MainClass\", \"thread\": \"mainThread\"}"}], "input_token_count": 233, "output_token_count": 757, "latency": 10.7317373752594}
{"id": "java_89", "result": [{"runThis": "{\"argv\": \"new String[]{\\\"-waitTime\\\", \\\"2\\\", \\\"-debuggeeName\\\", \\\"TestDebuggee\\\"}\", \"out\": \"testLogStream\"}"}], "input_token_count": 212, "output_token_count": 2541, "latency": 26.253429412841797}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": \"['-v', '-p']\", \"out\": \"System.out\"}"}], "input_token_count": 185, "output_token_count": 607, "latency": 11.069743394851685}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": \"['suspend', 'log']\", \"out\": \"debugLog\"}"}], "input_token_count": 183, "output_token_count": 993, "latency": 14.915559768676758}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\", \"name\": \"com.example.MyClass\"}"}], "input_token_count": 165, "output_token_count": 485, "latency": 6.11534309387207}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"inst\": \"appInstrumentation\", \"options\": \"trace log\"}"}], "input_token_count": 175, "output_token_count": 284, "latency": 5.133104562759399}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\"}"}], "input_token_count": 148, "output_token_count": 533, "latency": 6.499333620071411}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"argument\": \"HelloWorld\", \"library\": \"NodeLibraryInstance\", \"node\": \"InlinableNodeInstance\", \"receiver\": \"ExportInlinedObject1Instance\"}"}], "input_token_count": 239, "output_token_count": 497, "latency": 6.273597002029419}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 210, "output_token_count": 553, "latency": 7.767436504364014}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 159, "output_token_count": 414, "latency": 5.149919033050537}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"falseCountLocal\": \"50\", \"trueCountLocal\": \"150\"}"}], "input_token_count": 150, "output_token_count": 223, "latency": 3.8692843914031982}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"delegate\": \"existingDelegate\", \"factory\": \"myFactory\"}"}], "input_token_count": 172, "output_token_count": 474, "latency": 8.193669319152832}
