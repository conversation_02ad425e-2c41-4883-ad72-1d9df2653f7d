{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 284, "output_token_count": 200, "latency": 1.772721767425537}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"Customers\", \"useShortName\": \"true\", \"params\": \"{\\\"limit\\\":50, \\\"schemaFilter\\\":\\\"public\\\"}\"}"}], "input_token_count": 329, "output_token_count": 309, "latency": 2.729029893875122}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"view\": \"EmployeeView\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 342, "output_token_count": 632, "latency": 5.556703567504883}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\": \"dbMonitor\", \"dataSource\": \"db2DataSource\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 349, "output_token_count": 320, "latency": 2.8382322788238525}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\": \"jdbcSession\", \"db2ViewBase\": \"HR.EmployeeView\"}"}], "input_token_count": 297, "output_token_count": 287, "latency": 2.531879186630249}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 305, "output_token_count": 315, "latency": 2.7785050868988037}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\": \"true\", \"append\": \"true\", \"keepState\": \"true\"}"}], "input_token_count": 316, "output_token_count": 227, "latency": 2.005542039871216}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 357, "output_token_count": 205, "latency": 1.8194983005523682}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\": \"fileStream\", \"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\"}"}], "input_token_count": 385, "output_token_count": 217, "latency": 1.926642656326294}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\": \"new MultiPoint(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10))\", \"xyzmMode\": \"XyzmMode.XYZ\", \"buffer\": \"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 407, "output_token_count": 396, "latency": 3.496307611465454}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 263, "output_token_count": 194, "latency": 1.7146854400634766}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\": \"HKEY_LOCAL_MACHINE\", \"property\": \"EnableExtensions\"}"}], "input_token_count": 298, "output_token_count": 184, "latency": 1.63075852394104}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 286, "output_token_count": 223, "latency": 1.9752991199493408}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}], "input_token_count": 294, "output_token_count": 152, "latency": 1.3508310317993164}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 279, "output_token_count": 231, "latency": 2.0408573150634766}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 298, "output_token_count": 125, "latency": 1.1164000034332275}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 282, "output_token_count": 232, "latency": 2.049363136291504}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 296, "output_token_count": 315, "latency": 2.7773265838623047}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 293, "output_token_count": 177, "latency": 1.5722064971923828}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest\"}"}], "input_token_count": 295, "output_token_count": 321, "latency": 2.8434667587280273}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\": \"user-mapping.yml\", \"config\": \"newMappingConfig\"}"}], "input_token_count": 291, "output_token_count": 280, "latency": 2.4688501358032227}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 293, "output_token_count": 382, "latency": 3.363085985183716}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"[101, 202, 303]\", \"newStatus\": \"2\"}"}], "input_token_count": 297, "output_token_count": 284, "latency": 2.507678747177124}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\": \"LED TV\", \"recommendStatus\": \"1\", \"pageSize\": \"20\", \"pageNum\": \"3\"}"}], "input_token_count": 364, "output_token_count": 237, "latency": 2.099339485168457}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"[101, 102, 103]\", \"showStatus\": \"0\"}"}], "input_token_count": 305, "output_token_count": 268, "latency": 2.3644461631774902}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 272, "output_token_count": 255, "latency": 2.269181966781616}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"1001\", \"concurrency\": \"1007\", \"holdability\": \"1\"}"}], "input_token_count": 408, "output_token_count": 338, "latency": 2.998110294342041}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"[2,7,11,15]\", \"target\": \"9\"}"}], "input_token_count": 287, "output_token_count": 545, "latency": 4.785005807876587}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\", \"basicCredentials\": \"basicAuthCredentials\"}"}], "input_token_count": 333, "output_token_count": 335, "latency": 2.9679551124572754}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\", \"builderExtractor\": \"builder.getConcurrency()\"}"}], "input_token_count": 330, "output_token_count": 426, "latency": 3.7512590885162354}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 311, "output_token_count": 199, "latency": 1.7629520893096924}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 266, "output_token_count": 138, "latency": 1.226982831954956}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\": \"5\", \"leaseTime\": \"120\", \"unit\": \"SECONDS\"}"}], "input_token_count": 347, "output_token_count": 289, "latency": 2.5503578186035156}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 284, "output_token_count": 146, "latency": 1.297762155532837}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\": \"cleanupTask\", \"delay\": \"300\", \"unit\": \"TimeUnit.SECONDS\"}"}], "input_token_count": 324, "output_token_count": 258, "latency": 2.277395725250244}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\": \"BitOperation.AND\", \"destination\": [\"user:online:both\"], \"keys\": [\"user:online:today\", \"user:online:yesterday\"]}"}], "input_token_count": 376, "output_token_count": 277, "latency": 2.4475128650665283}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": \"['userID', 42, 'username', 'johndoe', 'isActive', true]\", \"state\": \"processingState\"}"}], "input_token_count": 310, "output_token_count": 882, "latency": 7.7560553550720215}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 279, "output_token_count": 265, "latency": 2.***************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"{\\\"name\\\": \\\"value\\\", \\\"address\\\": \\\"value\\\"}\"}"}], "input_token_count": 254, "output_token_count": 521, "latency": 4.***************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"logEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 282, "output_token_count": 254, "latency": 2.****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\": \"shardA\", \"initializedShard\": \"shardB\"}"}], "input_token_count": 285, "output_token_count": 221, "latency": 1.****************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser\"}"}], "input_token_count": 230, "output_token_count": 176, "latency": 1.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\": \"usernameField\", \"value\": \"JohnDoe\", \"caseInsensitive\": \"true\"}"}], "input_token_count": 316, "output_token_count": 286, "latency": 2.****************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 328, "output_token_count": 152, "latency": 1.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 323, "output_token_count": 284, "latency": 2.508937358856201}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": \"5\"}"}], "input_token_count": 264, "output_token_count": 449, "latency": 3.951042890548706}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 310, "output_token_count": 436, "latency": 3.8373961448669434}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESG...[1024 characters total]\"}"}], "input_token_count": 310, "output_token_count": 471, "latency": 4.140714406967163}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"new java.net.InetSocketAddress(\\\"************\\\", 8080)\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 347, "output_token_count": 218, "latency": 1.9346325397491455}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 307, "output_token_count": 267, "latency": 2.3589882850646973}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 319, "output_token_count": 285, "latency": 2.5207223892211914}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\": \"gather_ccr_usage\", \"request\": \"usageRequest\", \"state\": \"clusterState\", \"listener\": \"actionListener\"}"}], "input_token_count": 383, "output_token_count": 283, "latency": 2.504424810409546}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"SAMLAssertionNode\", \"node_type\": \"Element.class\"}"}], "input_token_count": 292, "output_token_count": 301, "latency": 2.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 295, "output_token_count": 208, "latency": 1.**************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\", \"listener\": \"operationListener\"}"}], "input_token_count": 390, "output_token_count": 286, "latency": 2.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\": \"repositoriesService\", \"cache\": \"cacheService\", \"indexSettings\": \"indexSettingsForLogs\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"threadPool\": \"threadPool\", \"blobStoreCacheService\": \"blobStoreCacheService\", \"sharedBlobCacheService\": \"sharedBlobCacheService\"}"}], "input_token_count": 611, "output_token_count": 605, "latency": 5.***************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 341, "output_token_count": 287, "latency": 2.****************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\": \"yes\", \"defaultValue\": \"false\"}"}], "input_token_count": 296, "output_token_count": 963, "latency": 8.460718870162964}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\": \"{\\\"name\\\": \\\"John Doe\\\", \\\"age\\\": 30, \\\"email\\\": \\\"<EMAIL>\\\"}\", \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 367, "output_token_count": 654, "latency": 5.753703355789185}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\": \"terminal\", \"shardPath\": \"new ShardPath(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard\\\"))\", \"indexDirectory\": \"FSDirectory.open(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard/index\\\"))\"}"}], "input_token_count": 411, "output_token_count": 456, "latency": 4.018191576004028}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\"}"}], "input_token_count": 317, "output_token_count": 694, "latency": 6.096315145492554}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\": \"timestamp\", \"origin\": \"now\", \"scale\": \"10d\", \"offset\": \"2d\", \"decay\": \"0.5\"}"}], "input_token_count": 422, "output_token_count": 289, "latency": 2.5571649074554443}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"queryType\": \"FLOAT\", \"from\": \"20.5\", \"to\": \"30.0\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 453, "output_token_count": 300, "latency": 2.6616148948669434}
{"id": "java_63", "result": [{"withinQuery": "{\"field\": \"age\", \"from\": \"30\", \"to\": \"40\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 399, "output_token_count": 171, "latency": 1.5237138271331787}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": \"{\\\"format\\\": \\\"epoch_millis\\\"}\", \"onScriptError\": \"FAIL\"}"}], "input_token_count": 410, "output_token_count": 269, "latency": 2.3838441371917725}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\": \"xContentBuilderInstance\", \"params\": [\"include_defaults\", \"skip_runtime\"]}"}], "input_token_count": 309, "output_token_count": 850, "latency": 7.470513105392456}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"onScriptError.IGNORE\"}"}], "input_token_count": 385, "output_token_count": 309, "latency": 2.7337663173675537}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\": \"/Applications/PhotoEditor.app\", \"params\": \"{\\\"appName\\\":\\\"PhotoEditor\\\",\\\"imagesRoot\\\":\\\"/path/to/images\\\",\\\"backgroundImage\\\":\\\"background.dmg\\\"}\"}"}], "input_token_count": 315, "output_token_count": 845, "latency": 7.42804217338562}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": \"{\\\"appImageDir\\\": \\\"/Applications/MyApp.app\\\", \\\"appName\\\": \\\"MyApp\\\"}\"}"}], "input_token_count": 270, "output_token_count": 330, "latency": 2.9047718048095703}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"start\": \"2\", \"end\": \"5\"}"}], "input_token_count": 352, "output_token_count": 354, "latency": 3.124084949493408}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\": \"new QName(\\\"http://www.example.com\\\", \\\"item\\\", \\\"ex\\\")\", \"augs\": \"augmentations\"}"}], "input_token_count": 321, "output_token_count": 224, "latency": 1.9833261966705322}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 348, "output_token_count": 298, "latency": 2.637155532836914}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}], "input_token_count": 351, "output_token_count": 267, "latency": 2.365272045135498}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\": \"UTF-8\", \"allowJavaNames\": \"true\"}"}], "input_token_count": 269, "output_token_count": 253, "latency": 2.234508514404297}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"false\"}"}], "input_token_count": 333, "output_token_count": 322, "latency": 2.8418996334075928}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 285, "output_token_count": 362, "latency": 3.1853480339050293}
{"id": "java_76", "result": [{"Intro_step": "{\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 258, "output_token_count": 190, "latency": 1.6806371212005615}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 322, "output_token_count": 971, "latency": 8.539003133773804}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"quiet\", \"otherDependents\": [\"verbose\"]}"}], "input_token_count": 287, "output_token_count": 1034, "latency": 9.093093156814575}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 313, "output_token_count": 313, "latency": 2.762012481689453}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 281, "output_token_count": 262, "latency": 2.311418056488037}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\": \"humongous-test-case.json\", \"doGC\": \"customGarbageCollectorRunnable\", \"checker\": \"referenceChecker\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": [\"GC pause\"], \"shouldNotContain\": [\"OutOfMemoryError\"]}"}], "input_token_count": 534, "output_token_count": 1056, "latency": 9.328827619552612}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"[\\\"testArgs\\\"]\", \"out\": \"System.out\"}"}], "input_token_count": 324, "output_token_count": 435, "latency": 3.826383590698242}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": [\"-waitTime\", \"120\", \"-iterations\", \"500\"], \"out\": \"System.out\"}"}], "input_token_count": 354, "output_token_count": 1534, "latency": 13.527408361434937}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 301, "output_token_count": 334, "latency": 2.942699670791626}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"['/path/to/classes', '60']\", \"out\": \"logStream\"}"}], "input_token_count": 351, "output_token_count": 348, "latency": 3.0686399936676025}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"['-v', '--no-strict']\", \"out\": \"debugOutput\"}"}], "input_token_count": 319, "output_token_count": 1269, "latency": 11.159070253372192}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"1\", \"property\": \"deathEvent001\"}"}], "input_token_count": 288, "output_token_count": 2659, "latency": 23.55649971961975}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\": \"mainThread\", \"testedClass\": \"com.example.MainClass\", \"suspendPolicy\": \"2\", \"property\": \"testProperty\"}"}], "input_token_count": 385, "output_token_count": 914, "latency": 8.043793201446533}
{"id": "java_89", "result": [{"runThis": "{\"argv\": [\"-waitTime\", \"2\", \"-debuggeeName\", \"TestDebuggee\"], \"out\": \"testLogStream\"}"}], "input_token_count": 335, "output_token_count": 289, "latency": 2.5498650074005127}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": [\"-v\", \"-p\"], \"out\": \"System.out\"}"}], "input_token_count": 311, "output_token_count": 444, "latency": 3.9057934284210205}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": [\"suspend\", \"log\"], \"out\": \"debugLog\"}"}], "input_token_count": 307, "output_token_count": 489, "latency": 4.302764177322388}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\"}"}], "input_token_count": 290, "output_token_count": 338, "latency": 2.977309226989746}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 299, "output_token_count": 192, "latency": 1.717844009399414}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\", \"recursive\": null}"}], "input_token_count": 268, "output_token_count": 205, "latency": 1.8130924701690674}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\": \"ExportInlinedObject1Instance\", \"argument\": \"HelloWorld\", \"node\": \"InlinableNodeInstance\", \"library\": \"NodeLibraryInstance\"}"}], "input_token_count": 389, "output_token_count": 250, "latency": 2.2140610218048096}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 350, "output_token_count": 323, "latency": 2.852247476577759}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 286, "output_token_count": 284, "latency": 2.5053484439849854}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 281, "output_token_count": 399, "latency": 3.5095884799957275}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 295, "output_token_count": 305, "latency": 2.687439441680908}
