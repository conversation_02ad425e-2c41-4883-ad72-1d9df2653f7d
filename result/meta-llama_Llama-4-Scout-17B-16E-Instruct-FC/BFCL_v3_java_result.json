{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 755, "output_token_count": 16, "latency": 0.2792494297027588}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"Customers\", \"useShortName\": \"true\", \"params\": \"{\\\"limit\\\": \\\"50\\\", \\\"schemaFilter\\\": \\\"public\\\"}\"}"}], "input_token_count": 805, "output_token_count": 37, "latency": 0.5636813640594482}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"view\": \"EmployeeView\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 818, "output_token_count": 34, "latency": 0.524620771408081}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\": \"dbMonitor\", \"dataSource\": \"db2DataSource\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 829, "output_token_count": 31, "latency": 0.4829568862915039}
{"id": "java_4", "result": [], "input_token_count": 768, "output_token_count": 25, "latency": 0.3979804515838623}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 777, "output_token_count": 17, "latency": 0.2940819263458252}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\": \"true\", \"append\": \"true\", \"keepState\": \"true\"}"}], "input_token_count": 792, "output_token_count": 22, "latency": 0.35652780532836914}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 831, "output_token_count": 23, "latency": 0.37374138832092285}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\": \"fileStream\", \"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\"}"}], "input_token_count": 868, "output_token_count": 27, "latency": 0.4339103698730469}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\": \"[Point(1,2), Point(3,4), Point(5,6), Point(7,8), Point(9,10)]\", \"xyzmMode\": \"XyzmMode.XYZ\", \"buffer\": \"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 883, "output_token_count": 60, "latency": 0.8797035217285156}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 734, "output_token_count": 25, "latency": 0.39571332931518555}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\": \"HKEY_LOCAL_MACHINE\", \"property\": \"EnableExtensions\"}"}], "input_token_count": 768, "output_token_count": 22, "latency": 0.3558528423309326}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 757, "output_token_count": 22, "latency": 0.35549426078796387}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}], "input_token_count": 762, "output_token_count": 20, "latency": 0.33428096771240234}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 750, "output_token_count": 16, "latency": 0.27494311332702637}
{"id": "java_15", "result": [], "input_token_count": 770, "output_token_count": 18, "latency": 0.30526232719421387}
{"id": "java_16", "result": [], "input_token_count": 755, "output_token_count": 20, "latency": 0.32947826385498047}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 768, "output_token_count": 22, "latency": 0.35553479194641113}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 763, "output_token_count": 19, "latency": 0.3173654079437256}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest\"}"}], "input_token_count": 768, "output_token_count": 16, "latency": 0.2760796546936035}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\": \"user-mapping.yml\", \"config\": \"newMappingConfig\"}"}], "input_token_count": 762, "output_token_count": 19, "latency": 0.32039570808410645}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 766, "output_token_count": 20, "latency": 0.3315749168395996}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"[101, 202, 303]\", \"newStatus\": \"2\"}"}], "input_token_count": 763, "output_token_count": 24, "latency": 0.38884949684143066}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\": \"LED TV\", \"recommendStatus\": \"1\", \"pageSize\": \"20\", \"pageNum\": \"3\"}"}], "input_token_count": 850, "output_token_count": 30, "latency": 0.47725558280944824}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"[101, 102, 103]\", \"showStatus\": \"0\"}"}], "input_token_count": 770, "output_token_count": 24, "latency": 0.38646602630615234}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 740, "output_token_count": 17, "latency": 0.2886030673980713}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"ResultSet.TYPE_SCROLL_INSENSITIVE\", \"concurrency\": \"ResultSet.CONCUR_READ_ONLY\", \"holdability\": \"ResultSet.CLOSE_CURSORS_AT_COMMIT\"}"}], "input_token_count": 900, "output_token_count": 47, "latency": 0.7044470310211182}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"[2, 7, 11, 15]\", \"target\": \"9\"}"}], "input_token_count": 755, "output_token_count": 23, "latency": 0.371384859085083}
{"id": "java_28", "result": [], "input_token_count": 808, "output_token_count": 30, "latency": 0.4665648937225342}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\", \"builderExtractor\": \"getConcurrency\"}"}], "input_token_count": 805, "output_token_count": 30, "latency": 0.4695708751678467}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 782, "output_token_count": 17, "latency": 0.29390573501586914}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 735, "output_token_count": 17, "latency": 0.286224365234375}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\": \"5\", \"leaseTime\": \"120\", \"unit\": \"SECONDS\"}"}], "input_token_count": 823, "output_token_count": 26, "latency": 0.41301631927490234}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 751, "output_token_count": 21, "latency": 0.3449528217315674}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\": \"cleanupTask\", \"delay\": \"5\", \"unit\": \"TimeUnit.MINUTES\"}"}], "input_token_count": 801, "output_token_count": 22, "latency": 0.36376452445983887}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\": \"AND\", \"destination\": \"['user:online:both']\", \"keys\": \"['user:online:today', 'user:online:yesterday']\"}"}], "input_token_count": 852, "output_token_count": 36, "latency": 0.5533738136291504}
{"id": "java_36", "result": [], "input_token_count": 779, "output_token_count": 35, "latency": 0.5363197326660156}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 746, "output_token_count": 17, "latency": 0.****************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"{'name': 'value', 'address': {'street': 'value', 'city': 'value'}}\"}"}], "input_token_count": 719, "output_token_count": 34, "latency": 0.****************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"logEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 756, "output_token_count": 18, "latency": 0.*****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\": \"shardA\", \"initializedShard\": \"shardB\"}"}], "input_token_count": 751, "output_token_count": 24, "latency": 0.*****************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser\"}"}], "input_token_count": 694, "output_token_count": 16, "latency": 0.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\": \"usernameField\", \"value\": \"JohnDoe\", \"caseInsensitive\": \"true\"}"}], "input_token_count": 793, "output_token_count": 24, "latency": 0.*****************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 804, "output_token_count": 22, "latency": 0.*****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 800, "output_token_count": 24, "latency": 0.38485097885131836}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": \"5\"}"}], "input_token_count": 735, "output_token_count": 15, "latency": 0.26047801971435547}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 779, "output_token_count": 23, "latency": 0.3718550205230713}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESG\"}"}], "input_token_count": 774, "output_token_count": 27, "latency": 0.42906713485717773}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"192.168.1.10:8080\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 805, "output_token_count": 27, "latency": 0.4301745891571045}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 773, "output_token_count": 21, "latency": 0.34472107887268066}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 788, "output_token_count": 25, "latency": 0.3998832702636719}
{"id": "java_51", "result": [], "input_token_count": 866, "output_token_count": 28, "latency": 0.4463686943054199}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"SAMLAssertionNode\", \"node_type\": \"Element.class\"}"}], "input_token_count": 759, "output_token_count": 21, "latency": 0.3429422378540039}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 760, "output_token_count": 24, "latency": 0.3833475112915039}
{"id": "java_54", "result": [], "input_token_count": 875, "output_token_count": 35, "latency": 0.5458903312683105}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\": \"repositoriesService\", \"cache\": \"cacheService\", \"indexSettings\": \"indexSettingsForLogs\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"threadPool\": \"threadPool\", \"blobStoreCacheService\": \"blobStoreCacheService\", \"sharedBlobCacheService\": \"sharedBlobCacheService\"}"}], "input_token_count": 1120, "output_token_count": 77, "latency": 1.1264276504516602}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 816, "output_token_count": 27, "latency": 0.42855358123779297}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\": \"yes\", \"defaultValue\": \"false\"}"}], "input_token_count": 768, "output_token_count": 18, "latency": 0.30214786529541016}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\": {\"name\": \"John\", \"age\": 30, \"email\": \"<EMAIL>\"}, \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 844, "output_token_count": 41, "latency": 0.6177413463592529}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\": \"Terminal\", \"shardPath\": \"/var/data/elasticsearch/nodes/0/indices/1shard\", \"indexDirectory\": \"/var/data/elasticsearch/nodes/0/indices/1shard/index\"}"}], "input_token_count": 889, "output_token_count": 50, "latency": 0.7460007667541504}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\"}"}], "input_token_count": 788, "output_token_count": 22, "latency": 0.3641219139099121}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\": \"timestamp\", \"origin\": \"now\", \"scale\": \"10d\", \"offset\": \"2d\", \"decay\": \"0.5\"}"}], "input_token_count": 905, "output_token_count": 34, "latency": 0.5309920310974121}
{"id": "java_62", "result": [], "input_token_count": 945, "output_token_count": 36, "latency": 0.568321704864502}
{"id": "java_63", "result": [], "input_token_count": 883, "output_token_count": 26, "latency": 0.42105841636657715}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": \"{\\\"format\\\": \\\"epoch_millis\\\"}\", \"onScriptError\": \"FAIL\"}"}], "input_token_count": 901, "output_token_count": 40, "latency": 0.6121652126312256}
{"id": "java_65", "result": [], "input_token_count": 780, "output_token_count": 27, "latency": 0.4276127815246582}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"onScriptError.IGNORE\"}"}], "input_token_count": 870, "output_token_count": 40, "latency": 0.6070342063903809}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\": \"/Applications/PhotoEditor.app\", \"params\": {\"appName\": \"PhotoEditor\", \"imagesRoot\": \"/path/to/images\", \"backgroundImageFolder\": \"/path/to/background/image\"}}"}], "input_token_count": 786, "output_token_count": 50, "latency": 0.7367696762084961}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": {\"appImagePath\": \"/Applications/MyApp.app\", \"appName\": \"MyApp\"}}"}], "input_token_count": 733, "output_token_count": 34, "latency": 0.5167064666748047}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"start\": 2, \"end\": 5}"}], "input_token_count": 828, "output_token_count": 20, "latency": 0.3351426124572754}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\": \"http://www.example.com#item\", \"augs\": \"augmentations\"}"}], "input_token_count": 791, "output_token_count": 24, "latency": 0.3854525089263916}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 823, "output_token_count": 23, "latency": 0.3732163906097412}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}], "input_token_count": 827, "output_token_count": 20, "latency": 0.33983492851257324}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\": \"UTF-8\", \"allowJavaNames\": \"true\"}"}], "input_token_count": 738, "output_token_count": 20, "latency": 0.32971787452697754}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"false\"}"}], "input_token_count": 803, "output_token_count": 23, "latency": 0.37546277046203613}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 758, "output_token_count": 22, "latency": 0.35837507247924805}
{"id": "java_76", "result": [{"Intro_step": "{\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 724, "output_token_count": 12, "latency": 0.2225055694580078}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 768, "output_token_count": 57, "latency": 0.8280830383300781}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"output-format\", \"otherDependents\": [\"quiet\", \"verbose\"]}"}], "input_token_count": 755, "output_token_count": 23, "latency": 0.37026166915893555}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 782, "output_token_count": 28, "latency": 0.4423511028289795}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 749, "output_token_count": 17, "latency": 0.28845906257629395}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\": \"humongous-test-case.json\", \"doGC\": \"customGC.collect()\", \"checker\": \"referenceChecker.checkReferences()\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": [\"GC pause\"], \"shouldNotContain\": [\"OutOfMemoryError\"]}"}], "input_token_count": 1030, "output_token_count": 57, "latency": 0.8473691940307617}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"testArgs\", \"out\": \"System.out\"}"}], "input_token_count": 792, "output_token_count": 17, "latency": 0.2903587818145752}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": [\"-waitTime\", \"120000\", \"-iterations\", \"500\"], \"out\": \"System.out\"}"}], "input_token_count": 820, "output_token_count": 30, "latency": 0.46867990493774414}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 769, "output_token_count": 19, "latency": 0.31908202171325684}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"[\\\"/path/to/classes\\\", \\\"60\\\"]\", \"out\": \"logStream\"}"}], "input_token_count": 812, "output_token_count": 25, "latency": 0.40280604362487793}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": [\"-v\", \"--no-strict\"], \"out\": \"debugOutput\"}"}], "input_token_count": 787, "output_token_count": 22, "latency": 0.35959339141845703}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"EVENT_THREAD\", \"property\": \"testProperty=deathEvent001\"}"}], "input_token_count": 758, "output_token_count": 25, "latency": 0.39882922172546387}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\": \"mainThread\", \"testedClass\": \"com.example.MainClass\", \"suspendPolicy\": \"EventRequest.SUSPEND_ALL\", \"property\": \"testProperty\"}"}], "input_token_count": 867, "output_token_count": 38, "latency": 0.5833895206451416}
{"id": "java_89", "result": [{"runThis": "{\"argv\": [\"-waitTime\", \"120\", \"-debuggeeName\", \"TestDebuggee\"], \"out\": \"testLogStream\"}"}], "input_token_count": 805, "output_token_count": 29, "latency": 0.4526071548461914}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": [\"-v\", \"-p\"], \"out\": \"System.out\"}"}], "input_token_count": 779, "output_token_count": 19, "latency": 0.32317042350769043}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": [\"suspend\", \"log\"], \"out\": \"debugLog\"}"}], "input_token_count": 776, "output_token_count": 22, "latency": 0.356292724609375}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\"}"}], "input_token_count": 760, "output_token_count": 27, "latency": 0.42240095138549805}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 766, "output_token_count": 18, "latency": 0.3036954402923584}
{"id": "java_94", "result": [], "input_token_count": 737, "output_token_count": 16, "latency": 0.2775998115539551}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\": \"ExportInlinedObject1Instance\", \"argument\": \"HelloWorld\", \"node\": \"InlinableNodeInstance\", \"library\": \"NodeLibraryInstance\"}"}], "input_token_count": 870, "output_token_count": 35, "latency": 0.5418088436126709}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 827, "output_token_count": 29, "latency": 0.4558241367340088}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 757, "output_token_count": 20, "latency": 0.33310747146606445}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 748, "output_token_count": 20, "latency": 0.3306746482849121}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 765, "output_token_count": 16, "latency": 0.27831315994262695}
