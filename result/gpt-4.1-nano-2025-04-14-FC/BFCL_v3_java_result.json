{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 165, "output_token_count": 38, "latency": 0.999920129776001}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"Customers\", \"useShortName\": \"true\", \"params\": \"{\\\"limit\\\":50,\\\"schemaFilter\\\":\\\"public\\\"}\"}"}], "input_token_count": 193, "output_token_count": 56, "latency": 1.2041566371917725}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\":\"dbMonitor\",\"view\":\"EmployeeView\",\"source\":\"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 209, "output_token_count": 39, "latency": 1.3844175338745117}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\": \"dbMonitor\", \"dataSource\": \"db2DataSource\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 220, "output_token_count": 52, "latency": 1.5454812049865723}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\": \"jdbcSession\", \"db2ViewBase\": \"EmployeeView\"}"}, {"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\": \"jdbcSession\", \"db2ViewBase\": \"HR.EmployeeView\"}"}], "input_token_count": 174, "output_token_count": 77, "latency": 2.8745627403259277}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 188, "output_token_count": 41, "latency": 1.1743247509002686}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\":\"true\",\"append\":\"true\",\"keepState\":\"true\"}"}], "input_token_count": 178, "output_token_count": 28, "latency": 0.8344476222991943}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 223, "output_token_count": 46, "latency": 1.397820234298706}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\": \"fileStream\", \"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\"}"}], "input_token_count": 241, "output_token_count": 50, "latency": 1.4031734466552734}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\": \"[(1,2), (3,4), (5,6), (7,8), (9,10)]\", \"xyzmMode\": \"XyzmMode.XYZ\", \"buffer\": \"java.nio.ByteBuffer.allocate(1024)\"}"}, {"writeMultiPoint": "{\"multiPoint\": \"[(1,2), (3,4), (5,6), (7,8), (9,10)]\", \"xyzmMode\": \"XyzmMode.XYZ\", \"buffer\": \"java.nio.ByteBuffer.allocate(1024)\"}"}], "input_token_count": 273, "output_token_count": 142, "latency": 2.656477689743042}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 144, "output_token_count": 48, "latency": 1.56866455078125}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\":\"HKEY_LOCAL_MACHINE\",\"property\":\"EnableExtensions\"}"}], "input_token_count": 178, "output_token_count": 28, "latency": 0.931877613067627}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\":\"progressMonitor\",\"schemaName\":\"AnalyticsDB\"}"}], "input_token_count": 164, "output_token_count": 26, "latency": 0.8540530204772949}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}, {"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}], "input_token_count": 173, "output_token_count": 70, "latency": 3.859325408935547}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\":\"gameLayout\",\"success\":\"true\"}"}], "input_token_count": 160, "output_token_count": 23, "latency": 0.8997054100036621}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 181, "output_token_count": 42, "latency": 1.1625587940216064}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\":\"instructionData\",\"isRange\":\"true\"}"}], "input_token_count": 164, "output_token_count": 27, "latency": 0.9108967781066895}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}, {"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 177, "output_token_count": 72, "latency": 1.724313735961914}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 172, "output_token_count": 41, "latency": 0.9437644481658936}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\":\"webModel\",\"request\":\"userRequest\"}"}], "input_token_count": 179, "output_token_count": 22, "latency": 0.7469298839569092}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\":\"user-mapping.yml\",\"config\":\"newMappingConfig\"}"}], "input_token_count": 173, "output_token_count": 27, "latency": 1.0159187316894531}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}, {"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 176, "output_token_count": 66, "latency": 1.584611177444458}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"101,202,303\", \"newStatus\": \"2\"}"}], "input_token_count": 173, "output_token_count": 45, "latency": 1.1841368675231934}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\": \"LED TV\", \"recommendStatus\": \"1\", \"pageSize\": \"20\", \"pageNum\": \"3\"}"}], "input_token_count": 218, "output_token_count": 53, "latency": 1.2701497077941895}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"[101,102,103]\", \"showStatus\": \"0\"}"}], "input_token_count": 180, "output_token_count": 47, "latency": 1.194610595703125}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 151, "output_token_count": 40, "latency": 1.1248586177825928}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"ResultSet.TYPE_SCROLL_INSENSITIVE\", \"concurrency\": \"ResultSet.CONCUR_READ_ONLY\", \"holdability\": \"ResultSet.CLOSE_CURSORS_AT_COMMIT\"}"}], "input_token_count": 268, "output_token_count": 70, "latency": 1.6505589485168457}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"[2, 7, 11, 15]\", \"target\": \"9\"}"}], "input_token_count": 165, "output_token_count": 48, "latency": 1.5229556560516357}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\", \"basicCredentials\": \"basicAuthCredentials\"}"}], "input_token_count": 196, "output_token_count": 51, "latency": 1.32206392288208}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\":\"zipkin.collector.activemq.concurrency\",\"value\":\"10\",\"builderExtractor\":\"builder.concurrency\"}"}], "input_token_count": 194, "output_token_count": 35, "latency": 1.0798237323760986}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\":\"answer\",\"value\":\"42\"}"}], "input_token_count": 193, "output_token_count": 23, "latency": 1.0002961158752441}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 145, "output_token_count": 39, "latency": 0.9215219020843506}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\":\"5\",\"leaseTime\":\"120\",\"unit\":\"SECONDS\"}"}], "input_token_count": 211, "output_token_count": 31, "latency": 0.9912998676300049}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 162, "output_token_count": 43, "latency": 1.3228282928466797}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\": \"cleanupTask\", \"delay\": \"5\", \"unit\": \"TimeUnit.SECONDS\"}"}], "input_token_count": 193, "output_token_count": 43, "latency": 1.0804500579833984}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\": \"AND\", \"destination\": \"user:online:both\", \"keys\": [\"user:online:today\", \"user:online:yesterday\"]}"}], "input_token_count": 247, "output_token_count": 56, "latency": 1.4451179504394531}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": [\"userID\", 42, \"username\", \"johndoe\", \"isActive\", true], \"state\": \"processingState\"}"}, {"ObjectMapEntryReplayDecoder_decode": "{\"parts\": [\"userID\", 42, \"username\", \"johndoe\", \"isActive\", true], \"state\": \"processingState\"}"}], "input_token_count": 192, "output_token_count": 92, "latency": 1.962627649307251}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 162, "output_token_count": 41, "latency": 1.1088323593139648}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"name,address\"}"}, {"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"name,address\"}"}], "input_token_count": 149, "output_token_count": 60, "latency": 1.****************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\":\"logEvent\",\"toAppendTo\":\"logBuilder\"}"}], "input_token_count": 165, "output_token_count": 24, "latency": 0.****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\":\"shardA\",\"initializedShard\":\"shardB\"}"}], "input_token_count": 159, "output_token_count": 31, "latency": 1.****************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\":\"searchHitParser\"}"}], "input_token_count": 124, "output_token_count": 22, "latency": 0.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\":\"usernameField\",\"value\":\"JohnDoe\",\"caseInsensitive\":\"true\"}"}], "input_token_count": 183, "output_token_count": 29, "latency": 1.****************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 196, "output_token_count": 45, "latency": 1.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 188, "output_token_count": 44, "latency": 1.2120628356933594}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\":\"envVariables\",\"size\":\"5\"}"}], "input_token_count": 145, "output_token_count": 21, "latency": 0.8112790584564209}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}, {"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 190, "output_token_count": 72, "latency": 1.587557315826416}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\":\"CERTIFICATE\",\"value\":\"\\\"MIIFdTCCBF2gAwIBAgISESG\\\"\"}"}], "input_token_count": 185, "output_token_count": 36, "latency": 1.071645736694336}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\":\"192.168.1.10:8080\",\"sslContext\":\"testSSLContext\"}"}], "input_token_count": 215, "output_token_count": 32, "latency": 1.0287551879882812}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\":\"404\",\"responseLength\":\"1500\"}"}], "input_token_count": 181, "output_token_count": 27, "latency": 0.8937585353851318}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 195, "output_token_count": 47, "latency": 1.304753303527832}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\":\"Gather CCR usage statistics\",\"request\":\"usageRequest\",\"state\":\"clusterState\",\"listener\":\"actionListener\"}"}], "input_token_count": 239, "output_token_count": 37, "latency": 1.0033345222473145}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\":\"SAMLAssertionNode\",\"node_type\":\"org.w3c.dom.Element\"}"}], "input_token_count": 169, "output_token_count": 32, "latency": 1.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 168, "output_token_count": 46, "latency": 1.***************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\", \"listener\": \"operationListener\"}"}], "input_token_count": 244, "output_token_count": 57, "latency": 1.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\": \"daily-snapshots\", \"cache\": \"cacheService\", \"indexSettings\": \"indexSettingsForLogs\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"threadPool\": \"threadPool\", \"blobStoreCacheService\": \"blobStoreCacheService\", \"sharedBlobCacheService\": \"sharedBlobCacheService\"}"}], "input_token_count": 404, "output_token_count": 103, "latency": 2.****************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 206, "output_token_count": 50, "latency": 1.***************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\": \"yes\", \"defaultValue\": \"false\"}"}], "input_token_count": 177, "output_token_count": 41, "latency": 2.0166664123535156}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\": \"{\\\"name\\\":\\\"John Doe\\\",\\\"age\\\":30,\\\"email\\\":\\\"<EMAIL>\\\"}\", \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 229, "output_token_count": 65, "latency": 1.784611701965332}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\": \"standard output\", \"shardPath\": \"/var/data/elasticsearch/nodes/0/indices/1shard\", \"indexDirectory\": \"/var/data/elasticsearch/nodes/0/indices/1shard/index\"}"}], "input_token_count": 277, "output_token_count": 75, "latency": 1.4158837795257568}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\"}"}, {"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\"}"}], "input_token_count": 194, "output_token_count": 74, "latency": 1.6378443241119385}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\":\"timestamp\",\"origin\":\"now\",\"scale\":\"10d\",\"offset\":\"2d\",\"decay\":\"0.5\"}"}], "input_token_count": 266, "output_token_count": 41, "latency": 1.0451786518096924}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"queryType\": \"FLOAT\", \"from\": \"20.5\", \"to\": \"30.0\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 277, "output_token_count": 58, "latency": 1.2949652671813965}
{"id": "java_63", "result": [{"withinQuery": "{\"field\":\"age\",\"from\":\"30\",\"to\":\"40\",\"includeFrom\":\"true\",\"includeTo\":\"false\"}"}], "input_token_count": 235, "output_token_count": 33, "latency": 0.9855690002441406}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": \"{\\\"format\\\":\\\"epoch_millis\\\"}\", \"onScriptError\": \"FAIL\"}"}], "input_token_count": 253, "output_token_count": 62, "latency": 1.4810235500335693}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\":\"xContentBuilderInstance\",\"params\":[\"true,false\",\"true,false\",\"true,false\",\"true,false\"]}"}], "input_token_count": 191, "output_token_count": 36, "latency": 1.2095189094543457}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"onScriptError.IGNORE\"}"}], "input_token_count": 240, "output_token_count": 64, "latency": 1.7261192798614502}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\":\"/Applications/PhotoEditor.app\",\"params\":\"{\\\"appName\\\":\\\"PhotoEditor\\\",\\\"imagesRoot\\\":\\\"images\\\",\\\"backgroundImageFolder\\\":\\\"backgrounds\\\",\\\"installDir\\\":\\\"/Applications\\\"}\"}"}], "input_token_count": 197, "output_token_count": 58, "latency": 2.01455020904541}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": \"{\\\"appImageDir\\\":\\\"/Applications/MyApp.app\\\",\\\"appName\\\":\\\"MyApp\\\"}\"}"}], "input_token_count": 163, "output_token_count": 57, "latency": 1.462327480316162}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"start\": \"2\", \"end\": \"5\"}"}], "input_token_count": 222, "output_token_count": 41, "latency": 1.156928539276123}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\": \"{namespaceURI='http://www.example.com', localPart='item', prefix='ex'}\", \"augs\": \"augmentations\"}"}, {"XMLNamespaceBinder_endElement": "{\"element\": \"ex:item\", \"augs\": \"augmentations\"}"}], "input_token_count": 200, "output_token_count": 81, "latency": 1.5663807392120361}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\":\"resultData\",\"thisCoroutine\":\"5\",\"toCoroutine\":\"10\"}"}], "input_token_count": 211, "output_token_count": 31, "latency": 0.966681718826294}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}, {"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}], "input_token_count": 219, "output_token_count": 68, "latency": 1.5558831691741943}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\": \"UTF-8\", \"allowJavaNames\": \"true\"}"}], "input_token_count": 146, "output_token_count": 42, "latency": 1.8480308055877686}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"false\"}"}], "input_token_count": 194, "output_token_count": 45, "latency": 1.2065668106079102}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\":\"XML_SECURITY\",\"sysPropertyName\":\"enableXmlSecurityFeature\"}"}], "input_token_count": 167, "output_token_count": 29, "latency": 0.8472349643707275}
{"id": "java_76", "result": [{"Intro_step": "{\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 135, "output_token_count": 35, "latency": 2.8940908908843994}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"password\":\"P@ssw0rd!\"}"}], "input_token_count": 178, "output_token_count": 63, "latency": 2.0232691764831543}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\":\"quiet\",\"otherDependents\":\"verbose\"}"}], "input_token_count": 170, "output_token_count": 23, "latency": 0.8459482192993164}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 189, "output_token_count": 50, "latency": 1.151703119277954}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}, {"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 159, "output_token_count": 62, "latency": 1.4862370491027832}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\": \"humongous-test-case.json\", \"doGC\": \"System.gc()\", \"checker\": \"referenceChecker\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": [\"GC pause\"], \"shouldNotContain\": [\"OutOfMemoryError\"]}"}], "input_token_count": 357, "output_token_count": 79, "latency": 1.8787333965301514}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"testArgs\", \"out\": \"System.out\"}"}], "input_token_count": 202, "output_token_count": 39, "latency": 1.2251365184783936}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\":\"-waitTime 2 -iterations 500\",\"out\":\"System.out\"}"}], "input_token_count": 230, "output_token_count": 32, "latency": 4.183319807052612}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}, {"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}, {"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 177, "output_token_count": 91, "latency": 1.7979772090911865}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"/path/to/classes 60\", \"out\": \"logStream\"}"}, {"classfloadhk005_runIt": "{\"argv\": \"/path/to/classes 60\", \"out\": \"logStream\"}"}], "input_token_count": 222, "output_token_count": 74, "latency": 1.524825096130371}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"-v --no-strict\", \"out\": \"debugOutput\"}"}, {"argumenttypes001_runThis": "{\"argv\": \"-v --no-strict\", \"out\": \"debugOutput\"}"}], "input_token_count": 197, "output_token_count": 70, "latency": 1.4314594268798828}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"1\", \"property\": \"testProperty\"}"}, {"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"2\", \"property\": \"deathEvent001\"}"}], "input_token_count": 166, "output_token_count": 71, "latency": 1.6357357501983643}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\":\"mainThread\",\"testedClass\":\"com.example.MainClass\",\"suspendPolicy\":\"EventRequest.SUSPEND_ALL\",\"property\":\"testProperty\"}"}], "input_token_count": 237, "output_token_count": 45, "latency": 1.1995265483856201}
{"id": "java_89", "result": [{"runThis": "{\"argv\": \"-waitTime 2 -debuggeeName TestDebuggee\", \"out\": \"testLogStream\"}"}], "input_token_count": 216, "output_token_count": 47, "latency": 1.2030043601989746}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": \"['-v', '-p']\", \"out\": \"System.out\"}"}, {"sourcepaths002_runIt": "{\"args\": \"['-v', '-p']\", \"out\": \"System.out\"}"}], "input_token_count": 189, "output_token_count": 72, "latency": 1.3511860370635986}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\":\"suspend log\",\"out\":\"debugLog\"}"}], "input_token_count": 187, "output_token_count": 26, "latency": 1.4324440956115723}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\"}"}, {"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\"}"}], "input_token_count": 169, "output_token_count": 84, "latency": 1.5142097473144531}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 179, "output_token_count": 40, "latency": 1.208651065826416}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\"}"}, {"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\", \"recursive\": \"true\"}"}], "input_token_count": 152, "output_token_count": 58, "latency": 1.272566318511963}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\": \"ExportInlinedObject1Instance\", \"argument\": \"HelloWorld\", \"node\": \"InlinableNodeInstance\", \"library\": \"NodeLibraryInstance\"}"}], "input_token_count": 243, "output_token_count": 57, "latency": 1.3182902336120605}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 214, "output_token_count": 51, "latency": 3.625420570373535}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 163, "output_token_count": 42, "latency": 1.1559934616088867}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 154, "output_token_count": 43, "latency": 1.1759822368621826}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}, {"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 176, "output_token_count": 60, "latency": 2.0360782146453857}
