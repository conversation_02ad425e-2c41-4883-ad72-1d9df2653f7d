{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 284, "output_token_count": 176, "latency": 3.1155881881713867}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"Customers\", \"useShortName\": \"true\", \"params\": \"{\\\"limit\\\": \\\"50\\\", \\\"schemaFilter\\\": \\\"public\\\"}\"}"}], "input_token_count": 329, "output_token_count": 531, "latency": 9.29123330116272}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"view\": \"employeeView\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 342, "output_token_count": 1641, "latency": 28.697065114974976}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\": \"dbMonitor\", \"dataSource\": \"db2DataSource\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 349, "output_token_count": 204, "latency": 3.6088812351226807}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\": \"jdbcSession\", \"db2ViewBase\": \"new DB2ViewBase(\\\"HR.EmployeeView\\\")\"}"}], "input_token_count": 297, "output_token_count": 311, "latency": 5.458820104598999}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 305, "output_token_count": 316, "latency": 5.552764892578125}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\": \"true\", \"append\": \"true\", \"keepState\": \"true\"}"}], "input_token_count": 316, "output_token_count": 239, "latency": 4.212146759033203}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 357, "output_token_count": 285, "latency": 5.021654367446899}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\": \"fileStream\", \"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\"}"}], "input_token_count": 385, "output_token_count": 313, "latency": 5.526502370834351}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\": \"new MultiPoint(Arrays.asList(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10)))\", \"xyzmMode\": \"XyzmMode.XYZ\", \"buffer\": \"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 407, "output_token_count": 788, "latency": 13.783866882324219}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 263, "output_token_count": 277, "latency": 4.86177659034729}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\": \"HKEY_LOCAL_MACHINE\", \"property\": \"EnableExtensions\"}"}], "input_token_count": 298, "output_token_count": 427, "latency": 7.469860553741455}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 286, "output_token_count": 244, "latency": 4.2938454151153564}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}], "input_token_count": 294, "output_token_count": 373, "latency": 6.5375707149505615}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 279, "output_token_count": 290, "latency": 5.0973899364471436}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 298, "output_token_count": 415, "latency": 7.261908531188965}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 282, "output_token_count": 226, "latency": 3.980915069580078}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 296, "output_token_count": 436, "latency": 7.638689041137695}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 293, "output_token_count": 182, "latency": 3.225680351257324}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest\"}"}], "input_token_count": 295, "output_token_count": 366, "latency": 6.409379959106445}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\": \"user-mapping.yml\", \"config\": \"newMappingConfig\"}"}], "input_token_count": 291, "output_token_count": 248, "latency": 4.370960235595703}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 293, "output_token_count": 423, "latency": 7.397062063217163}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"[101, 202, 303]\", \"newStatus\": \"2\"}"}], "input_token_count": 297, "output_token_count": 369, "latency": 6.46587061882019}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\": \"LED TV\", \"recommendStatus\": \"1\", \"pageSize\": \"20\", \"pageNum\": \"3\"}"}], "input_token_count": 364, "output_token_count": 325, "latency": 5.712924242019653}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"[101, 102, 103]\", \"showStatus\": \"0\"}"}], "input_token_count": 305, "output_token_count": 243, "latency": 4.28184175491333}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 272, "output_token_count": 154, "latency": 2.7363789081573486}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"1004\", \"concurrency\": \"1007\", \"holdability\": \"2\"}"}], "input_token_count": 408, "output_token_count": 1459, "latency": 25.57573628425598}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"[2, 7, 11, 15]\", \"target\": \"9\"}"}], "input_token_count": 287, "output_token_count": 391, "latency": 6.845802068710327}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\", \"basicCredentials\": \"basicAuthCredentials\"}"}], "input_token_count": 333, "output_token_count": 344, "latency": 6.03053092956543}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\", \"builderExtractor\": \"builder -> Integer.toString(builder.getConcurrency())\"}"}], "input_token_count": 330, "output_token_count": 630, "latency": 11.005773782730103}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 311, "output_token_count": 219, "latency": 3.8632450103759766}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 266, "output_token_count": 260, "latency": 4.568834066390991}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\": \"5\", \"leaseTime\": \"120\", \"unit\": \"SECONDS\"}"}], "input_token_count": 347, "output_token_count": 698, "latency": 12.195129871368408}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 284, "output_token_count": 243, "latency": 4.298295736312866}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\": \"cleanupTask\", \"delay\": \"5\", \"unit\": \"TimeUnit.MINUTES\"}"}], "input_token_count": 324, "output_token_count": 387, "latency": 6.843241453170776}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\": \"BitOperation.AND\", \"destination\": \"[\\\"user:online:both\\\"]\", \"keys\": \"[\\\"user:online:today\\\", \\\"user:online:yesterday\\\"]\"}"}], "input_token_count": 376, "output_token_count": 1776, "latency": 31.140762090682983}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": \"[\\\"userID\\\", 42, \\\"username\\\", \\\"johndoe\\\", \\\"isActive\\\", true]\", \"state\": \"processingState\"}"}], "input_token_count": 310, "output_token_count": 1367, "latency": 23.95336389541626}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 279, "output_token_count": 182, "latency": 3.***************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"{\\\"name\\\": \\\"\\\", \\\"address\\\": \\\"\\\"}\"}"}], "input_token_count": 254, "output_token_count": 685, "latency": 11.**************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"logEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 282, "output_token_count": 288, "latency": 5.***************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\": \"shardA\", \"initializedShard\": \"shardB\"}"}], "input_token_count": 285, "output_token_count": 402, "latency": 7.**************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser\"}"}], "input_token_count": 230, "output_token_count": 454, "latency": 7.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\": \"usernameField\", \"value\": \"'JohnDoe'\", \"caseInsensitive\": \"true\"}"}], "input_token_count": 316, "output_token_count": 518, "latency": 9.**************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 328, "output_token_count": 243, "latency": 4.***************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 323, "output_token_count": 185, "latency": 3.293525218963623}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": \"5\"}"}], "input_token_count": 264, "output_token_count": 275, "latency": 4.864421129226685}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 310, "output_token_count": 318, "latency": 5.604407787322998}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESG\"}"}], "input_token_count": 310, "output_token_count": 1402, "latency": 24.5315420627594}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"new InetSocketAddress(\\\"************\\\", 8080)\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 347, "output_token_count": 326, "latency": 5.721690893173218}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 307, "output_token_count": 256, "latency": 4.5077128410339355}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 319, "output_token_count": 347, "latency": 6.108575105667114}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\": \"masterOperationTask\", \"request\": \"usageRequest\", \"state\": \"clusterState\", \"listener\": \"actionListener\"}"}], "input_token_count": 383, "output_token_count": 1137, "latency": 19.879775047302246}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"samlAssertionNode\", \"node_type\": \"Element.class\"}"}], "input_token_count": 292, "output_token_count": 477, "latency": 8.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 295, "output_token_count": 279, "latency": 4.***************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\", \"listener\": \"operationListener\"}"}], "input_token_count": 390, "output_token_count": 568, "latency": 9.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\": \"repositoriesService\", \"cache\": \"cacheService\", \"indexSettings\": \"indexSettingsForLogs\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"threadPool\": \"threadPool\", \"blobStoreCacheService\": \"blobStoreCacheService\", \"sharedBlobCacheService\": \"sharedBlobCacheService\"}"}], "input_token_count": 611, "output_token_count": 464, "latency": 8.***************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 341, "output_token_count": 294, "latency": 5.****************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\": \"yes\", \"defaultValue\": \"false\"}"}], "input_token_count": 296, "output_token_count": 1517, "latency": 26.542965173721313}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\": \"new HashMap<String, Object>() {{ put(\\\"name\\\", \\\"John Doe\\\"); put(\\\"age\\\", 30); put(\\\"email\\\", \\\"<EMAIL>\\\"); }}\", \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 367, "output_token_count": 448, "latency": 7.861225128173828}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\": \"System.console()\", \"shardPath\": \"new ShardPath(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard\\\"))\", \"indexDirectory\": \"FSDirectory.open(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard/index\\\"))\"}"}], "input_token_count": 411, "output_token_count": 1776, "latency": 31.140928506851196}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\"}"}], "input_token_count": 317, "output_token_count": 540, "latency": 9.469297885894775}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\": \"timestamp\", \"origin\": \"now\", \"scale\": \"10d\", \"offset\": \"2d\", \"decay\": \"0.5\"}"}], "input_token_count": 422, "output_token_count": 368, "latency": 6.535727262496948}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"queryType\": \"FLOAT\", \"from\": \"20.5\", \"to\": \"30.0\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 453, "output_token_count": 334, "latency": 5.8932273387908936}
{"id": "java_63", "result": [{"withinQuery": "{\"field\": \"age\", \"from\": \"30\", \"to\": \"40\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 399, "output_token_count": 362, "latency": 6.375658988952637}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": \"{\\\"format\\\": \\\"epoch_millis\\\"}\", \"onScriptError\": \"FAIL\"}"}], "input_token_count": 410, "output_token_count": 377, "latency": 6.637261390686035}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\": \"xContentBuilderInstance\", \"params\": [\"include_defaults\", \"skip_runtime_fields\"]}"}], "input_token_count": 309, "output_token_count": 537, "latency": 9.416279554367065}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"onScriptError.IGNORE\"}"}], "input_token_count": 385, "output_token_count": 342, "latency": 6.022469520568848}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\": \"/Applications/PhotoEditor.app\", \"params\": \"{appName=PhotoEditor, imagesRoot=/resources/images, backgroundImageFolder=backgrounds, installDir=/Applications}\"}"}], "input_token_count": 315, "output_token_count": 1210, "latency": 21.161430835723877}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": \"{\\\"appImagePath\\\": \\\"/Applications/MyApp.app\\\", \\\"appName\\\": \\\"MyApp\\\"}\"}"}], "input_token_count": 270, "output_token_count": 604, "latency": 10.555803298950195}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"start\": \"2\", \"end\": \"5\"}"}], "input_token_count": 352, "output_token_count": 437, "latency": 7.6634907722473145}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\": \"{http://www.example.com}item\", \"augs\": \"augmentations\"}"}], "input_token_count": 321, "output_token_count": 1835, "latency": 32.121325731277466}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 348, "output_token_count": 174, "latency": 3.0891876220703125}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}], "input_token_count": 351, "output_token_count": 354, "latency": 6.240013122558594}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\": \"UTF-8\", \"allowJavaNames\": \"true\"}"}], "input_token_count": 269, "output_token_count": 227, "latency": 4.026343822479248}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"false\"}"}], "input_token_count": 333, "output_token_count": 448, "latency": 7.868712902069092}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 285, "output_token_count": 463, "latency": 8.101754903793335}
{"id": "java_76", "result": [{"Intro_step": "{\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 258, "output_token_count": 278, "latency": 4.883239030838013}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 322, "output_token_count": 432, "latency": 7.564687967300415}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"quiet\", \"otherDependents\": [\"verbose\"]}"}], "input_token_count": 287, "output_token_count": 590, "latency": 10.304720163345337}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 313, "output_token_count": 1127, "latency": 19.752283334732056}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 281, "output_token_count": 258, "latency": 4.5380659103393555}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\": \"humongous-test-case.json\", \"doGC\": \"() -> CustomGarbageCollector.run()\", \"checker\": \"referenceChecker\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": [\"GC pause\"], \"shouldNotContain\": [\"OutOfMemoryError\"]}"}], "input_token_count": 534, "output_token_count": 1056, "latency": 18.501168251037598}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"[\\\"testArgs\\\"]\", \"out\": \"System.out\"}"}], "input_token_count": 324, "output_token_count": 3012, "latency": 53.00214886665344}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": \"[\\\"-waitTime\\\", \\\"2\\\", \\\"-iterations\\\", \\\"500\\\"]\", \"out\": \"System.out\"}"}], "input_token_count": 354, "output_token_count": 2888, "latency": 51.659050941467285}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 301, "output_token_count": 386, "latency": 7.030988931655884}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"[\\\"/path/to/classes\\\", \\\"60\\\"]\", \"out\": \"logStream\"}"}], "input_token_count": 351, "output_token_count": 2506, "latency": 44.03123378753662}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"{\\\"-v\\\", \\\"--no-strict\\\"}\", \"out\": \"debugOutput\"}"}], "input_token_count": 319, "output_token_count": 1948, "latency": 34.13772797584534}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"1\", \"property\": \"deathEvent001\"}"}], "input_token_count": 288, "output_token_count": 2730, "latency": 49.49199652671814}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\": \"mainThread\", \"testedClass\": \"com.example.MainClass\", \"suspendPolicy\": \"2\", \"property\": \"testProperty\"}"}], "input_token_count": 385, "output_token_count": 526, "latency": 9.21547245979309}
{"id": "java_89", "result": [{"runThis": "{\"argv\": \"[\\\"-waitTime\\\", \\\"120\\\", \\\"-debuggeeName\\\", \\\"TestDebuggee\\\"]\", \"out\": \"testLogStream\"}"}], "input_token_count": 335, "output_token_count": 2956, "latency": 52.28910684585571}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": [\"-v\", \"-p\"], \"out\": \"System.out\"}"}], "input_token_count": 311, "output_token_count": 285, "latency": 5.013016223907471}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": \"[\\\"suspend\\\", \\\"log\\\"]\", \"out\": \"debugLog\"}"}], "input_token_count": 307, "output_token_count": 1352, "latency": 23.809858083724976}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\"}"}], "input_token_count": 290, "output_token_count": 369, "latency": 6.474876642227173}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 299, "output_token_count": 265, "latency": 4.663588523864746}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\"}"}], "input_token_count": 268, "output_token_count": 155, "latency": 2.7555129528045654}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\": \"ExportInlinedObject1Instance\", \"argument\": \"HelloWorld\", \"node\": \"InlinableNodeInstance\", \"library\": \"NodeLibraryInstance\"}"}], "input_token_count": 389, "output_token_count": 303, "latency": 5.328438997268677}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 350, "output_token_count": 424, "latency": 7.427680015563965}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 286, "output_token_count": 307, "latency": 6.8598644733428955}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 281, "output_token_count": 251, "latency": 4.426858425140381}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 295, "output_token_count": 349, "latency": 6.1200268268585205}
