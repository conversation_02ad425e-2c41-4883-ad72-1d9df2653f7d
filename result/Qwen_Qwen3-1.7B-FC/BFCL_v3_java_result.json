{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 284, "output_token_count": 157, "latency": 0.8053908348083496}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"Customers\", \"useShortName\": \"true\", \"params\": \"{\\\"limit\\\":\\\"50\\\",\\\"schema\\\":\\\"public\\\"}\"}"}], "input_token_count": 329, "output_token_count": 343, "latency": 1.7453117370605469}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"view\": \"EmployeeView\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 342, "output_token_count": 318, "latency": 1.62109375}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\": \"dbMonitor\", \"dataSource\": \"db2DataSource\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 349, "output_token_count": 240, "latency": 1.233199119567871}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\": \"jdbcSession\", \"db2ViewBase\": \"HR.EmployeeView\"}"}], "input_token_count": 297, "output_token_count": 231, "latency": 1.1782302856445312}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 305, "output_token_count": 486, "latency": 2.4636449813842773}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\": \"true\", \"append\": \"true\", \"keepState\": \"true\"}"}], "input_token_count": 316, "output_token_count": 270, "latency": 1.3937411308288574}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 357, "output_token_count": 286, "latency": 1.461925983428955}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\": \"fileStream\", \"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\"}"}], "input_token_count": 385, "output_token_count": 236, "latency": 1.2038164138793945}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\": \"[[1,2], [3,4], [5,6], [7,8], [9,10]]\", \"xyzmMode\": \"XYZ\", \"buffer\": \"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 407, "output_token_count": 651, "latency": 3.307476043701172}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 263, "output_token_count": 142, "latency": 0.7326018810272217}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\": \"HKEY_LOCAL_MACHINE\", \"property\": \"EnableExtensions\"}"}], "input_token_count": 298, "output_token_count": 417, "latency": 2.1119508743286133}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 286, "output_token_count": 250, "latency": 1.2736797332763672}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}], "input_token_count": 294, "output_token_count": 337, "latency": 1.7131729125976562}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 279, "output_token_count": 349, "latency": 1.7710707187652588}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 298, "output_token_count": 169, "latency": 0.8769991397857666}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 282, "output_token_count": 238, "latency": 1.2140932083129883}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 296, "output_token_count": 386, "latency": 1.965613603591919}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 293, "output_token_count": 174, "latency": 0.8958661556243896}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest?username=john_doe\"}"}], "input_token_count": 295, "output_token_count": 430, "latency": 2.1891791820526123}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\": \"user-mapping.yml\", \"config\": \"newMappingConfig\"}"}], "input_token_count": 291, "output_token_count": 312, "latency": 1.5924487113952637}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 293, "output_token_count": 349, "latency": 1.777024269104004}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"101,202,303\", \"newStatus\": \"2\"}"}], "input_token_count": 297, "output_token_count": 209, "latency": 1.0709059238433838}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\": \"LED TV\", \"recommendStatus\": \"1\", \"pageSize\": \"20\", \"pageNum\": \"3\"}"}], "input_token_count": 364, "output_token_count": 285, "latency": 1.454160451889038}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"101,102,103\", \"showStatus\": \"0\"}"}], "input_token_count": 305, "output_token_count": 259, "latency": 1.3196039199829102}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 272, "output_token_count": 172, "latency": 0.88051438331604}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"TYPE_SCROLL_INSENSITIVE\", \"concurrency\": \"CONCUR_READ_ONLY\", \"holdability\": \"CLOSE_CURSORS_AT_COMMIT\"}"}], "input_token_count": 408, "output_token_count": 631, "latency": 3.207317590713501}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"[2, 7, 11, 15]\", \"target\": \"9\"}"}], "input_token_count": 287, "output_token_count": 431, "latency": 2.186323642730713}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\", \"basicCredentials\": \"basicAuthCredentials\"}"}], "input_token_count": 333, "output_token_count": 464, "latency": 2.3555376529693604}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\", \"builderExtractor\": \"builder.getConcurrency()\"}"}], "input_token_count": 330, "output_token_count": 384, "latency": 1.950049638748169}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 311, "output_token_count": 275, "latency": 1.4007256031036377}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 266, "output_token_count": 249, "latency": 1.269559621810913}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\": \"5\", \"leaseTime\": \"120\", \"unit\": \"SECONDS\"}"}], "input_token_count": 347, "output_token_count": 304, "latency": 1.5464599132537842}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 284, "output_token_count": 193, "latency": 0.9847393035888672}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\": \"cleanupTask\", \"delay\": \"300\", \"unit\": \"SECONDS\"}"}], "input_token_count": 324, "output_token_count": 244, "latency": 1.2496683597564697}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\": \"AND\", \"destination\": \"user:online:both\", \"keys\": [\"user:online:today\", \"user:online:yesterday\"]}"}], "input_token_count": 376, "output_token_count": 324, "latency": 1.6499452590942383}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": \"['userID', 42, 'username', 'johndoe', 'isActive', true]\", \"state\": \"processingState\"}"}], "input_token_count": 310, "output_token_count": 726, "latency": 3.6780998706817627}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 279, "output_token_count": 305, "latency": 1.****************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"{\\\"name\\\": \\\"John Doe\\\", \\\"address\\\": \\\"123 Main St\\\"}\"}"}], "input_token_count": 254, "output_token_count": 330, "latency": 1.****************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"logEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 282, "output_token_count": 320, "latency": 1.****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\": \"shardA\", \"initializedShard\": \"shardB\"}"}], "input_token_count": 285, "output_token_count": 283, "latency": 1.****************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser\"}"}], "input_token_count": 230, "output_token_count": 304, "latency": 1.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\": \"usernameField\", \"value\": \"JohnDoe\", \"caseInsensitive\": \"true\"}"}], "input_token_count": 316, "output_token_count": 415, "latency": 2.****************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 328, "output_token_count": 268, "latency": 1.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 323, "output_token_count": 188, "latency": 0.9613163471221924}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": \"5\"}"}], "input_token_count": 264, "output_token_count": 584, "latency": 2.964620590209961}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 310, "output_token_count": 326, "latency": 1.662360429763794}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESG\"}"}], "input_token_count": 310, "output_token_count": 591, "latency": 2.9910333156585693}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"192.168.1.10:8080\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 347, "output_token_count": 405, "latency": 2.052949905395508}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 307, "output_token_count": 260, "latency": 1.3242030143737793}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 319, "output_token_count": 277, "latency": 1.4071199893951416}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\": \"get_ccr_usage\", \"request\": \"XPackUsageRequest{...}\", \"state\": \"ClusterState{...}\", \"listener\": \"ActionListener{...}\"}"}], "input_token_count": 383, "output_token_count": 250, "latency": 1.2764418125152588}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"SAMLAssertionNode\", \"node_type\": \"Element.class\"}"}], "input_token_count": 292, "output_token_count": 323, "latency": 1.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 295, "output_token_count": 359, "latency": 1.***************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\", \"listener\": \"operationListener\"}"}], "input_token_count": 390, "output_token_count": 290, "latency": 1.****************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\": \"daily-snapshots\", \"cache\": \"cacheService\", \"indexSettings\": \"indexSettingsForLogs\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"threadPool\": \"threadPool\", \"blobStoreCacheService\": \"blobStoreCacheService\", \"sharedBlobCacheService\": \"sharedBlobCacheService\"}"}], "input_token_count": 611, "output_token_count": 630, "latency": 3.***************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 341, "output_token_count": 168, "latency": 0.****************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\": \"yes\", \"defaultValue\": \"false\"}"}], "input_token_count": 296, "output_token_count": 329, "latency": 1.6652445793151855}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\": \"{\\\"name\\\": \\\"John Doe\\\", \\\"age\\\": 30, \\\"email\\\": \\\"<EMAIL>\\\"}\", \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 367, "output_token_count": 288, "latency": 1.4668140411376953}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\": \"terminal\", \"shardPath\": \"/var/data/elasticsearch/nodes/0/indices/1shard\", \"indexDirectory\": \"/var/data/elasticsearch/nodes/0/indices/1shard/index\"}"}], "input_token_count": 411, "output_token_count": 523, "latency": 2.656416177749634}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\"}"}], "input_token_count": 317, "output_token_count": 990, "latency": 5.01800537109375}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\": \"timestamp\", \"origin\": \"now\", \"scale\": \"10d\", \"decay\": \"0.5\", \"offset\": \"2d\"}"}], "input_token_count": 422, "output_token_count": 374, "latency": 1.9043357372283936}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"queryType\": \"FLOAT\", \"from\": \"20.5\", \"to\": \"30.0\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 453, "output_token_count": 352, "latency": 1.794233798980713}
{"id": "java_63", "result": [{"withinQuery": "{\"field\": \"age\", \"from\": \"30\", \"to\": \"40\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 399, "output_token_count": 308, "latency": 1.5723330974578857}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": \"{\\\"format\\\": \\\"epoch_millis\\\"}\", \"onScriptError\": \"FAIL\"}"}], "input_token_count": 410, "output_token_count": 371, "latency": 1.8838427066802979}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\": \"xContentBuilderInstance\", \"params\": [\"includeDefaults=true\", \"skipRuntimeFields=true\"]}"}], "input_token_count": 309, "output_token_count": 382, "latency": 1.9345118999481201}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"onScriptError.IGNORE\"}"}], "input_token_count": 385, "output_token_count": 355, "latency": 1.8061554431915283}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\": \"/Applications/PhotoEditor.app\", \"params\": \"{\\\"applicationName\\\":\\\"PhotoEditor\\\",\\\"imagesRoot\\\":\\\"/Applications/PhotoEditor.app/images\\\",\\\"backgroundImageFolder\\\":\\\"/Applications/PhotoEditor.app/images/background\\\"}\"}"}], "input_token_count": 315, "output_token_count": 587, "latency": 2.983250141143799}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": \"{\\\"appImagePath\\\": \\\"/Applications/MyApp.app\\\", \\\"appName\\\": \\\"MyApp\\\"}\"}"}], "input_token_count": 270, "output_token_count": 459, "latency": 2.330049514770508}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"[123, 456, 789, 1011]\", \"start\": \"2\", \"end\": \"5\"}"}], "input_token_count": 352, "output_token_count": 406, "latency": 2.0635387897491455}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\": \"ex:item\", \"augs\": \"augmentations\"}"}], "input_token_count": 321, "output_token_count": 341, "latency": 1.7412314414978027}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 348, "output_token_count": 262, "latency": 1.3401474952697754}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}], "input_token_count": 351, "output_token_count": 457, "latency": 2.3317649364471436}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\": \"UTF-8\", \"allowJavaNames\": \"true\"}"}], "input_token_count": 269, "output_token_count": 168, "latency": 0.8679888248443604}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"false\"}"}], "input_token_count": 333, "output_token_count": 443, "latency": 2.2500040531158447}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 285, "output_token_count": 195, "latency": 0.9975683689117432}
{"id": "java_76", "result": [{"Intro_step": "{\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 258, "output_token_count": 222, "latency": 1.1317551136016846}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 322, "output_token_count": 520, "latency": 2.6449737548828125}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"output-format\", \"otherDependents\": \"quiet verbose\"}"}], "input_token_count": 287, "output_token_count": 425, "latency": 2.1560845375061035}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 313, "output_token_count": 316, "latency": 1.609436273574829}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 281, "output_token_count": 312, "latency": 1.5846755504608154}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\": \"humongous-test-case.json\", \"doGC\": \"customGC\", \"checker\": \"referenceChecker\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": \"GC pause\", \"shouldNotContain\": \"OutOfMemoryError\"}"}], "input_token_count": 534, "output_token_count": 826, "latency": 4.218154430389404}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": [\"testArgs\"], \"out\": \"System.out\"}"}], "input_token_count": 324, "output_token_count": 559, "latency": 2.83609938621521}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": [\"-waitTime 2\", \"-iterations 500\"], \"out\": \"System.out\"}"}], "input_token_count": 354, "output_token_count": 313, "latency": 1.5969650745391846}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 301, "output_token_count": 639, "latency": 3.2399039268493652}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"[/path/to/classes, 60]\", \"out\": \"logStream\"}"}], "input_token_count": 351, "output_token_count": 891, "latency": 4.526973485946655}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"['-v', '--no-strict']\", \"out\": \"debugOutput\"}"}], "input_token_count": 319, "output_token_count": 243, "latency": 1.2435860633850098}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"EVENT_THREAD\", \"property\": \"testProperty=deathEvent001\"}"}], "input_token_count": 288, "output_token_count": 571, "latency": 2.8944740295410156}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\": \"mainThread\", \"testedClass\": \"com.example.MainClass\", \"suspendPolicy\": \"EventRequest.SUSPEND_ALL\", \"property\": \"testProperty\"}"}], "input_token_count": 385, "output_token_count": 403, "latency": 2.0509097576141357}
{"id": "java_89", "result": [{"runThis": "{\"argv\": [\"-waitTime 2\", \"-debuggeeName TestDebuggee\"], \"out\": \"testLogStream\"}"}], "input_token_count": 335, "output_token_count": 406, "latency": 2.0677692890167236}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": \"-v -p\", \"out\": \"System.out\"}"}], "input_token_count": 311, "output_token_count": 334, "latency": 1.6984591484069824}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": [\"suspend\", \"log\"], \"out\": \"debugLog\"}"}], "input_token_count": 307, "output_token_count": 623, "latency": 3.163480281829834}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes/home/<USER>/java/libs\"}"}], "input_token_count": 290, "output_token_count": 440, "latency": 2.2343719005584717}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 299, "output_token_count": 258, "latency": 1.312638759613037}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\", \"recursive\": null}"}], "input_token_count": 268, "output_token_count": 347, "latency": 1.76344633102417}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\": \"ExportInlinedObject1Instance\", \"argument\": \"HelloWorld\", \"node\": \"InlinableNodeInstance\", \"library\": \"NodeLibraryInstance\"}"}], "input_token_count": 389, "output_token_count": 282, "latency": 1.4403808116912842}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 350, "output_token_count": 389, "latency": 1.9778120517730713}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 286, "output_token_count": 257, "latency": 1.3108994960784912}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 281, "output_token_count": 398, "latency": 2.0169336795806885}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 295, "output_token_count": 214, "latency": 1.0978600978851318}
