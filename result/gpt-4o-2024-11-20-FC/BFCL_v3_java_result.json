{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\":\"mapController\",\"parent\":\"mapArea\"}"}], "input_token_count": 165, "output_token_count": 22, "latency": 0.8330755233764648}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\":\"Customers\",\"useShortName\":\"true\",\"params\":\"{\\\"limit\\\": \\\"50\\\", \\\"schemaFilter\\\": \\\"public\\\"}\"}"}], "input_token_count": 193, "output_token_count": 43, "latency": 1.3437755107879639}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\":\"dbMonitor\",\"view\":\"EmployeeView\",\"source\":\"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 209, "output_token_count": 39, "latency": 0.7421598434448242}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\":\"dbMonitor\",\"dataSource\":\"db2DataSource\",\"reference\":\"USERSPACE1\"}"}], "input_token_count": 220, "output_token_count": 36, "latency": 1.7098591327667236}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\":\"jdbcSession\",\"db2ViewBase\":\"HR.EmployeeView\"}"}], "input_token_count": 174, "output_token_count": 31, "latency": 1.0136597156524658}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\":\"dataController\",\"parent\":\"compositeParent\"}"}], "input_token_count": 188, "output_token_count": 25, "latency": 0.8960742950439453}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\":\"true\",\"append\":\"true\",\"keepState\":\"true\"}"}], "input_token_count": 178, "output_token_count": 28, "latency": 0.7340230941772461}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\":\"/backup/data.txt\",\"force\":\"true\",\"monitor\":\"progressTracker\"}"}], "input_token_count": 223, "output_token_count": 30, "latency": 0.7541725635528564}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\":\"fileStream\",\"force\":\"true\",\"keepHistory\":\"false\",\"monitor\":\"progressMonitor\"}"}], "input_token_count": 241, "output_token_count": 34, "latency": 1.1188437938690186}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\":\"new MultiPoint(Arrays.asList(new Point(1,2), new Point(3,4), new Point(5,6), new Point(7,8), new Point(9,10)))\",\"xyzmMode\":\"XyzmMode.XYZ\",\"buffer\":\"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 273, "output_token_count": 77, "latency": 1.1097571849822998}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\":\"/usr/local/bin/dbeaver\",\"name\":\"DBeaverLauncher\"}"}], "input_token_count": 144, "output_token_count": 32, "latency": 0.9365489482879639}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\":\"HKEY_LOCAL_MACHINE\",\"property\":\"EnableExtensions\"}"}], "input_token_count": 178, "output_token_count": 28, "latency": 0.7959754467010498}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\":\"progressMonitor\",\"schemaName\":\"AnalyticsDB\"}"}], "input_token_count": 164, "output_token_count": 26, "latency": 4.524818181991577}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\":\"JDBC_session\",\"owner\":\"JohnDoe\"}"}], "input_token_count": 173, "output_token_count": 27, "latency": 0.7706999778747559}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\":\"gameLayout\",\"success\":\"true\"}"}], "input_token_count": 160, "output_token_count": 23, "latency": 0.6664764881134033}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\":\"imageInputStream\",\"out\":\"imageOutputStream\"}"}], "input_token_count": 181, "output_token_count": 26, "latency": 1.4367649555206299}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\":\"instructionData\",\"isRange\":\"true\"}"}], "input_token_count": 164, "output_token_count": 27, "latency": 0.8832361698150635}
{"id": "java_17", "result": "To attach generic type information to a constructor invocation instruction (`newConstructorInsn`) within a method (`initMethod`) in a Java decompiler analysis tool, we need to follow these steps:\n\n1. **Identify the `MethodNode`**: The `initMethod` represents the method that contains the constructor invocation instruction. This is represented as a `MethodNode` in the Java decompiler tool.\n\n2. **Identify the `ConstructorInsn`**: The `newConstructorInsn` refers to the specific constructor invocation instruction within `initMethod`. This is represented as a `ConstructorInsn` object.\n\n3. **Attach Generic Type Information**: Using the `GenericTypesVisitor_attachGenericTypesInfo` function, we can attach generic type information to the `newConstructorInsn`.\n\nPlease provide the string representations of the `MethodNode` (`initMethod`) and the `ConstructorInsn` (`newConstructorInsn`) objects, so I can assist you in attaching the generic type information.", "input_token_count": 177, "output_token_count": 195, "latency": 2.7711687088012695}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\":\"3\",\"pageSize\":\"20\"}"}], "input_token_count": 172, "output_token_count": 25, "latency": 0.7953567504882812}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\":\"webModel\",\"request\":\"userRequest\"}"}], "input_token_count": 179, "output_token_count": 22, "latency": 0.7369482517242432}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\":\"user-mapping.yml\",\"config\":\"newMappingConfig\"}"}], "input_token_count": 173, "output_token_count": 27, "latency": 0.8945319652557373}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\":\"nettyChannelContext\",\"e\":\"ioExceptionEvent\"}"}], "input_token_count": 176, "output_token_count": 25, "latency": 0.6199676990509033}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\":\"[101, 202, 303]\",\"newStatus\":\"2\"}"}], "input_token_count": 173, "output_token_count": 33, "latency": 1.10377836227417}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\":\"LED TV\",\"recommendStatus\":\"1\",\"pageSize\":\"20\",\"pageNum\":\"3\"}"}], "input_token_count": 218, "output_token_count": 37, "latency": 0.9950129985809326}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\":\"[101,102,103]\",\"showStatus\":\"0\"}"}], "input_token_count": 180, "output_token_count": 31, "latency": 0.8883786201477051}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\":\"42\",\"sort\":\"5\"}"}], "input_token_count": 151, "output_token_count": 24, "latency": 0.8660695552825928}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\":\"CALL totalSales(?)\",\"resultSetType\":\"ResultSet.TYPE_SCROLL_INSENSITIVE\",\"concurrency\":\"ResultSet.CONCUR_READ_ONLY\",\"holdability\":\"ResultSet.CLOSE_CURSORS_AT_COMMIT\"}"}], "input_token_count": 268, "output_token_count": 54, "latency": 0.787867546081543}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\":\"[2, 7, 11, 15]\",\"target\":\"9\"}"}], "input_token_count": 165, "output_token_count": 32, "latency": 1.0108578205108643}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\":\"es_credentials.properties\",\"credentialsRefreshInterval\":\"30\",\"basicCredentials\":\"basicAuthCredentials\"}"}], "input_token_count": 196, "output_token_count": 35, "latency": 0.8434526920318604}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\":\"zipkin.collector.activemq.concurrency\",\"value\":\"10\",\"builderExtractor\":\"ActiveMQCollector.Builder::concurrency\"}"}], "input_token_count": 194, "output_token_count": 39, "latency": 0.9500036239624023}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\":\"answer\",\"value\":\"42\"}"}], "input_token_count": 193, "output_token_count": 23, "latency": 1.6730327606201172}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\":\"taskQueue\",\"codec\":\"jsonCodec\"}"}], "input_token_count": 145, "output_token_count": 23, "latency": 0.8741276264190674}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\":\"5\",\"leaseTime\":\"120\",\"unit\":\"SECONDS\"}"}], "input_token_count": 211, "output_token_count": 31, "latency": 2.8453521728515625}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\":\"employee:1234\",\"value\":\"John Doe\"}"}], "input_token_count": 162, "output_token_count": 27, "latency": 0.9695148468017578}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\":\"cleanupTask\",\"delay\":\"300\",\"unit\":\"TimeUnit.SECONDS\"}"}], "input_token_count": 193, "output_token_count": 27, "latency": 1.1081821918487549}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\":\"AND\",\"destination\":\"user:online:both\",\"keys\":\"[\\\"user:online:today\\\",\\\"user:online:yesterday\\\"]\"}"}], "input_token_count": 247, "output_token_count": 42, "latency": 1.0857067108154297}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\":\"['userID', 42, 'username', 'johndoe', 'isActive', true]\",\"state\":\"processingState\"}"}], "input_token_count": 192, "output_token_count": 44, "latency": 0.8620331287384033}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\":\"jenkinsBuild\",\"text\":\"buildOutput\"}"}], "input_token_count": 162, "output_token_count": 25, "latency": 1.****************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\":\"{\\\"name\\\":{},\\\"address\\\":{}}\"}"}], "input_token_count": 149, "output_token_count": 29, "latency": 1.****************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\":\"logEvent\",\"toAppendTo\":\"logBuilder\"}"}], "input_token_count": 165, "output_token_count": 24, "latency": 0.****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\":\"shardA\",\"initializedShard\":\"shardB\"}"}], "input_token_count": 159, "output_token_count": 31, "latency": 1.****************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\":\"searchHitParser\"}"}], "input_token_count": 124, "output_token_count": 22, "latency": 0.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\":\"usernameField\",\"value\":\"JohnDoe\",\"caseInsensitive\":\"true\"}"}], "input_token_count": 183, "output_token_count": 29, "latency": 0.****************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\":\"mockSettings\",\"handler\":\"mockHandler\",\"object\":\"testObject\"}"}], "input_token_count": 196, "output_token_count": 29, "latency": 0.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\":\"DESede\",\"mode\":\"CBC\",\"padding\":\"PKCS5Padding\"}"}], "input_token_count": 188, "output_token_count": 28, "latency": 1.3242602348327637}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\":\"envVariables\",\"size\":\"5\"}"}], "input_token_count": 145, "output_token_count": 21, "latency": 5.937643051147461}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\":\"csmInstance\",\"expected\":\"MyExpectedClass.class\"}"}], "input_token_count": 190, "output_token_count": 28, "latency": 0.7946567535400391}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\":\"CERTIFICATE\",\"value\":\"MIIFdTCCBF2gAwIBAgISESG\"}"}], "input_token_count": 185, "output_token_count": 34, "latency": 0.8651876449584961}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\":\"new InetSocketAddress(\\\"************\\\", 8080)\",\"sslContext\":\"testSSLContext\"}"}], "input_token_count": 215, "output_token_count": 39, "latency": 0.8202767372131348}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\":\"404\",\"responseLength\":\"1500\"}"}], "input_token_count": 181, "output_token_count": 27, "latency": 0.7577838897705078}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\":\"deleteQueryRequest\",\"responseListener\":\"testListener\"}"}], "input_token_count": 195, "output_token_count": 31, "latency": 1.232417106628418}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\":\"taskObject\",\"request\":\"usageRequest\",\"state\":\"clusterState\",\"listener\":\"actionListener\"}"}], "input_token_count": 239, "output_token_count": 35, "latency": 1.5553233623504639}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\":\"SAMLAssertionNode\",\"node_type\":\"Element.class\"}"}], "input_token_count": 169, "output_token_count": 28, "latency": 1.****************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\":\"42\",\"localAcceptedVersion\":\"7\"}"}], "input_token_count": 168, "output_token_count": 30, "latency": 0.****************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\":\"snapshotRequest\",\"shardRouting\":\"shardRouteInfo\",\"task\":\"snapshotTask\",\"listener\":\"operationListener\"}"}], "input_token_count": 244, "output_token_count": 41, "latency": 0.****************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\":\"repositoriesService\",\"cache\":\"cacheService\",\"indexSettings\":\"indexSettingsForLogs\",\"shardPath\":\"/data/nodes/0/indices/logs/5\",\"currentTimeNanosSupplier\":\"currentTimeNanos\",\"threadPool\":\"threadPool\",\"blobStoreCacheService\":\"blobStoreCacheService\",\"sharedBlobCacheService\":\"sharedBlobCacheService\"}"}], "input_token_count": 404, "output_token_count": 85, "latency": 1.****************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\":\"httpResponseEntity\",\"entityParser\":\"responseParser\",\"parserConfig\":\"defaultParserConfig\"}"}], "input_token_count": 206, "output_token_count": 34, "latency": 0.****************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\":\"yes\",\"defaultValue\":\"false\"}"}], "input_token_count": 177, "output_token_count": 25, "latency": 1.0640208721160889}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\":\"{'name':'string','age':'int','email':'string'}\",\"ensureNoSelfReferences\":\"true\",\"writeStartAndEndHeaders\":\"true\"}"}], "input_token_count": 229, "output_token_count": 43, "latency": 1.0039169788360596}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\":\"System.out\",\"shardPath\":\"Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard\\\")\",\"indexDirectory\":\"FSDirectory.open(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard/index\\\"))\"}"}], "input_token_count": 277, "output_token_count": 71, "latency": 1.2553272247314453}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\":\"mainSearchContext\",\"innerHitsContext\":\"hitsContext\"}"}], "input_token_count": 194, "output_token_count": 29, "latency": 0.910973072052002}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\":\"timestamp\",\"origin\":\"now\",\"scale\":\"10d\",\"offset\":\"2d\",\"decay\":\"0.5\"}"}], "input_token_count": 266, "output_token_count": 41, "latency": 1.025818109512329}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\":\"temperature\",\"queryType\":\"FLOAT\",\"from\":\"20.5\",\"to\":\"30.0\",\"includeFrom\":\"true\",\"includeTo\":\"false\"}"}], "input_token_count": 277, "output_token_count": 42, "latency": 0.8397047519683838}
{"id": "java_63", "result": [{"withinQuery": "{\"field\":\"age\",\"from\":\"30\",\"to\":\"40\",\"includeFrom\":\"true\",\"includeTo\":\"false\"}"}], "input_token_count": 235, "output_token_count": 33, "latency": 1.2095680236816406}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\":\"timestamp\",\"factory\":\"dateFactory\",\"script\":\"dateScript\",\"meta\":\"{\\\"format\\\": \\\"epoch_millis\\\"}\",\"onScriptError\":\"FAIL\"}"}], "input_token_count": 253, "output_token_count": 47, "latency": 0.9394092559814453}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\":\"xContentBuilderInstance\",\"params\":\"[\\\"include_defaults\\\", \\\"skip_runtime_fields\\\"]\"}"}], "input_token_count": 191, "output_token_count": 34, "latency": 0.8795371055603027}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\":\"mappingParserContext\",\"parent\":\"compositeField1\",\"parentScriptFactory\":\"compositeScriptFactory\",\"onScriptError\":\"onScriptError.IGNORE\"}"}], "input_token_count": 240, "output_token_count": 48, "latency": 0.9694056510925293}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\":\"/Applications/PhotoEditor.app\",\"params\":\"{\\\"appName\\\":\\\"PhotoEditor\\\",\\\"imagesRoot\\\":\\\"/path/to/images\\\",\\\"backgroundImage\\\":\\\"/path/to/images/background.png\\\",\\\"volumeURL\\\":\\\"/Volumes/PhotoEditor\\\",\\\"installDir\\\":\\\"/Applications\\\"}\"}"}], "input_token_count": 197, "output_token_count": 73, "latency": 1.3257873058319092}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\":\"{applicationImage: '/Applications/MyApp.app', applicationName: 'MyApp'}\"}"}], "input_token_count": 163, "output_token_count": 40, "latency": 0.978367805480957}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\":\"durations\",\"start\":\"2\",\"end\":\"5\"}"}], "input_token_count": 222, "output_token_count": 25, "latency": 0.861595630645752}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\":\"{namespaceURI='http://www.example.com', localPart='item', prefix='ex'}\",\"augs\":\"augmentations\"}"}], "input_token_count": 200, "output_token_count": 41, "latency": 0.7864589691162109}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\":\"resultData\",\"thisCoroutine\":\"5\",\"toCoroutine\":\"10\"}"}], "input_token_count": 211, "output_token_count": 31, "latency": 0.9027359485626221}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\":\"textBuffer\",\"start\":\"5\",\"length\":\"10\"}"}], "input_token_count": 219, "output_token_count": 26, "latency": 1.0302937030792236}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\":\"UTF-8\",\"allowJavaNames\":\"true\"}"}], "input_token_count": 146, "output_token_count": 26, "latency": 0.721240758895874}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\":\"55357\",\"low\":\"56832\",\"inContent\":\"true\"}"}], "input_token_count": 194, "output_token_count": 29, "latency": 0.7775390148162842}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\":\"XML_SECURITY\",\"sysPropertyName\":\"enableXmlSecurityFeature\"}"}], "input_token_count": 167, "output_token_count": 29, "latency": 0.8074097633361816}
{"id": "java_76", "result": [{"Intro_step": "{\"w\":\"800\",\"h\":\"600\"}"}], "input_token_count": 135, "output_token_count": 19, "latency": 1.064030408859253}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"password\":\"P@ssw0rd!\"}"}], "input_token_count": 178, "output_token_count": 63, "latency": 1.0437963008880615}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"quiet\", \"otherDependents\": \"verbose\"}"}, {"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"verbose\", \"otherDependents\": \"quiet\"}"}], "input_token_count": 170, "output_token_count": 62, "latency": 0.9596598148345947}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\":\"1234\",\"sysId\":\"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 189, "output_token_count": 34, "latency": 1.796804428100586}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\":\"failOn\",\"ruleIdx\":\"42\"}"}], "input_token_count": 159, "output_token_count": 23, "latency": 0.580721378326416}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\":\"humongous-test-case.json\",\"doGC\":\"customGarbageCollector()\",\"checker\":\"referenceChecker()\",\"gcLogName\":\"gc-analysis.log\",\"shouldContain\":\"[\\\"GC pause\\\"]\",\"shouldNotContain\":\"[\\\"OutOfMemoryError\\\"]\"}"}], "input_token_count": 357, "output_token_count": 70, "latency": 1.3206465244293213}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\":\"testArgs\",\"out\":\"System.out\"}"}], "input_token_count": 202, "output_token_count": 23, "latency": 0.7916312217712402}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\":\"-waitTime, 120, -iterations, 500\",\"out\":\"System.out\"}"}], "input_token_count": 230, "output_token_count": 35, "latency": 1.084467887878418}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}, {"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}, {"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 177, "output_token_count": 91, "latency": 1.4831905364990234}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\":\"['/path/to/classes', '60']\",\"out\":\"logStream\"}"}], "input_token_count": 222, "output_token_count": 32, "latency": 0.8960580825805664}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\":\"['-v', '--no-strict']\",\"out\":\"debugOutput\"}"}], "input_token_count": 197, "output_token_count": 30, "latency": 1.0819101333618164}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\":\"EVENT_THREAD\",\"property\":\"deathEvent001\"}"}], "input_token_count": 166, "output_token_count": 29, "latency": 1.0955827236175537}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\":\"mainThread\",\"testedClass\":\"com.example.MainClass\",\"suspendPolicy\":\"EventRequest.SUSPEND_ALL\",\"property\":\"testProperty\"}"}], "input_token_count": 237, "output_token_count": 45, "latency": 0.8914811611175537}
{"id": "java_89", "result": [{"runThis": "{\"argv\":\"-waitTime, 2, -debuggeeName, TestDebuggee\",\"out\":\"testLogStream\"}"}], "input_token_count": 216, "output_token_count": 34, "latency": 1.0902156829833984}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\":\"['-v', '-p']\",\"out\":\"System.out\"}"}], "input_token_count": 189, "output_token_count": 28, "latency": 0.9927904605865479}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\":\"['suspend', 'log']\",\"out\":\"debugLog\"}"}], "input_token_count": 187, "output_token_count": 30, "latency": 1.1253941059112549}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes\"}"}, {"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/home/<USER>/java/libs\"}"}], "input_token_count": 169, "output_token_count": 75, "latency": 0.9747912883758545}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\":\"trace log\",\"inst\":\"appInstrumentation\"}"}], "input_token_count": 179, "output_token_count": 24, "latency": 0.7076427936553955}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\":\"getVersion\"}"}], "input_token_count": 152, "output_token_count": 19, "latency": 0.8601932525634766}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\":\"ExportInlinedObject1Instance\",\"argument\":\"HelloWorld\",\"node\":\"InlinableNodeInstance\",\"library\":\"NodeLibraryInstance\"}"}], "input_token_count": 243, "output_token_count": 41, "latency": 1.0099577903747559}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\":\"convertValue\",\"frameParameterName\":\"frameVar\",\"returnName\":\"returnValueCode\"}"}], "input_token_count": 214, "output_token_count": 35, "latency": 1.0070133209228516}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\":\"NodeClass\",\"inlined\":\"false\"}"}], "input_token_count": 163, "output_token_count": 26, "latency": 0.8149900436401367}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\":\"150\",\"falseCountLocal\":\"50\"}"}], "input_token_count": 154, "output_token_count": 27, "latency": 0.8433551788330078}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\":\"myFactory\",\"delegate\":\"existingDelegate\"}"}], "input_token_count": 176, "output_token_count": 22, "latency": 1.1090574264526367}
