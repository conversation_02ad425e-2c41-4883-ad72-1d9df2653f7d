{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\":\"mapController\",\"parent\":\"mapArea\"}"}], "input_token_count": 427, "output_token_count": 36, "latency": 1.6048290729522705}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\":\"Customers\",\"useShortName\":\"true\",\"params\":\"{limit=50, schema=public}\"}"}], "input_token_count": 476, "output_token_count": 52, "latency": 1.8824784755706787}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\":\"dbMonitor\",\"view\":\"EmployeeView\",\"source\":\"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 490, "output_token_count": 54, "latency": 1.9353227615356445}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\":\"dbMonitor\",\"dataSource\":\"db2DataSource\",\"reference\":\"USERSPACE1\"}"}], "input_token_count": 500, "output_token_count": 51, "latency": 1.9079341888427734}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\":\"jdbcSession\",\"db2ViewBase\":\"EmployeeView in schema HR\"}"}], "input_token_count": 440, "output_token_count": 47, "latency": 1.951345443725586}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\":\"dataController\",\"parent\":\"compositeParent\"}"}], "input_token_count": 449, "output_token_count": 37, "latency": 1.6447076797485352}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\":\"true\",\"append\":\"true\",\"keepState\":\"true\"}"}], "input_token_count": 461, "output_token_count": 42, "latency": 1.7822065353393555}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\":\"/backup/data.txt\",\"force\":\"true\",\"monitor\":\"progressTracker\"}"}], "input_token_count": 504, "output_token_count": 46, "latency": 1.8947746753692627}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\":\"fileStream\",\"force\":\"true\",\"keepHistory\":\"false\",\"monitor\":\"progressMonitor\"}"}], "input_token_count": 540, "output_token_count": 52, "latency": 2.098374128341675}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\":\"new MultiPoint(Arrays.asList(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10)))\",\"xyzmMode\":\"XyzmMode.XYZ\",\"buffer\":\"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 555, "output_token_count": 97, "latency": 2.3348402976989746}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\":\"/usr/local/bin/dbeaver\",\"name\":\"DBeaverLauncher\"}"}], "input_token_count": 404, "output_token_count": 44, "latency": 1.6633410453796387}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\":\"HKEY_LOCAL_MACHINE\",\"property\":\"EnableExtensions\"}"}], "input_token_count": 440, "output_token_count": 42, "latency": 1.657155990600586}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\":\"progressMonitor\",\"schemaName\":\"AnalyticsDB\"}"}], "input_token_count": 429, "output_token_count": 41, "latency": 2.219919443130493}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\":\"JDBC_session\",\"owner\":\"JohnDoe\"}"}], "input_token_count": 434, "output_token_count": 40, "latency": 2.3218438625335693}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\":\"gameLayout\",\"success\":\"true\"}"}], "input_token_count": 421, "output_token_count": 36, "latency": 5.728336334228516}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\":\"imageInputStream\",\"out\":\"imageOutputStream\"}"}], "input_token_count": 439, "output_token_count": 40, "latency": 2.006765127182007}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\":\"instructionData\",\"isRange\":\"true\"}"}], "input_token_count": 424, "output_token_count": 39, "latency": 7.5899598598480225}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\":\"initMethod\",\"insn\":\"newConstructorInsn\"}"}], "input_token_count": 438, "output_token_count": 80, "latency": 2.4029605388641357}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\":\"3\",\"pageSize\":\"20\"}"}], "input_token_count": 435, "output_token_count": 39, "latency": 1.8482606410980225}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\":\"webModel\",\"request\":\"userRequest\"}"}], "input_token_count": 440, "output_token_count": 36, "latency": 1.6077687740325928}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\":\"user-mapping.yml\",\"config\":\"newMappingConfig\"}"}], "input_token_count": 434, "output_token_count": 40, "latency": 1.644866704940796}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\":\"nettyChannelContext\",\"e\":\"ioExceptionEvent\"}"}], "input_token_count": 437, "output_token_count": 39, "latency": 1.8176765441894531}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\":\"[101, 202, 303]\",\"newStatus\":\"2\"}"}], "input_token_count": 435, "output_token_count": 45, "latency": 1.7278099060058594}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\":\"LED TV\",\"recommendStatus\":\"1\",\"pageSize\":\"20\",\"pageNum\":\"3\"}"}], "input_token_count": 522, "output_token_count": 54, "latency": 2.026644706726074}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\":\"[101, 102, 103]\",\"showStatus\":\"0\"}"}], "input_token_count": 442, "output_token_count": 45, "latency": 1.8610503673553467}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\":\"42\",\"sort\":\"5\"}"}], "input_token_count": 412, "output_token_count": 37, "latency": 1.654508352279663}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\":\"CALL totalSales(?)\",\"resultSetType\":\"ResultSet.TYPE_SCROLL_INSENSITIVE\",\"concurrency\":\"ResultSet.CONCUR_READ_ONLY\",\"holdability\":\"ResultSet.CLOSE_CURSORS_AT_COMMIT\"}"}], "input_token_count": 571, "output_token_count": 112, "latency": 2.5239908695220947}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\":\"[2, 7, 11, 15]\",\"target\":\"9\"}"}], "input_token_count": 427, "output_token_count": 43, "latency": 1.5451524257659912}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\":\"es_credentials.properties\",\"credentialsRefreshInterval\":\"30\",\"basicCredentials\":\"basicAuthCredentials\"}"}], "input_token_count": 479, "output_token_count": 51, "latency": 2.181734085083008}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\":\"zipkin.collector.activemq.concurrency\",\"value\":\"10\",\"builderExtractor\":\"ActiveMQCollector.Builder::getConcurrency\"}"}], "input_token_count": 476, "output_token_count": 56, "latency": 1.9040825366973877}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\":\"answer\",\"value\":\"42\"}"}], "input_token_count": 454, "output_token_count": 37, "latency": 1.5564723014831543}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\":\"taskQueue\",\"codec\":\"jsonCodec\"}"}], "input_token_count": 407, "output_token_count": 37, "latency": 1.711205005645752}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\":\"5\",\"leaseTime\":\"120\",\"unit\":\"SECONDS\"}"}], "input_token_count": 494, "output_token_count": 48, "latency": 1.8129749298095703}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\":\"employee:1234\",\"value\":\"John Doe\"}"}], "input_token_count": 423, "output_token_count": 68, "latency": 1.8989884853363037}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\":\"cleanupTask\",\"delay\":\"300\",\"unit\":\"TimeUnit.SECONDS\"}"}], "input_token_count": 472, "output_token_count": 64, "latency": 1.8671722412109375}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\":\"BitOperation.AND\",\"destination\":\"user:online:both\",\"keys\":\"user:online:today,user:online:yesterday\"}"}], "input_token_count": 523, "output_token_count": 56, "latency": 2.0772511959075928}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\":\"['userID', 42, 'username', 'johndoe', 'isActive', true]\",\"state\":\"processingState\"}"}], "input_token_count": 451, "output_token_count": 55, "latency": 1.8210842609405518}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\":\"jenkinsBuild\",\"text\":\"buildOutput\"}"}], "input_token_count": 418, "output_token_count": 64, "latency": 1.****************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\":\"{'name': '', 'address': ''}\"}"}], "input_token_count": 391, "output_token_count": 95, "latency": 2.***************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\":\"logEvent\",\"toAppendTo\":\"logBuilder\"}"}], "input_token_count": 428, "output_token_count": 38, "latency": 1.****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\":\"shardA\",\"initializedShard\":\"shardB\"}"}], "input_token_count": 421, "output_token_count": 42, "latency": 8.***************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\":\"searchHitParser\"}"}], "input_token_count": 366, "output_token_count": 64, "latency": 2.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\":\"usernameField\",\"value\":\"JohnDoe\",\"caseInsensitive\":\"true\"}"}], "input_token_count": 463, "output_token_count": 75, "latency": 2.****************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\":\"mockSettings\",\"handler\":\"mockHandler\",\"object\":\"testObject\"}"}], "input_token_count": 475, "output_token_count": 44, "latency": 1.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\":\"DESede\",\"mode\":\"CBC\",\"padding\":\"PKCS5Padding\"}"}], "input_token_count": 469, "output_token_count": 44, "latency": 1.809936285018921}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\":\"envVariables\",\"size\":\"5\"}"}], "input_token_count": 407, "output_token_count": 72, "latency": 2.240220546722412}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\":\"csmInstance\",\"expected\":\"MyExpectedClass.class\"}"}], "input_token_count": 450, "output_token_count": 72, "latency": 1.9894874095916748}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\":\"CERTIFICATE\",\"value\":\"MIIFdTCCBF2gAwIBAgISESG...\"}"}], "input_token_count": 446, "output_token_count": 98, "latency": 2.4479496479034424}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\":\"new InetSocketAddress(\\\"************\\\", 8080)\",\"sslContext\":\"testSSLContext\"}"}], "input_token_count": 477, "output_token_count": 96, "latency": 2.4068546295166016}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\":\"404\",\"responseLength\":\"1500\"}"}], "input_token_count": 445, "output_token_count": 41, "latency": 1.644174575805664}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\":\"deleteQueryRequest\",\"responseListener\":\"testListener\"}"}], "input_token_count": 460, "output_token_count": 45, "latency": 1.8081276416778564}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\":\"gather_ccr_usage_stats\",\"request\":\"usageRequest\",\"state\":\"clusterState\",\"listener\":\"actionListener\"}"}], "input_token_count": 537, "output_token_count": 86, "latency": 2.4137468338012695}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\":\"SAMLAssertionNode\",\"node_type\":\"Element.class\"}"}], "input_token_count": 431, "output_token_count": 75, "latency": 2.****************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\":\"42\",\"localAcceptedVersion\":\"7\"}"}], "input_token_count": 431, "output_token_count": 43, "latency": 1.****************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\":\"snapshotRequest\",\"shardRouting\":\"shardRouteInfo\",\"task\":\"snapshotTask\",\"listener\":\"operationListener\"}"}], "input_token_count": 541, "output_token_count": 57, "latency": 3.****************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\":\"repositoriesService\",\"cache\":\"cacheService\",\"indexSettings\":\"indexSettingsForLogs\",\"shardPath\":\"/data/nodes/0/indices/logs/5\",\"currentTimeNanosSupplier\":\"currentTimeNanos\",\"threadPool\":\"threadPool\",\"blobStoreCacheService\":\"blobStoreCacheService\",\"sharedBlobCacheService\":\"sharedBlobCacheService\"}"}], "input_token_count": 790, "output_token_count": 108, "latency": 2.****************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\":\"httpResponseEntity\",\"entityParser\":\"responseParser\",\"parserConfig\":\"defaultParserConfig\"}"}], "input_token_count": 488, "output_token_count": 49, "latency": 1.****************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\":\"yes\",\"defaultValue\":\"false\"}"}], "input_token_count": 438, "output_token_count": 94, "latency": 2.1346800327301025}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\":\"{name=John Doe, age=30, email=<EMAIL>}\",\"ensureNoSelfReferences\":\"true\",\"writeStartAndEndHeaders\":\"true\"}"}], "input_token_count": 516, "output_token_count": 62, "latency": 2.108942747116089}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\":\"terminal\",\"shardPath\":\"/var/data/elasticsearch/nodes/0/indices/1shard\",\"indexDirectory\":\"/var/data/elasticsearch/nodes/0/indices/1shard/index\"}"}], "input_token_count": 555, "output_token_count": 69, "latency": 2.059762954711914}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\":\"mainSearchContext\",\"innerHitsContext\":\"hitsContext\"}"}], "input_token_count": 460, "output_token_count": 68, "latency": 2.0917670726776123}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\":\"timestamp\",\"origin\":\"now\",\"scale\":\"10d\",\"offset\":\"2d\",\"decay\":\"0.5\"}"}], "input_token_count": 577, "output_token_count": 60, "latency": 2.1205670833587646}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\":\"temperature\",\"queryType\":\"FLOAT\",\"from\":\"20.5\",\"to\":\"30.0\",\"includeFrom\":\"true\",\"includeTo\":\"false\"}"}], "input_token_count": 616, "output_token_count": 64, "latency": 2.156848907470703}
{"id": "java_63", "result": [{"withinQuery": "{\"field\":\"age\",\"from\":\"30\",\"to\":\"40\",\"includeFrom\":\"true\",\"includeTo\":\"false\"}"}], "input_token_count": 555, "output_token_count": 52, "latency": 1.836876630783081}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\":\"timestamp\",\"factory\":\"dateFactory\",\"script\":\"dateScript\",\"meta\":\"{format: epoch_millis}\",\"onScriptError\":\"FAIL\"}"}], "input_token_count": 573, "output_token_count": 63, "latency": 2.171008348464966}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\":\"xContentBuilderInstance\",\"params\":\"[true, true]\"}"}], "input_token_count": 452, "output_token_count": 41, "latency": 1.702073335647583}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\":\"mappingParserContext\",\"parent\":\"compositeField1\",\"parentScriptFactory\":\"compositeScriptFactory\",\"onScriptError\":\"onScriptError.IGNORE\"}"}], "input_token_count": 542, "output_token_count": 64, "latency": 2.254423141479492}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\":\"/Applications/PhotoEditor.app\",\"params\":\"{appName=PhotoEditor, backgroundImage=true}\"}"}], "input_token_count": 459, "output_token_count": 95, "latency": 2.1945431232452393}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\":\"{appImagePath: '/Applications/MyApp.app', appName: 'MyApp'}\"}"}], "input_token_count": 405, "output_token_count": 95, "latency": 2.39214825630188}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\":\"durations\",\"start\":\"2\",\"end\":\"5\"}"}], "input_token_count": 502, "output_token_count": 80, "latency": 2.27374005317688}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\":\"new QName('http://www.example.com', 'item', 'ex')\",\"augs\":\"augmentations\"}"}], "input_token_count": 463, "output_token_count": 52, "latency": 3.6127991676330566}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\":\"resultData\",\"thisCoroutine\":\"5\",\"toCoroutine\":\"10\"}"}], "input_token_count": 495, "output_token_count": 72, "latency": 2.268578290939331}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\":\"textBuffer\",\"start\":\"5\",\"length\":\"10\"}"}], "input_token_count": 497, "output_token_count": 105, "latency": 3.042093276977539}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\":\"UTF-8\",\"allowJavaNames\":\"true\"}"}], "input_token_count": 410, "output_token_count": 39, "latency": 1.5510737895965576}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\":\"55357\",\"low\":\"56832\",\"inContent\":\"true\"}"}], "input_token_count": 475, "output_token_count": 45, "latency": 1.7076656818389893}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\":\"XML_SECURITY\",\"sysPropertyName\":\"enableXmlSecurityFeature\"}"}], "input_token_count": 430, "output_token_count": 86, "latency": 2.1844751834869385}
{"id": "java_76", "result": [{"Intro_step": "{\"w\":\"800\",\"h\":\"600\"}"}], "input_token_count": 396, "output_token_count": 32, "latency": 1.5726606845855713}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"password\":\"P@ssw0rd!\"}"}], "input_token_count": 439, "output_token_count": 76, "latency": 2.0874974727630615}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\":\"quiet\",\"otherDependents\":\"verbose\"}"}], "input_token_count": 427, "output_token_count": 83, "latency": 2.4381158351898193}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\":\"1234\",\"sysId\":\"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 451, "output_token_count": 46, "latency": 1.7764134407043457}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\":\"failOn\",\"ruleIdx\":\"42\"}"}], "input_token_count": 421, "output_token_count": 37, "latency": 1.6079444885253906}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\":\"humongous-test-case.json\",\"doGC\":\"customGarbageCollector\",\"checker\":\"referenceChecker\",\"gcLogName\":\"gc-analysis.log\",\"shouldContain\":\"[\\\"GC pause\\\"]\",\"shouldNotContain\":\"[\\\"OutOfMemoryError\\\"]\"}"}], "input_token_count": 702, "output_token_count": 88, "latency": 7.004316091537476}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\":\"testArgs\",\"out\":\"System.out\"}"}], "input_token_count": 464, "output_token_count": 37, "latency": 1.5229816436767578}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\":\"-waitTime, 120000, -iterations, 500\",\"out\":\"System.out\"}"}], "input_token_count": 491, "output_token_count": 92, "latency": 2.1661200523376465}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\":\"myRedefClass\",\"expValue\":\"100\"}"}], "input_token_count": 441, "output_token_count": 39, "latency": 1.7692463397979736}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\":\"['/path/to/classes', '60']\",\"out\":\"logStream\"}"}], "input_token_count": 484, "output_token_count": 92, "latency": 2.057074785232544}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\":\"[-v, --no-strict]\",\"out\":\"debugOutput\"}"}], "input_token_count": 459, "output_token_count": 42, "latency": 1.6394808292388916}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\":\"EVENT_THREAD\",\"property\":\"deathEvent001\"}"}], "input_token_count": 427, "output_token_count": 41, "latency": 1.8061954975128174}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\":\"mainThread\",\"testedClass\":\"com.example.MainClass\",\"suspendPolicy\":\"EventRequest.SUSPEND_ALL\",\"property\":\"testProperty\"}"}], "input_token_count": 536, "output_token_count": 61, "latency": 1.9593160152435303}
{"id": "java_89", "result": [{"runThis": "{\"argv\":\"-waitTime, 120000, -debuggeeName, TestDebuggee\",\"out\":\"testLogStream\"}"}], "input_token_count": 477, "output_token_count": 48, "latency": 1.7423181533813477}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\":\"[-v, -p]\",\"out\":\"System.out\"}"}], "input_token_count": 451, "output_token_count": 40, "latency": 1.6259171962738037}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\":\"[suspend, log]\",\"out\":\"debugLog\"}"}], "input_token_count": 447, "output_token_count": 40, "latency": 1.9170513153076172}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\":\"com.example.MyClass\",\"classPath\":\"/usr/local/classes:/home/<USER>/java/libs\"}"}], "input_token_count": 432, "output_token_count": 47, "latency": 1.711200475692749}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\":\"trace log\",\"inst\":\"appInstrumentation\"}"}], "input_token_count": 438, "output_token_count": 38, "latency": 1.7359004020690918}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\":\"getVersion\"}"}], "input_token_count": 408, "output_token_count": 31, "latency": 1.9685542583465576}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\":\"ExportInlinedObject1Instance\",\"argument\":\"HelloWorld\",\"node\":\"InlinableNodeInstance\",\"library\":\"NodeLibraryInstance\"}"}], "input_token_count": 542, "output_token_count": 59, "latency": 2.0064635276794434}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\":\"convertValue\",\"frameParameterName\":\"frameVar\",\"returnName\":\"returnValueCode\"}"}], "input_token_count": 499, "output_token_count": 50, "latency": 1.9160411357879639}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\":\"NodeClass\",\"inlined\":\"false\"}"}], "input_token_count": 421, "output_token_count": 40, "latency": 1.617058515548706}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\":\"150\",\"falseCountLocal\":\"50\"}"}], "input_token_count": 420, "output_token_count": 40, "latency": 1.9261484146118164}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\":\"myFactory\",\"delegate\":\"existingDelegate\"}"}], "input_token_count": 437, "output_token_count": 69, "latency": 2.272934675216675}
