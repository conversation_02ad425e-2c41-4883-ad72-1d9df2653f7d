{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 284, "output_token_count": 151, "latency": 0.7193145751953125}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"Customers\", \"useShortName\": \"true\", \"params\": {\"limit\": \"50\", \"schema\": \"public\"}}"}], "input_token_count": 329, "output_token_count": 335, "latency": 1.574922800064087}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"view\": \"EmployeeView\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 342, "output_token_count": 204, "latency": 0.97283935546875}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\": \"dbMonitor\", \"dataSource\": \"db2DataSource\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 349, "output_token_count": 223, "latency": 1.064023494720459}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\": \"jdbcSession\", \"db2ViewBase\": \"EmployeeView\"}"}], "input_token_count": 297, "output_token_count": 273, "latency": 1.302992820739746}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 305, "output_token_count": 439, "latency": 2.0702171325683594}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\": \"true\", \"append\": \"true\", \"keepState\": \"true\"}"}], "input_token_count": 316, "output_token_count": 174, "latency": 0.832007884979248}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 357, "output_token_count": 242, "latency": 1.1533846855163574}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\": \"fileStream\", \"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\"}"}], "input_token_count": 385, "output_token_count": 289, "latency": 1.3724422454833984}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\": \"((1,2), (3,4), (5,6), (7,8), (9,10))\", \"xyzmMode\": \"XyzmMode.XYZ\", \"buffer\": \"allocate(1024)\"}"}], "input_token_count": 407, "output_token_count": 305, "latency": 1.4470312595367432}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 263, "output_token_count": 200, "latency": 0.9531114101409912}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\": \"HKEY_LOCAL_MACHINE\", \"property\": \"EnableExtensions\"}"}], "input_token_count": 298, "output_token_count": 197, "latency": 0.9405827522277832}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 286, "output_token_count": 179, "latency": 0.8526186943054199}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}], "input_token_count": 294, "output_token_count": 453, "latency": 2.1356422901153564}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 279, "output_token_count": 299, "latency": 1.4111015796661377}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 298, "output_token_count": 148, "latency": 0.7106313705444336}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 282, "output_token_count": 192, "latency": 0.9215908050537109}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 296, "output_token_count": 244, "latency": 1.1517586708068848}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 293, "output_token_count": 207, "latency": 0.983468770980835}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest\"}"}], "input_token_count": 295, "output_token_count": 155, "latency": 0.7384200096130371}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\": \"user-mapping.yml\", \"config\": \"newMappingConfig\"}"}], "input_token_count": 291, "output_token_count": 234, "latency": 1.1089370250701904}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 293, "output_token_count": 321, "latency": 1.5149919986724854}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"101,202,303\", \"newStatus\": \"2\"}"}], "input_token_count": 297, "output_token_count": 242, "latency": 1.1755919456481934}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\": \"LED TV\", \"recommendStatus\": 1, \"pageSize\": \"20\", \"pageNum\": 3}"}], "input_token_count": 364, "output_token_count": 258, "latency": 1.2850620746612549}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"101,102,103\", \"showStatus\": \"0\"}"}], "input_token_count": 305, "output_token_count": 240, "latency": 1.1977674961090088}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 272, "output_token_count": 278, "latency": 1.3805243968963623}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"TYPE_SCROLL_INSENSITIVE\", \"concurrency\": \"CONCUR_READ_ONLY\", \"holdability\": \"HOLD_CURSORS_OVER_COMMIT\"}"}], "input_token_count": 408, "output_token_count": 246, "latency": 1.2232263088226318}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"2,7,11,15\", \"target\": \"9\"}"}], "input_token_count": 287, "output_token_count": 300, "latency": 1.4884722232818604}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\", \"basicCredentials\": \"basicAuthCredentials\"}"}], "input_token_count": 333, "output_token_count": 954, "latency": 4.684459686279297}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\", \"builderExtractor\": \"concurrency\"}"}], "input_token_count": 330, "output_token_count": 443, "latency": 2.183228015899658}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 311, "output_token_count": 240, "latency": 1.1902670860290527}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 266, "output_token_count": 179, "latency": 0.8921451568603516}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\": \"5\", \"leaseTime\": \"2\", \"unit\": \"SECONDS\"}"}], "input_token_count": 347, "output_token_count": 324, "latency": 1.597564458847046}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 284, "output_token_count": 178, "latency": 0.8827378749847412}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\": \"cleanupTask\", \"delay\": \"300\", \"unit\": \"SECONDS\"}"}], "input_token_count": 324, "output_token_count": 199, "latency": 0.9921209812164307}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\": \"AND\", \"destination\": \"user:online:both\", \"keys\": \"user:online:today,user:online:yesterday\"}"}], "input_token_count": 376, "output_token_count": 314, "latency": 1.5449326038360596}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": \"userID,42,username,johndoe,isActive,true\", \"state\": \"processingState\"}"}], "input_token_count": 310, "output_token_count": 551, "latency": 2.672492504119873}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 279, "output_token_count": 168, "latency": 0.***************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"{'name': 'name', 'address': 'address'}\"}"}], "input_token_count": 254, "output_token_count": 277, "latency": 1.****************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"LogEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 282, "output_token_count": 161, "latency": 0.***************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\": \"shardA\", \"initializedShard\": \"shardB\"}"}], "input_token_count": 285, "output_token_count": 225, "latency": 1.****************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser\"}"}], "input_token_count": 230, "output_token_count": 178, "latency": 0.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\": \"usernameField\", \"value\": \"JohnDoe\", \"caseInsensitive\": \"true\"}"}], "input_token_count": 316, "output_token_count": 199, "latency": 0.****************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 328, "output_token_count": 168, "latency": 0.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 323, "output_token_count": 214, "latency": 1.0408658981323242}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": 5}"}], "input_token_count": 264, "output_token_count": 265, "latency": 1.2841694355010986}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 310, "output_token_count": 264, "latency": 1.2787611484527588}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESG\"}"}], "input_token_count": 310, "output_token_count": 427, "latency": 2.0640082359313965}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"192.168.1.10:8080\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 347, "output_token_count": 294, "latency": 1.4305047988891602}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 307, "output_token_count": 300, "latency": 1.454967737197876}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"deleteQueryRequest\", \"responseListener\": \"testListener\"}"}], "input_token_count": 319, "output_token_count": 338, "latency": 1.6436526775360107}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\": \"master_operation\", \"request\": \"usage_request\", \"state\": \"cluster_state\", \"listener\": \"action_listener\"}"}], "input_token_count": 383, "output_token_count": 271, "latency": 1.3194200992584229}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"SAMLAssertionNode\", \"node_type\": \"Element\"}"}], "input_token_count": 292, "output_token_count": 293, "latency": 1.****************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 295, "output_token_count": 209, "latency": 1.****************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\", \"listener\": \"operationListener\"}"}], "input_token_count": 390, "output_token_count": 211, "latency": 1.****************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\": \"daily-snapshots\", \"cache\": \"cacheService\", \"indexSettings\": \"logs\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"threadPool\": \"\", \"blobStoreCacheService\": \"blobStoreCacheService\", \"sharedBlobCacheService\": \"sharedBlobCacheService\"}"}], "input_token_count": 611, "output_token_count": 1625, "latency": 7.***************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 341, "output_token_count": 326, "latency": 1.***************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\": \"yes\", \"defaultValue\": \"false\"}"}], "input_token_count": 296, "output_token_count": 256, "latency": 1.2439684867858887}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\": \"userProfile\", \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 367, "output_token_count": 1486, "latency": 7.1522276401519775}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\": \"TerminalInterface\", \"shardPath\": \"/var/data/elasticsearch/nodes/0/indices/1shard\", \"indexDirectory\": \"/var/data/elasticsearch/nodes/0/indices/1shard/index\"}"}], "input_token_count": 411, "output_token_count": 211, "latency": 1.0274181365966797}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\", \"nestedPath\": \"user.address\"}"}], "input_token_count": 317, "output_token_count": 369, "latency": 1.7919020652770996}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\": \"timestamp\", \"origin\": \"now\", \"scale\": \"10d\", \"decay\": \"0.5\"}"}], "input_token_count": 422, "output_token_count": 323, "latency": 1.5647706985473633}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"queryType\": \"FLOAT\", \"from\": \"20.5\", \"to\": \"30.0\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 453, "output_token_count": 317, "latency": 1.5435287952423096}
{"id": "java_63", "result": [{"withinQuery": "{\"field\": \"age\", \"from\": \"30\", \"to\": \"40\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 399, "output_token_count": 369, "latency": 1.7857000827789307}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": {\"format\": \"epoch_millis\"}, \"onScriptError\": \"FAIL\"}"}], "input_token_count": 410, "output_token_count": 281, "latency": 1.366866111755371}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\": \"your_xcontent_builder_instance\", \"params\": \"includeDefaults=true&skipRuntimeFields=true\"}"}], "input_token_count": 309, "output_token_count": 382, "latency": 1.8528046607971191}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"IGNORE\"}"}], "input_token_count": 385, "output_token_count": 221, "latency": 1.0815823078155518}
{"id": "java_67", "result": [], "input_token_count": 315, "output_token_count": 1047, "latency": 5.047953128814697}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": {\"applicationImagePath\": \"/Applications/MyApp.app\", \"applicationName\": \"MyApp\"}}"}], "input_token_count": 270, "output_token_count": 267, "latency": 1.2944416999816895}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"start\": \"2\", \"end\": \"5\"}"}], "input_token_count": 352, "output_token_count": 408, "latency": 1.974498987197876}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\": \"{namespaceURI='http://www.example.com', localPart='item', prefix='ex'}\", \"augs\": \"augmentations\"}"}], "input_token_count": 321, "output_token_count": 201, "latency": 0.9804258346557617}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 348, "output_token_count": 324, "latency": 1.5695462226867676}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}], "input_token_count": 351, "output_token_count": 299, "latency": 1.4490771293640137}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\": \"UTF-8\", \"allowJavaNames\": \"true\"}"}], "input_token_count": 269, "output_token_count": 181, "latency": 0.8833236694335938}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"false\"}"}], "input_token_count": 333, "output_token_count": 260, "latency": 1.263401746749878}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 285, "output_token_count": 371, "latency": 1.796175241470337}
{"id": "java_76", "result": [{"Intro_step": "{\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 258, "output_token_count": 224, "latency": 1.089076280593872}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 322, "output_token_count": 355, "latency": 1.7197721004486084}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"output-format\", \"otherDependents\": [\"quiet\", \"verbose\"]}"}], "input_token_count": 287, "output_token_count": 248, "latency": 1.2028834819793701}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 313, "output_token_count": 237, "latency": 1.157010793685913}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 281, "output_token_count": 378, "latency": 1.8275165557861328}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\": \"humongous-test-case.json\", \"doGC\": \"java.util.concurrent.Executors.newFixedThreadPool(1).boxed()\", \"checker\": \"java.util.function.Consumer<java.lang.Object> { }\", \"gcLogName\": \"gc-analysis.log\", \"shouldContain\": [\"GC pause\"], \"shouldNotContain\": [\"OutOfMemoryError\"]}"}], "input_token_count": 534, "output_token_count": 633, "latency": 3.057145357131958}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"[\\\"createTestObject\\\", \\\"invokeTestMethod\\\", \\\"logToSystemOut\\\"]\", \"out\": \"java.util.logging.Logger.getPlatformLogger()\"}"}], "input_token_count": 324, "output_token_count": 306, "latency": 1.4822731018066406}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": \"-wait 120 -iterations 500\", \"out\": \"System.out\"}"}], "input_token_count": 354, "output_token_count": 304, "latency": 1.4763200283050537}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 301, "output_token_count": 274, "latency": 1.3388946056365967}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"/path/to/classes,60\", \"out\": \"logStream\"}"}], "input_token_count": 351, "output_token_count": 293, "latency": 1.431051254272461}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"-v,--no-strict\", \"out\": \"debugOutput\"}"}], "input_token_count": 319, "output_token_count": 156, "latency": 0.7745997905731201}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"EVENT_THREAD\", \"property\": \"testProperty\"}"}], "input_token_count": 288, "output_token_count": 184, "latency": 0.9077613353729248}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\": \"mainThread\", \"testedClass\": \"com.example.MainClass\", \"suspendPolicy\": \"EventRequest.SUSPEND_ALL\", \"property\": \"testProperty\"}"}], "input_token_count": 385, "output_token_count": 286, "latency": 1.4029836654663086}
{"id": "java_89", "result": [], "input_token_count": 335, "output_token_count": 422, "latency": 2.0479140281677246}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": [\"-v\", \"-p\"], \"out\": \"System.out\"}"}], "input_token_count": 311, "output_token_count": 344, "latency": 1.6876320838928223}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": [\"suspend\", \"log\"], \"out\": \"debugLog\"}"}], "input_token_count": 307, "output_token_count": 274, "latency": 1.3365979194641113}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes/home/<USER>/java/libs\"}"}], "input_token_count": 290, "output_token_count": 288, "latency": 1.4040465354919434}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 299, "output_token_count": 383, "latency": 1.8628559112548828}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\"}"}], "input_token_count": 268, "output_token_count": 223, "latency": 1.0903778076171875}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\": \"ExportInlinedObject1Instance\", \"argument\": \"HelloWorld\", \"node\": \"InlinableNodeInstance\", \"library\": \"NodeLibraryInstance\"}"}], "input_token_count": 389, "output_token_count": 199, "latency": 0.9722850322723389}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 350, "output_token_count": 268, "latency": 1.298887014389038}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 286, "output_token_count": 232, "latency": 1.1286401748657227}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 281, "output_token_count": 378, "latency": 1.8239076137542725}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 295, "output_token_count": 226, "latency": 1.0976362228393555}
