{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\": \"mapController\", \"parent\": \"mapArea\"}"}], "input_token_count": 284, "output_token_count": 240, "latency": 6.080912351608276}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\": \"\\\"Customers\\\"\", \"useShortName\": \"true\", \"params\": \"{\\\"limit\\\": \\\"50\\\", \\\"schemaFilter\\\": \\\"public\\\"}\"}"}], "input_token_count": 329, "output_token_count": 340, "latency": 8.619966268539429}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\": \"dbMonitor\", \"view\": \"EmployeeView\", \"source\": \"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 342, "output_token_count": 423, "latency": 10.712582349777222}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\": \"dbMonitor\", \"dataSource\": \"db2DataSource\", \"reference\": \"USERSPACE1\"}"}], "input_token_count": 349, "output_token_count": 234, "latency": 5.950998544692993}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\": \"jdbcSession\", \"db2ViewBase\": \"DB2ViewBase(viewName='EmployeeView', schema='HR')\"}"}], "input_token_count": 297, "output_token_count": 491, "latency": 12.418731212615967}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\": \"dataController\", \"parent\": \"compositeParent\"}"}], "input_token_count": 305, "output_token_count": 280, "latency": 7.101728677749634}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\": \"true\", \"append\": \"true\", \"keepState\": \"true\"}"}], "input_token_count": 316, "output_token_count": 205, "latency": 5.2114622592926025}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\": \"/backup/data.txt\", \"force\": \"true\", \"monitor\": \"progressTracker\"}"}], "input_token_count": 357, "output_token_count": 468, "latency": 11.862582206726074}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\": \"fileStream\", \"force\": \"true\", \"keepHistory\": \"false\", \"monitor\": \"progressMonitor\"}"}], "input_token_count": 385, "output_token_count": 328, "latency": 8.368293523788452}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\": \"new MultiPoint(Arrays.asList(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10)))\", \"xyzmMode\": \"XyzmMode.XYZ\", \"buffer\": \"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 407, "output_token_count": 535, "latency": 13.612792491912842}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\": \"/usr/local/bin/dbeaver\", \"name\": \"DBeaverLauncher\"}"}], "input_token_count": 263, "output_token_count": 159, "latency": 4.0486109256744385}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\": \"HKEY_LOCAL_MACHINE\", \"property\": \"EnableExtensions\"}"}], "input_token_count": 298, "output_token_count": 264, "latency": 6.710691452026367}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\": \"progressMonitor\", \"schemaName\": \"AnalyticsDB\"}"}], "input_token_count": 286, "output_token_count": 212, "latency": 5.393189430236816}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\": \"JDBC_session\", \"owner\": \"JohnDoe\"}"}], "input_token_count": 294, "output_token_count": 175, "latency": 4.453958034515381}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\": \"gameLayout\", \"success\": \"true\"}"}], "input_token_count": 279, "output_token_count": 267, "latency": 6.792503833770752}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\": \"imageInputStream\", \"out\": \"imageOutputStream\"}"}], "input_token_count": 298, "output_token_count": 151, "latency": 3.8488106727600098}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\": \"instructionData\", \"isRange\": \"true\"}"}], "input_token_count": 282, "output_token_count": 447, "latency": 11.298151731491089}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\": \"initMethod\", \"insn\": \"newConstructorInsn\"}"}], "input_token_count": 296, "output_token_count": 318, "latency": 8.03922152519226}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\": \"3\", \"pageSize\": \"20\"}"}], "input_token_count": 293, "output_token_count": 181, "latency": 4.5901734828948975}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\": \"webModel\", \"request\": \"userRequest\"}"}], "input_token_count": 295, "output_token_count": 234, "latency": 5.923379898071289}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\": \"user-mapping.yml\", \"config\": \"newMappingConfig\"}"}], "input_token_count": 291, "output_token_count": 322, "latency": 8.14013123512268}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\": \"nettyChannelContext\", \"e\": \"ioExceptionEvent\"}"}], "input_token_count": 293, "output_token_count": 565, "latency": 14.286955118179321}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\": \"[101, 202, 303]\", \"newStatus\": \"2\"}"}], "input_token_count": 297, "output_token_count": 291, "latency": 7.383227348327637}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\": \"'%LED TV%'\", \"recommendStatus\": \"1\", \"pageSize\": \"20\", \"pageNum\": \"3\"}"}], "input_token_count": 364, "output_token_count": 274, "latency": 6.976595878601074}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\": \"[101, 102, 103]\", \"showStatus\": \"0\"}"}], "input_token_count": 305, "output_token_count": 347, "latency": 8.810205936431885}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\": \"42\", \"sort\": \"5\"}"}], "input_token_count": 272, "output_token_count": 180, "latency": 4.574308395385742}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\": \"CALL totalSales(?)\", \"resultSetType\": \"1001\", \"concurrency\": \"1007\", \"holdability\": \"1005\"}"}], "input_token_count": 408, "output_token_count": 351, "latency": 8.915597915649414}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\": \"[2, 7, 11, 15]\", \"target\": \"9\"}"}], "input_token_count": 287, "output_token_count": 408, "latency": 10.336771965026855}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\": \"es_credentials.properties\", \"credentialsRefreshInterval\": \"30\", \"basicCredentials\": \"basicAuthCredentials\"}"}], "input_token_count": 333, "output_token_count": 490, "latency": 12.43648648262024}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\": \"zipkin.collector.activemq.concurrency\", \"value\": \"10\", \"builderExtractor\": \"builder.getConcurrency()\"}"}], "input_token_count": 330, "output_token_count": 455, "latency": 11.536359786987305}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\": \"answer\", \"value\": \"42\"}"}], "input_token_count": 311, "output_token_count": 251, "latency": 6.367810964584351}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\": \"taskQueue\", \"codec\": \"jsonCodec\"}"}], "input_token_count": 266, "output_token_count": 236, "latency": 5.967077732086182}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\": \"5\", \"leaseTime\": \"120\", \"unit\": \"SECONDS\"}"}], "input_token_count": 347, "output_token_count": 307, "latency": 7.7712273597717285}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\": \"employee:1234\", \"value\": \"John Doe\"}"}], "input_token_count": 284, "output_token_count": 163, "latency": 4.134366273880005}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\": \"cleanupTask\", \"delay\": \"300\", \"unit\": \"TimeUnit.SECONDS\"}"}], "input_token_count": 324, "output_token_count": 229, "latency": 5.806620836257935}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\": \"BitOperation.AND\", \"destination\": \"user:online:both\", \"keys\": [\"user:online:today\", \"user:online:yesterday\"]}"}], "input_token_count": 376, "output_token_count": 370, "latency": 9.365373373031616}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\": \"['userID', 42, 'username', 'johndoe', 'isActive', true]\", \"state\": \"processingState\"}"}], "input_token_count": 310, "output_token_count": 368, "latency": 9.306207656860352}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\": \"jenkinsBuild\", \"text\": \"buildOutput\"}"}], "input_token_count": 279, "output_token_count": 206, "latency": 5.***************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\": \"docFields\"}"}], "input_token_count": 254, "output_token_count": 449, "latency": 11.***************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\": \"logEvent\", \"toAppendTo\": \"logBuilder\"}"}], "input_token_count": 282, "output_token_count": 394, "latency": 10.***************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\": \"shardA\", \"initializedShard\": \"shardB\"}"}], "input_token_count": 285, "output_token_count": 231, "latency": 5.****************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\": \"searchHitParser\"}"}], "input_token_count": 230, "output_token_count": 335, "latency": 8.***************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\": \"usernameField\", \"value\": \"JohnDoe\", \"caseInsensitive\": \"true\"}"}], "input_token_count": 316, "output_token_count": 255, "latency": 6.***************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\": \"mockSettings\", \"handler\": \"mockHandler\", \"object\": \"testObject\"}"}], "input_token_count": 328, "output_token_count": 181, "latency": 4.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\": \"DESede\", \"mode\": \"CBC\", \"padding\": \"PKCS5Padding\"}"}], "input_token_count": 323, "output_token_count": 168, "latency": 4.283083915710449}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\": \"envVariables\", \"size\": \"5\"}"}], "input_token_count": 264, "output_token_count": 412, "latency": 10.419934034347534}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\": \"csmInstance\", \"expected\": \"MyExpectedClass.class\"}"}], "input_token_count": 310, "output_token_count": 315, "latency": 8.014783382415771}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\": \"CERTIFICATE\", \"value\": \"MIIFdTCCBF2gAwIBAgISESG...[truncated for brevity]...\"}"}], "input_token_count": 310, "output_token_count": 391, "latency": 9.909424066543579}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\": \"new InetSocketAddress(\\\"************\\\", 8080)\", \"sslContext\": \"testSSLContext\"}"}], "input_token_count": 347, "output_token_count": 262, "latency": 6.678233623504639}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\": \"404\", \"responseLength\": \"1500\"}"}], "input_token_count": 307, "output_token_count": 297, "latency": 7.541542053222656}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\": \"\\\"deleteQueryRequest\\\"\", \"responseListener\": \"\\\"testListener\\\"\"}"}], "input_token_count": 319, "output_token_count": 266, "latency": 6.760226488113403}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\": \"task\", \"request\": \"usageRequest\", \"state\": \"clusterState\", \"listener\": \"actionListener\"}"}], "input_token_count": 383, "output_token_count": 838, "latency": 21.25594401359558}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\": \"SAMLAssertionNode\", \"node_type\": \"org.w3c.dom.Element\"}"}], "input_token_count": 292, "output_token_count": 618, "latency": 15.***************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\": \"42\", \"localAcceptedVersion\": \"7\"}"}], "input_token_count": 295, "output_token_count": 162, "latency": 4.***************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\": \"snapshotRequest\", \"shardRouting\": \"shardRouteInfo\", \"task\": \"snapshotTask\", \"listener\": \"operationListener\"}"}], "input_token_count": 390, "output_token_count": 373, "latency": 9.***************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\": \"repositoriesService\", \"cache\": \"cacheService\", \"indexSettings\": \"indexSettingsForLogs\", \"shardPath\": \"/data/nodes/0/indices/logs/5\", \"currentTimeNanosSupplier\": \"currentTimeNanos\", \"threadPool\": \"threadPool\", \"blobStoreCacheService\": \"blobStoreCacheService\", \"sharedBlobCacheService\": \"sharedBlobCacheService\"}"}], "input_token_count": 611, "output_token_count": 568, "latency": 14.**************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\": \"httpResponseEntity\", \"entityParser\": \"responseParser\", \"parserConfig\": \"defaultParserConfig\"}"}], "input_token_count": 341, "output_token_count": 388, "latency": 9.***************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\": \"yes\", \"defaultValue\": \"false\"}"}], "input_token_count": 296, "output_token_count": 328, "latency": 8.32874345779419}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\": \"new java.util.HashMap<>(java.util.Map.of(\\\"name\\\", \\\"John Doe\\\", \\\"age\\\", 30, \\\"email\\\", \\\"<EMAIL>\\\"))\", \"ensureNoSelfReferences\": \"true\", \"writeStartAndEndHeaders\": \"true\"}"}], "input_token_count": 367, "output_token_count": 290, "latency": 7.387290716171265}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\": \"System.console()\", \"shardPath\": \"/var/data/elasticsearch/nodes/0/indices/1shard\", \"indexDirectory\": \"/var/data/elasticsearch/nodes/0/indices/1shard/index\"}"}], "input_token_count": 411, "output_token_count": 419, "latency": 10.6456778049469}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\": \"mainSearchContext\", \"innerHitsContext\": \"hitsContext\"}"}], "input_token_count": 317, "output_token_count": 743, "latency": 18.842185258865356}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\": \"timestamp\", \"origin\": \"now\", \"scale\": \"10d\", \"offset\": \"2d\", \"decay\": \"0.5\"}"}], "input_token_count": 422, "output_token_count": 422, "latency": 10.742584228515625}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\": \"temperature\", \"queryType\": \"FLOAT\", \"from\": \"20.5\", \"to\": \"30.0\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 453, "output_token_count": 333, "latency": 8.505861043930054}
{"id": "java_63", "result": [{"withinQuery": "{\"field\": \"age\", \"from\": \"30\", \"to\": \"40\", \"includeFrom\": \"true\", \"includeTo\": \"false\"}"}], "input_token_count": 399, "output_token_count": 257, "latency": 6.576395511627197}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\": \"timestamp\", \"factory\": \"dateFactory\", \"script\": \"dateScript\", \"meta\": \"{\\\"format\\\": \\\"epoch_millis\\\"}\", \"onScriptError\": \"FAIL\"}"}], "input_token_count": 410, "output_token_count": 377, "latency": 9.591059684753418}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\": \"xContentBuilderInstance\", \"params\": \"[true, true]\"}"}], "input_token_count": 309, "output_token_count": 687, "latency": 17.44983410835266}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\": \"mappingParserContext\", \"parent\": \"compositeField1\", \"parentScriptFactory\": \"compositeScriptFactory\", \"onScriptError\": \"onScriptError.IGNORE\"}"}], "input_token_count": 385, "output_token_count": 386, "latency": 9.838088750839233}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\": \"/Applications/PhotoEditor.app\", \"params\": \"{\\\"appName\\\":\\\"PhotoEditor\\\",\\\"imagesRoot\\\":\\\"/Volumes/PhotoEditor/Contents/Resources\\\",\\\"backgroundImageFolder\\\":\\\"/Volumes/PhotoEditor/Contents/Resources/backgrounds\\\"}\"}"}], "input_token_count": 315, "output_token_count": 1241, "latency": 31.523193836212158}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\": \"{\\\"appImageDir\\\":\\\"/Applications/MyApp.app\\\",\\\"appName\\\":\\\"MyApp\\\"}\"}"}], "input_token_count": 270, "output_token_count": 447, "latency": 11.350301265716553}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\": \"durations\", \"start\": \"2\", \"end\": \"5\"}"}], "input_token_count": 352, "output_token_count": 338, "latency": 8.615844488143921}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\": \"javax.xml.namespace.QName.<init>(\\\"http://www.example.com\\\", \\\"item\\\", \\\"ex\\\")\", \"augs\": \"augmentations\"}"}], "input_token_count": 321, "output_token_count": 884, "latency": 22.48472023010254}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\": \"resultData\", \"thisCoroutine\": \"5\", \"toCoroutine\": \"10\"}"}], "input_token_count": 348, "output_token_count": 287, "latency": 7.3053810596466064}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\": \"textBuffer\", \"start\": \"5\", \"length\": \"10\"}"}], "input_token_count": 351, "output_token_count": 474, "latency": 12.03216814994812}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\": \"UTF-8\", \"allowJavaNames\": \"true\"}"}], "input_token_count": 269, "output_token_count": 255, "latency": 6.499720811843872}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\": \"55357\", \"low\": \"56832\", \"inContent\": \"false\"}"}], "input_token_count": 333, "output_token_count": 357, "latency": 9.082690954208374}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\": \"XML_SECURITY\", \"sysPropertyName\": \"enableXmlSecurityFeature\"}"}], "input_token_count": 285, "output_token_count": 420, "latency": 10.653096437454224}
{"id": "java_76", "result": [{"Intro_step": "{\"w\": \"800\", \"h\": \"600\"}"}], "input_token_count": 258, "output_token_count": 239, "latency": 6.08455753326416}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", \"password\": \"P@ssw0rd!\"}"}], "input_token_count": 322, "output_token_count": 508, "latency": 12.874342918395996}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"quiet\", \"otherDependents\": [\"verbose\"]}"}], "input_token_count": 287, "output_token_count": 489, "latency": 12.4428391456604}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\": \"1234\", \"sysId\": \"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 313, "output_token_count": 632, "latency": 16.041609525680542}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\": \"failOn\", \"ruleIdx\": \"42\"}"}], "input_token_count": 281, "output_token_count": 300, "latency": 7.6410133838653564}
{"id": "java_81", "result": [], "input_token_count": 534, "output_token_count": 464, "latency": 11.873064994812012}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\": \"new String[] {\\\"testArgs\\\"}\", \"out\": \"System.out\"}"}], "input_token_count": 324, "output_token_count": 654, "latency": 16.59886908531189}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\": [\"-waitTime\", \"120\", \"-iterations\", \"500\"], \"out\": \"System.out\"}"}], "input_token_count": 354, "output_token_count": 731, "latency": 18.568920135498047}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 301, "output_token_count": 356, "latency": 9.02733826637268}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\": \"[\\\"/path/to/classes\\\", \\\"60\\\"]\", \"out\": \"logStream\"}"}], "input_token_count": 351, "output_token_count": 632, "latency": 16.009140253067017}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\": \"['-v', '--no-strict']\", \"out\": \"debugOutput\"}"}], "input_token_count": 319, "output_token_count": 1468, "latency": 37.24473214149475}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\": \"2\", \"property\": \"testProperty=deathEvent001\"}"}], "input_token_count": 288, "output_token_count": 338, "latency": 8.560553789138794}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\": \"mainThread\", \"testedClass\": \"com.example.MainClass\", \"suspendPolicy\": \"2\", \"property\": \"testProperty\"}"}], "input_token_count": 385, "output_token_count": 422, "latency": 10.713899850845337}
{"id": "java_89", "result": [{"runThis": "{\"argv\": [\"-waitTime\", \"120\", \"-debuggeeName\", \"TestDebuggee\"], \"out\": \"testLogStream\"}"}], "input_token_count": 335, "output_token_count": 518, "latency": 13.144434213638306}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\": \"['-v', '-p']\", \"out\": \"System.out\"}"}], "input_token_count": 311, "output_token_count": 334, "latency": 8.471551895141602}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\": \"[\\\"suspend\\\", \\\"log\\\"]\", \"out\": \"debugLog\"}"}], "input_token_count": 307, "output_token_count": 280, "latency": 7.117490530014038}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes:/home/<USER>/java/libs\"}"}], "input_token_count": 290, "output_token_count": 384, "latency": 9.725307703018188}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\": \"trace log\", \"inst\": \"appInstrumentation\"}"}], "input_token_count": 299, "output_token_count": 216, "latency": 5.500582933425903}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\": \"getVersion\", \"recursive\": \"null\"}"}], "input_token_count": 268, "output_token_count": 301, "latency": 7.665258407592773}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\": \"ExportInlinedObject1Instance\", \"argument\": \"HelloWorld\", \"node\": \"InlinableNodeInstance\", \"library\": \"NodeLibraryInstance\"}"}], "input_token_count": 389, "output_token_count": 325, "latency": 8.268211841583252}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\": \"convertValue\", \"frameParameterName\": \"frameVar\", \"returnName\": \"returnValueCode\"}"}], "input_token_count": 350, "output_token_count": 400, "latency": 10.169815063476562}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\": \"NodeClass\", \"inlined\": \"false\"}"}], "input_token_count": 286, "output_token_count": 251, "latency": 6.372381210327148}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\": \"150\", \"falseCountLocal\": \"50\"}"}], "input_token_count": 281, "output_token_count": 375, "latency": 9.519270658493042}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\": \"myFactory\", \"delegate\": \"existingDelegate\"}"}], "input_token_count": 295, "output_token_count": 306, "latency": 7.78524374961853}
