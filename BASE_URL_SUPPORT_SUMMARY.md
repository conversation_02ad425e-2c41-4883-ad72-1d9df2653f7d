# Base URL 参数支持实现总结

## 概述

本次更新为 Berkeley Function Call Leaderboard 添加了对 `base_url` 参数的全面支持，允许用户为各个大模型的客户端指定自定义的 API 端点。这对于使用代理服务器、私有部署或自定义 API 网关的场景非常有用。

## 实现的更改

### 1. 命令行接口更新

#### 新的 bfcl CLI (`bfcl/__main__.py`)
添加了新的命令行参数：
- `--base-url`: 自定义模型 API 端点的基础 URL，默认值为 None

```bash
python -m bfcl generate \
    --model gpt-4o-mini-2024-07-18-FC \
    --temperature 0.7 \
    --top-k 50 \
    --top-p 0.9 \
    --base-url "https://custom-openai-endpoint.com/v1"
```

#### 传统 openfunctions_evaluation.py 脚本 (`bfcl/_llm_response_generation.py`)
同样添加了对应的命令行参数：
- `--base-url`: 自定义模型 API 端点的基础 URL
- `--top-k`: Top-k 参数
- `--top-p`: Top-p 参数

```bash
python openfunctions_evaluation.py \
    --model claude-3-5-sonnet-20241022-FC \
    --temperature 0.7 \
    --top-k 50 \
    --top-p 0.9 \
    --base-url "https://api.anthropic.com/v1/"
```

### 2. 基础处理器更新 (`bfcl/model_handler/base_handler.py`)

- 更新了 `BaseHandler.__init__()` 方法以接受 `base_url` 参数
- 所有模型处理器现在都继承这个参数

### 3. 构建函数更新 (`bfcl/_llm_response_generation.py`)

- 更新了 `build_handler()` 函数以传递 `base_url` 参数
- 更新了 `generate_results()` 函数以使用新参数

### 4. 模型处理器更新

#### API 模型处理器

**OpenAI 处理器** (`bfcl/model_handler/api_inference/openai.py`)
- 支持自定义 base_url 参数
- 如果未提供 base_url，使用默认的 OpenAI API 端点

**Claude 处理器** (`bfcl/model_handler/api_inference/claude.py`)
- 支持自定义 base_url 参数
- 如果未提供 base_url，使用默认的 Anthropic API 端点

**Cohere 处理器** (`bfcl/model_handler/api_inference/cohere.py`)
- 支持自定义 base_url 参数
- 如果未提供 base_url，使用默认的 Cohere API 端点

**Gemini 处理器** (`bfcl/model_handler/api_inference/gemini.py`)
- 构造函数签名已更新以包含 base_url 参数
- 注意：Vertex AI 不支持自定义 base_url，参数被忽略

**Amazon Nova 处理器** (`bfcl/model_handler/api_inference/nova.py`)
- 构造函数签名已更新以包含 base_url 参数
- 注意：AWS Bedrock 不支持自定义 base_url，参数被忽略

**其他 API 处理器**
- Fireworks (`fireworks.py`) - 支持自定义 base_url
- Novita (`novita.py`) - 支持自定义 base_url
- Functionary (`functionary.py`) - 支持自定义 base_url
- DeepSeek (`deepseek.py`) - 支持自定义 base_url
- Databricks (`databricks.py`) - 支持自定义 base_url
- Mining (`mining.py`) - 支持自定义 base_url
- Nvidia (`nvidia.py`) - 支持自定义 base_url
- Yi (`yi.py`) - 支持自定义 base_url

#### 本地模型处理器

**基础 OSS 处理器** (`bfcl/model_handler/local_inference/base_oss_handler.py`)
- 更新了构造函数以接受 base_url 参数
- 如果提供了自定义 base_url，使用它；否则使用默认的 vLLM 端点

**具体本地模型处理器**
- 所有继承自 OSSHandler 的处理器都已更新构造函数签名
- 包括 Hermes、GLM、MiniCPM FC、Salesforce Llama、Salesforce Qwen 等

## 使用示例

### 基本用法

#### 使用新的 bfcl CLI

```bash
# 使用默认端点
python -m bfcl generate --model gpt-4o-mini-2024-07-18-FC

# 使用自定义端点
python -m bfcl generate \
    --model gpt-4o-mini-2024-07-18-FC \
    --base-url "https://my-proxy.example.com/v1"

# 使用自定义端点和其他参数
python -m bfcl generate \
    --model claude-3-5-sonnet-20241022-FC \
    --temperature 0.8 \
    --top-k 50 \
    --top-p 0.9 \
    --base-url "https://my-anthropic-proxy.example.com"
```

#### 使用传统 openfunctions_evaluation.py 脚本

```bash
# 使用默认端点
python openfunctions_evaluation.py --model gpt-4o-mini-2024-07-18-FC

# 使用自定义端点
python openfunctions_evaluation.py \
    --model gpt-4o-mini-2024-07-18-FC \
    --base-url "https://my-proxy.example.com/v1"

# 使用自定义端点和其他参数
python openfunctions_evaluation.py \
    --model claude-3-5-sonnet-20241022-FC \
    --temperature 0.8 \
    --top-k 50 \
    --top-p 0.9 \
    --base-url "https://my-anthropic-proxy.example.com"
```

### 编程接口

```python
from bfcl._llm_response_generation import build_handler

# 创建带有自定义 base_url 的处理器
handler = build_handler(
    model_name="gpt-4o-mini-2024-07-18-FC",
    temperature=0.7,
    top_k=50,
    top_p=0.9,
    base_url="https://custom-endpoint.com/v1"
)
```

## 支持的模型

### 完全支持自定义 base_url
- **OpenAI 兼容模型**: 所有使用 OpenAI 客户端的模型
- **Claude 模型**: 通过 Anthropic 客户端
- **Cohere 模型**: 通过 Cohere 客户端
- **本地模型**: 通过 vLLM/SGLang 部署的开源模型

### 不支持自定义 base_url（参数被忽略）
- **Gemini 模型**: 使用 Vertex AI，不支持自定义端点
- **Amazon Nova 模型**: 使用 AWS Bedrock，不支持自定义端点

## 向后兼容性

所有更改都保持了向后兼容性：
- 现有代码无需修改即可继续工作
- base_url 参数是可选的，默认值为 None
- 未提供 base_url 时，使用各模型的默认端点

## 测试验证

- 创建了测试脚本验证 base_url 参数的正确传递
- 验证了命令行接口的正确性
- 确认了向后兼容性

## 使用场景

1. **代理服务器**: 通过代理访问 API 服务
2. **私有部署**: 使用自己部署的模型服务
3. **API 网关**: 通过统一的 API 网关访问不同的模型
4. **本地开发**: 使用本地模拟的 API 端点进行开发和测试

---

此实现为用户提供了灵活的端点配置选项，同时保持了系统的易用性和向后兼容性。
